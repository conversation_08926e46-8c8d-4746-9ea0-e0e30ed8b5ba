import '../models/models.dart';
import 'database_service.dart';
import 'package:sqflite/sqflite.dart';

/// Сервіс для роботи з графіком змін
class ShiftScheduleService {
  final DatabaseService _databaseService = DatabaseService();

  // CRUD операції для змін

  /// Додавання нової зміни
  Future<void> insertShift(Shift shift) async {
    final db = await _databaseService.database;
    await db.insert(
      'shifts',
      shift.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення інформації про зміну
  Future<void> updateShift(Shift shift) async {
    final db = await _databaseService.database;
    await db.update(
      'shifts',
      shift.toMap(),
      where: 'id = ?',
      whereArgs: [shift.id],
    );
  }

  /// Видалення зміни
  Future<void> deleteShift(String id) async {
    final db = await _databaseService.database;
    await db.delete('shifts', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання зміни за ідентифікатором
  Future<Shift?> getShift(String id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'shifts',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return Shift.fromMap(maps.first);
  }

  /// Отримання всіх змін
  Future<List<Shift>> getAllShifts() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('shifts');
    return List.generate(maps.length, (i) => Shift.fromMap(maps[i]));
  }

  // CRUD операції для графіків змін

  /// Додавання нового графіку змін
  Future<void> insertShiftSchedule(ShiftSchedule schedule) async {
    final db = await _databaseService.database;
    await db.insert(
      'shiftSchedules',
      schedule.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення інформації про графік змін
  Future<void> updateShiftSchedule(ShiftSchedule schedule) async {
    final db = await _databaseService.database;
    await db.update(
      'shiftSchedules',
      schedule.toMap(),
      where: 'id = ?',
      whereArgs: [schedule.id],
    );
  }

  /// Видалення графіку змін
  Future<void> deleteShiftSchedule(String id) async {
    final db = await _databaseService.database;
    await db.delete('shiftSchedules', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання графіку змін за ідентифікатором
  Future<ShiftSchedule?> getShiftSchedule(String id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'shiftSchedules',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return ShiftSchedule.fromMap(maps.first);
  }

  /// Отримання всіх графіків змін
  Future<List<ShiftSchedule>> getAllShiftSchedules() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('shiftSchedules');
    return List.generate(maps.length, (i) => ShiftSchedule.fromMap(maps[i]));
  }

  // Операції для зв'язку працівників з графіками змін

  /// Призначення графіку змін працівнику
  Future<void> assignShiftScheduleToEmployee(
    String employeeId,
    String scheduleId,
  ) async {
    final db = await _databaseService.database;

    // Спочатку видаляємо існуючі зв'язки для цього працівника
    await db.delete(
      'employeeShiftSchedules',
      where: 'employeeId = ?',
      whereArgs: [employeeId],
    );

    // Створюємо новий зв'язок
    final employeeShiftSchedule = EmployeeShiftSchedule(
      employeeId: employeeId,
      shiftScheduleId: scheduleId,
    );

    await db.insert(
      'employeeShiftSchedules',
      employeeShiftSchedule.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Видалення призначення графіку змін для працівника
  Future<void> removeShiftScheduleFromEmployee(String employeeId) async {
    final db = await _databaseService.database;
    await db.delete(
      'employeeShiftSchedules',
      where: 'employeeId = ?',
      whereArgs: [employeeId],
    );
  }

  /// Отримання графіку змін для працівника
  Future<ShiftSchedule?> getEmployeeShiftSchedule(String employeeId) async {
    final db = await _databaseService.database;

    // Отримуємо зв'язок
    final List<Map<String, dynamic>> maps = await db.query(
      'employeeShiftSchedules',
      where: 'employeeId = ?',
      whereArgs: [employeeId],
    );

    if (maps.isEmpty) return null;

    final String scheduleId = maps.first['shiftScheduleId'];

    // Отримуємо графік змін
    return await getShiftSchedule(scheduleId);
  }

  /// Отримання зміни для вказаної дати та працівника
  Future<Shift?> getShiftForDate(String employeeId, DateTime date) async {
    // Отримуємо графік змін працівника
    final schedule = await getEmployeeShiftSchedule(employeeId);
    if (schedule == null) return null;

    // Отримуємо ID зміни для вказаної дати
    final shiftId = schedule.getShiftIdForDate(date);
    if (shiftId == null) return null;

    // Отримуємо зміну
    return await getShift(shiftId);
  }

  /// Генерація робочих днів на основі графіку змін
  Future<List<WorkDay>> generateWorkDaysFromSchedule(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<WorkDay> workDays = [];

    // Отримуємо графік змін працівника
    final schedule = await getEmployeeShiftSchedule(employeeId);
    if (schedule == null) return workDays;

    // Отримуємо всі зміни
    final shifts = await getAllShifts();
    final Map<String, Shift> shiftsMap = {
      for (var shift in shifts) shift.id: shift,
    };

    // Для кожного дня в періоді
    for (
      DateTime date = startDate;
      date.isBefore(endDate.add(const Duration(days: 1)));
      date = date.add(const Duration(days: 1))
    ) {
      // Перевіряємо, чи є день вихідним
      if (schedule.isDayOff(date)) continue;

      // Отримуємо ID зміни для вказаної дати
      final shiftId = schedule.getShiftIdForDate(date);
      if (shiftId == null) continue;

      // Отримуємо зміну
      final shift = shiftsMap[shiftId];
      if (shift == null) continue;

      // Визначаємо тип дня
      final dayOfWeek = DayOfWeek.fromDateTime(date);
      DayType dayType;

      if (dayOfWeek == DayOfWeek.saturday) {
        dayType = DayType.saturday;
      } else if (dayOfWeek == DayOfWeek.sunday) {
        dayType = DayType.sunday;
      } else {
        dayType = DayType.regular;
      }

      // Створюємо робочий день
      final workDay = WorkDay(
        employeeId: employeeId,
        date: date,
        hoursWorked: shift.durationInHours,
        dayType: dayType,
        shiftType: shift.isNightShift ? ShiftType.night : ShiftType.day,
        shiftId: shift.id,
      );

      workDays.add(workDay);
    }

    return workDays;
  }
}
