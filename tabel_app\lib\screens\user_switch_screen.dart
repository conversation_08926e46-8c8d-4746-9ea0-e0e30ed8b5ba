import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import 'screens.dart';

/// Екран для переключення між користувачами
class UserSwitchScreen extends StatefulWidget {
  const UserSwitchScreen({super.key});

  @override
  State<UserSwitchScreen> createState() => _UserSwitchScreenState();
}

class _UserSwitchScreenState extends State<UserSwitchScreen> {
  bool _isLoading = true;
  List<Employee> _allEmployees = [];
  List<Employee> _recentUsers = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final authService = Provider.of<AuthService>(context, listen: false);
      
      // Завантажуємо всіх працівників та недавніх користувачів
      final allEmployees = await authService.getAllEmployeesForSwitch();
      final recentUsers = await authService.getRecentUsers();

      setState(() {
        _allEmployees = allEmployees;
        _recentUsers = recentUsers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка завантаження: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// Переключення на вибраного користувача
  Future<void> _switchToUser(Employee employee) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authService = Provider.of<AuthService>(context, listen: false);
      final appState = Provider.of<AppState>(context, listen: false);
      
      // Переключаємося на вибраного користувача
      await authService.switchToUser(employee);
      
      // Оновлюємо стан додатку
      appState.setCurrentEmployee(employee);

      if (!mounted) return;

      // Повертаємося на головний екран
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const HomeScreen()),
        (route) => false,
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка переключення: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// Створення карточки користувача
  Widget _buildUserCard(Employee employee, {bool isRecent = false}) {
    final localizations = AppLocalizations.of(context);
    final appState = Provider.of<AppState>(context);
    final currentEmployee = appState.currentEmployee;
    final isCurrentUser = currentEmployee?.id == employee.id;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isCurrentUser ? Colors.blue : Colors.grey,
          child: Text(
            employee.firstName.isNotEmpty ? employee.firstName[0].toUpperCase() : '?',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          employee.fullName,
          style: TextStyle(
            fontWeight: isCurrentUser ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(employee.email),
            if (employee.position.isNotEmpty)
              Text(
                employee.position,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isRecent)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  localizations.translate('recent'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            if (isCurrentUser)
              Icon(
                Icons.check_circle,
                color: Colors.green,
              )
            else
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
          ],
        ),
        onTap: isCurrentUser ? null : () => _switchToUser(employee),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('switch_user')),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red[300],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: Text(localizations.translate('retry')),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Недавні користувачі
                      if (_recentUsers.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            localizations.translate('recent_users'),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        ..._recentUsers.map((user) => _buildUserCard(user, isRecent: true)),
                        const Divider(height: 32),
                      ],

                      // Всі користувачі
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          localizations.translate('all_users'),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      
                      if (_allEmployees.isEmpty)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(32),
                            child: Text(
                              localizations.translate('no_users_found'),
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        )
                      else
                        ..._allEmployees.map((user) => _buildUserCard(user)),
                      
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
          );
        },
        icon: const Icon(Icons.person_add),
        label: Text(localizations.translate('add_user')),
      ),
    );
  }
}
