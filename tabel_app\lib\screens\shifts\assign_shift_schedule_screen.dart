import 'package:flutter/material.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import '../../widgets/widgets.dart';
import 'shift_schedules_list_screen.dart';
import 'shift_schedule_edit_screen.dart';

/// Екран призначення графіку змін працівнику
class AssignShiftScheduleScreen extends StatefulWidget {
  /// Ідентифікатор працівника
  final String employeeId;

  /// Конструктор
  const AssignShiftScheduleScreen({Key? key, required this.employeeId})
    : super(key: key);

  @override
  State<AssignShiftScheduleScreen> createState() =>
      _AssignShiftScheduleScreenState();
}

class _AssignShiftScheduleScreenState extends State<AssignShiftScheduleScreen> {
  final ShiftScheduleService _shiftScheduleService = ShiftScheduleService();
  final DatabaseService _databaseService = DatabaseService();

  List<ShiftSchedule> _schedules = [];
  Map<String, Shift> _shiftsMap = {};
  ShiftSchedule? _selectedSchedule;
  Employee? _employee;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Завантаження працівника
      _employee = await _databaseService.getEmployee(widget.employeeId);

      // Завантаження графіків змін
      _schedules = await _shiftScheduleService.getAllShiftSchedules();

      // Завантаження поточного графіку змін працівника
      _selectedSchedule = await _shiftScheduleService.getEmployeeShiftSchedule(
        widget.employeeId,
      );

      // Завантаження змін
      final shifts = await _shiftScheduleService.getAllShifts();
      _shiftsMap = {for (var shift in shifts) shift.id: shift};
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка завантаження даних: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Призначення графіку змін працівнику
  Future<void> _assignSchedule(String? scheduleId) async {
    if (scheduleId == null) {
      // Видалення призначення
      try {
        await _shiftScheduleService.removeShiftScheduleFromEmployee(
          widget.employeeId,
        );

        setState(() {
          _selectedSchedule = null;
        });

        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Графік змін видалено')));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Помилка видалення графіку змін: $e')),
          );
        }
      }
    } else {
      // Призначення графіку
      try {
        await _shiftScheduleService.assignShiftScheduleToEmployee(
          widget.employeeId,
          scheduleId,
        );

        // Оновлення вибраного графіку
        _selectedSchedule = _schedules.firstWhere(
          (schedule) => schedule.id == scheduleId,
        );

        setState(() {});

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Графік змін призначено')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Помилка призначення графіку змін: $e')),
          );
        }
      }
    }
  }

  /// Отримання назви зміни за ідентифікатором
  String _getShiftName(String shiftId) {
    return _shiftsMap[shiftId]?.name ?? 'Невідома зміна';
  }

  /// Отримання кольору зміни за ідентифікатором
  Color _getShiftColor(String shiftId) {
    return _shiftsMap[shiftId]?.color ?? Colors.grey;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Призначення графіку змін')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _employee == null
              ? const Center(child: Text('Працівника не знайдено'))
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Працівник: ${_employee!.fullName}',
                              style: const TextStyle(
                                fontSize: 18.0,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8.0),
                            Text('Посада: ${_employee!.position}'),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    const Text(
                      'Поточний графік змін:',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    if (_selectedSchedule == null)
                      const Card(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: Text('Графік змін не призначено'),
                          ),
                        ),
                      )
                    else
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _selectedSchedule!.name,
                                style: const TextStyle(
                                  fontSize: 16.0,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8.0),
                              Text(
                                'Поточна зміна: ${_selectedSchedule!.shiftOrder.isNotEmpty ? _getShiftName(_selectedSchedule!.shiftOrder[_selectedSchedule!.currentShiftIndex]) : 'Не вказано'}',
                              ),
                              const SizedBox(height: 8.0),
                              Text(
                                'Вихідні: ${_selectedSchedule!.hasFloatingDaysOff ? 'Плаваючі (${_selectedSchedule!.workDaysCount} роб. / ${_selectedSchedule!.daysOffCount} вих.)' : '${_selectedSchedule!.dayOff1.name}${_selectedSchedule!.dayOff2 != null ? ', ${_selectedSchedule!.dayOff2!.name}' : ''}'}',
                              ),
                              if (_selectedSchedule!.shiftOrder.isNotEmpty) ...[
                                const SizedBox(height: 8.0),
                                const Text(
                                  'Порядок змін:',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 8.0),
                                Wrap(
                                  spacing: 8.0,
                                  runSpacing: 8.0,
                                  children:
                                      _selectedSchedule!.shiftOrder
                                          .map(
                                            (shiftId) => Chip(
                                              backgroundColor: _getShiftColor(
                                                shiftId,
                                              ),
                                              label: Text(
                                                _getShiftName(shiftId),
                                                style: TextStyle(
                                                  color:
                                                      _getShiftColor(
                                                                shiftId,
                                                              ).computeLuminance() >
                                                              0.5
                                                          ? Colors.black
                                                          : Colors.white,
                                                ),
                                              ),
                                            ),
                                          )
                                          .toList(),
                                ),
                              ],
                              const SizedBox(height: 8.0),
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () => _assignSchedule(null),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                      ),
                                      child: const Text('Видалити призначення'),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () async {
                                        await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) =>
                                                    ShiftScheduleEditScreen(
                                                      schedule:
                                                          _selectedSchedule,
                                                    ),
                                          ),
                                        );
                                        _loadData();
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                      ),
                                      child: const Text('Редагувати графік'),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    const SizedBox(height: 16.0),
                    const Text(
                      'Призначити новий графік змін:',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    if (_schedules.isEmpty)
                      Column(
                        children: [
                          const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(
                                child: Text('Немає доступних графіків змін'),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8.0),
                          ElevatedButton(
                            onPressed: () async {
                              await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) =>
                                          const ShiftSchedulesListScreen(),
                                ),
                              );
                              _loadData();
                            },
                            child: const Text('Створити графік змін'),
                          ),
                        ],
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _schedules.length,
                        itemBuilder: (context, index) {
                          final schedule = _schedules[index];
                          final isSelected =
                              _selectedSchedule?.id == schedule.id;

                          return Card(
                            color:
                                isSelected
                                    ? Theme.of(
                                      context,
                                    ).colorScheme.primaryContainer
                                    : null,
                            child: ListTile(
                              title: Text(
                                schedule.name,
                                style: TextStyle(
                                  fontWeight:
                                      isSelected ? FontWeight.bold : null,
                                ),
                              ),
                              subtitle: Text(
                                'Поточна зміна: ${schedule.shiftOrder.isNotEmpty ? _getShiftName(schedule.shiftOrder[schedule.currentShiftIndex]) : 'Не вказано'}',
                              ),
                              trailing:
                                  isSelected
                                      ? const Icon(Icons.check_circle)
                                      : null,
                              onTap: () {
                                if (!isSelected) {
                                  _assignSchedule(schedule.id);
                                }
                              },
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
    );
  }
}
