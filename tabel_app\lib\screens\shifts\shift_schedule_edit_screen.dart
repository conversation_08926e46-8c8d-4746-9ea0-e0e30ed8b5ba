import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import '../../widgets/widgets.dart';
import 'shifts_list_screen.dart';

/// Екран редагування графіку змін
class ShiftScheduleEditScreen extends StatefulWidget {
  /// Графік змін для редагування (null для створення нового)
  final ShiftSchedule? schedule;

  /// Конструктор
  const ShiftScheduleEditScreen({Key? key, this.schedule}) : super(key: key);

  @override
  State<ShiftScheduleEditScreen> createState() =>
      _ShiftScheduleEditScreenState();
}

class _ShiftScheduleEditScreenState extends State<ShiftScheduleEditScreen> {
  final ShiftScheduleService _shiftScheduleService = ShiftScheduleService();
  final _formKey = GlobalKey<FormState>();
  
  late String _name;
  late List<String> _shiftOrder;
  late int _currentShiftIndex;
  late DateTime _startDate;
  late DayOfWeek _dayOff1;
  DayOfWeek? _dayOff2;
  late bool _hasFloatingDaysOff;
  late int _workDaysCount;
  late int _daysOffCount;
  
  List<Shift> _availableShifts = [];
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _availableShifts = await _shiftScheduleService.getAllShifts();
      _initializeFields();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка завантаження даних: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Ініціалізація полів форми
  void _initializeFields() {
    if (widget.schedule != null) {
      _name = widget.schedule!.name;
      _shiftOrder = List.from(widget.schedule!.shiftOrder);
      _currentShiftIndex = widget.schedule!.currentShiftIndex;
      _startDate = widget.schedule!.startDate;
      _dayOff1 = widget.schedule!.dayOff1;
      _dayOff2 = widget.schedule!.dayOff2;
      _hasFloatingDaysOff = widget.schedule!.hasFloatingDaysOff;
      _workDaysCount = widget.schedule!.workDaysCount;
      _daysOffCount = widget.schedule!.daysOffCount;
    } else {
      _name = '';
      _shiftOrder = [];
      _currentShiftIndex = 0;
      _startDate = DateTime.now();
      _dayOff1 = DayOfWeek.saturday;
      _dayOff2 = DayOfWeek.sunday;
      _hasFloatingDaysOff = false;
      _workDaysCount = 5;
      _daysOffCount = 2;
    }
  }

  /// Вибір дати початку графіку
  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
      });
    }
  }

  /// Додавання зміни до порядку змін
  void _addShiftToOrder(String shiftId) {
    setState(() {
      _shiftOrder.add(shiftId);
    });
  }

  /// Видалення зміни з порядку змін
  void _removeShiftFromOrder(int index) {
    setState(() {
      _shiftOrder.removeAt(index);
      if (_currentShiftIndex >= _shiftOrder.length) {
        _currentShiftIndex = _shiftOrder.isEmpty ? 0 : _shiftOrder.length - 1;
      }
    });
  }

  /// Переміщення зміни вгору в порядку змін
  void _moveShiftUp(int index) {
    if (index <= 0) return;
    setState(() {
      final shift = _shiftOrder.removeAt(index);
      _shiftOrder.insert(index - 1, shift);
      if (_currentShiftIndex == index) {
        _currentShiftIndex--;
      } else if (_currentShiftIndex == index - 1) {
        _currentShiftIndex++;
      }
    });
  }

  /// Переміщення зміни вниз в порядку змін
  void _moveShiftDown(int index) {
    if (index >= _shiftOrder.length - 1) return;
    setState(() {
      final shift = _shiftOrder.removeAt(index);
      _shiftOrder.insert(index + 1, shift);
      if (_currentShiftIndex == index) {
        _currentShiftIndex++;
      } else if (_currentShiftIndex == index + 1) {
        _currentShiftIndex--;
      }
    });
  }

  /// Отримання назви зміни за ідентифікатором
  String _getShiftName(String shiftId) {
    return _availableShifts
        .firstWhere(
          (shift) => shift.id == shiftId,
          orElse: () => Shift(
            name: 'Невідома зміна',
            startTime: const TimeOfDay(hour: 0, minute: 0),
            endTime: const TimeOfDay(hour: 0, minute: 0),
            color: Colors.grey,
          ),
        )
        .name;
  }

  /// Отримання кольору зміни за ідентифікатором
  Color _getShiftColor(String shiftId) {
    return _availableShifts
        .firstWhere(
          (shift) => shift.id == shiftId,
          orElse: () => Shift(
            name: 'Невідома зміна',
            startTime: const TimeOfDay(hour: 0, minute: 0),
            endTime: const TimeOfDay(hour: 0, minute: 0),
            color: Colors.grey,
          ),
        )
        .color;
  }

  /// Збереження графіку змін
  Future<void> _saveSchedule() async {
    if (_isSaving) return;
    if (!_formKey.currentState!.validate()) return;
    
    if (_shiftOrder.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Додайте хоча б одну зміну до графіку'),
        ),
      );
      return;
    }
    
    _formKey.currentState!.save();
    
    setState(() {
      _isLoading = true;
      _isSaving = true;
    });
    
    try {
      final schedule = ShiftSchedule(
        id: widget.schedule?.id,
        name: _name,
        shiftOrder: _shiftOrder,
        currentShiftIndex: _currentShiftIndex,
        startDate: _startDate,
        dayOff1: _dayOff1,
        dayOff2: _hasFloatingDaysOff ? null : _dayOff2,
        hasFloatingDaysOff: _hasFloatingDaysOff,
        workDaysCount: _workDaysCount,
        daysOffCount: _daysOffCount,
      );
      
      if (widget.schedule == null) {
        await _shiftScheduleService.insertShiftSchedule(schedule);
      } else {
        await _shiftScheduleService.updateShiftSchedule(schedule);
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Графік змін збережено')),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка збереження графіку змін: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isSaving = false;
        });
      }
    }
  }

  /// Автоматичне заповнення графіка: 5 днів одна зміна, 5 днів інша і т.д.
  void _autoFillWorkWeekShifts() {
    if (_availableShifts.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Потрібно щонайменше 2 зміни для автозаповнення')),
      );
      return;
    }
    setState(() {
      _shiftOrder.clear();
      if (!_hasFloatingDaysOff) {
        // Встановлюємо дату початку на найближчий понеділок
        final now = DateTime.now();
        final int daysToMonday = (DateTime.monday - now.weekday + 7) % 7;
        _startDate = DateTime(now.year, now.month, now.day).add(Duration(days: daysToMonday));
        // Додаємо по 5 днів кожної зміни по черзі (починаючи з понеділка)
        for (int i = 0; i < _availableShifts.length; i++) {
          for (int d = 0; d < 5; d++) {
            _shiftOrder.add(_availableShifts[i].id);
          }
        }
        _workDaysCount = 5;
        _daysOffCount = 2;
        _dayOff1 = DayOfWeek.saturday;
        _dayOff2 = DayOfWeek.sunday;
        _hasFloatingDaysOff = false;
        _currentShiftIndex = 0;
      } else {
        // Для плаваючих вихідних — залишаємо поточну дату та стандартну логіка
        for (int i = 0; i < _availableShifts.length; i++) {
          for (int d = 0; d < 5; d++) {
            _shiftOrder.add(_availableShifts[i].id);
          }
        }
        _workDaysCount = 5;
        _daysOffCount = 2;
        _dayOff1 = DayOfWeek.saturday;
        _dayOff2 = DayOfWeek.sunday;
        _hasFloatingDaysOff = true;
        _currentShiftIndex = 0;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.schedule == null ? 'Новий графік змін' : 'Редагування графіку',
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      initialValue: _name,
                      decoration: const InputDecoration(
                        labelText: 'Назва графіку',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Введіть назву графіку';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _name = value!;
                      },
                    ),
                    const SizedBox(height: 16.0),
                    InkWell(
                      onTap: _selectStartDate,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Дата початку графіку',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          DateFormat('dd.MM.yyyy').format(_startDate),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    const Text(
                      'Порядок змін:',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    if (_shiftOrder.isEmpty)
                      const Card(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: Text('Немає доданих змін'),
                          ),
                        ),
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _shiftOrder.length,
                        itemBuilder: (context, index) {
                          final shiftId = _shiftOrder[index];
                          return Card(
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getShiftColor(shiftId),
                                child: Text(
                                  (index + 1).toString(),
                                  style: TextStyle(
                                    color: _getShiftColor(shiftId)
                                                .computeLuminance() >
                                            0.5
                                        ? Colors.black
                                        : Colors.white,
                                  ),
                                ),
                              ),
                              title: Text(_getShiftName(shiftId)),
                              subtitle: index == _currentShiftIndex
                                  ? const Text(
                                      'Поточна зміна',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )
                                  : null,
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.arrow_upward),
                                    onPressed:
                                        index > 0 ? () => _moveShiftUp(index) : null,
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.arrow_downward),
                                    onPressed: index < _shiftOrder.length - 1
                                        ? () => _moveShiftDown(index)
                                        : null,
                                  ),
                                  Radio<int>(
                                    value: index,
                                    groupValue: _currentShiftIndex,
                                    onChanged: (value) {
                                      setState(() {
                                        _currentShiftIndex = value!;
                                      });
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete),
                                    onPressed: () => _removeShiftFromOrder(index),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    const SizedBox(height: 8.0),
                    if (_availableShifts.isEmpty)
                      ElevatedButton(
                        onPressed: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ShiftsListScreen(),
                            ),
                          );
                          _loadData();
                        },
                        child: const Text('Створити зміни'),
                      )
                    else
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Додати зміну',
                          border: OutlineInputBorder(),
                        ),
                        items: _availableShifts.map((shift) {
                          return DropdownMenuItem<String>(
                            value: shift.id,
                            child: Row(
                              children: [
                                Container(
                                  width: 16.0,
                                  height: 16.0,
                                  decoration: BoxDecoration(
                                    color: shift.color,
                                    borderRadius: BorderRadius.circular(4.0),
                                  ),
                                ),
                                const SizedBox(width: 8.0),
                                Text(shift.name),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            _addShiftToOrder(value);
                          }
                        },
                      ),
                    const SizedBox(height: 16.0),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _autoFillWorkWeekShifts,
                          child: const Text('Автозаповнення: 5/2 (Пн-Пт)'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16.0),
                    const Text(
                      'Налаштування вихідних:',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    SwitchListTile(
                      title: const Text('Плаваючі вихідні'),
                      subtitle: const Text(
                        'Вихідні не прив\'язані до днів тижня',
                      ),
                      value: _hasFloatingDaysOff,
                      onChanged: (value) {
                        setState(() {
                          _hasFloatingDaysOff = value;
                        });
                      },
                    ),
                    if (_hasFloatingDaysOff)
                      Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    initialValue: _workDaysCount.toString(),
                                    decoration: const InputDecoration(
                                      labelText: 'Робочих днів',
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Введіть кількість';
                                      }
                                      final count = int.tryParse(value);
                                      if (count == null || count <= 0) {
                                        return 'Введіть число > 0';
                                      }
                                      return null;
                                    },
                                    onSaved: (value) {
                                      _workDaysCount = int.parse(value!);
                                    },
                                  ),
                                ),
                                const SizedBox(width: 16.0),
                                Expanded(
                                  child: TextFormField(
                                    initialValue: _daysOffCount.toString(),
                                    decoration: const InputDecoration(
                                      labelText: 'Вихідних днів',
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Введіть кількість';
                                      }
                                      final count = int.tryParse(value);
                                      if (count == null || count <= 0) {
                                        return 'Введіть число > 0';
                                      }
                                      return null;
                                    },
                                    onSaved: (value) {
                                      _daysOffCount = int.parse(value!);
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8.0),
                            const Text(
                              'Приклад: 5 робочих / 2 вихідних - стандартний графік 5/2',
                              style: TextStyle(
                                fontStyle: FontStyle.italic,
                                fontSize: 12.0,
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: Column(
                          children: [
                            DropdownButtonFormField<DayOfWeek>(
                              decoration: const InputDecoration(
                                labelText: 'Перший вихідний',
                                border: OutlineInputBorder(),
                              ),
                              value: _dayOff1,
                              items: DayOfWeek.values.map((day) {
                                return DropdownMenuItem<DayOfWeek>(
                                  value: day,
                                  child: Text(day.name),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _dayOff1 = value!;
                                });
                              },
                            ),
                            const SizedBox(height: 16.0),
                            DropdownButtonFormField<DayOfWeek?>(
                              decoration: const InputDecoration(
                                labelText: 'Другий вихідний (опціонально)',
                                border: OutlineInputBorder(),
                              ),
                              value: _dayOff2,
                              items: [
                                const DropdownMenuItem<DayOfWeek?>(
                                  value: null,
                                  child: Text('Немає'),
                                ),
                                ...DayOfWeek.values.map((day) {
                                  return DropdownMenuItem<DayOfWeek?>(
                                    value: day,
                                    child: Text(day.name),
                                  );
                                }).toList(),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _dayOff2 = value;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    const SizedBox(height: 24.0),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSaving ? null : _saveSchedule,
                        child: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          child: Text('Зберегти'),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
