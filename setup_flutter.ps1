# Настройка Flutter для PowerShell
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Настройка Flutter PATH" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Проверяем, где установлен Flutter
$FlutterPath = "C:\flutter\bin"

if (-not (Test-Path "$FlutterPath\flutter.exe")) {
    Write-Host "❌ Flutter не найден в $FlutterPath" -ForegroundColor Red
    Write-Host ""
    $FlutterPath = Read-Host "Введите правильный путь к Flutter bin (например, C:\flutter\bin)"
}

if (-not (Test-Path "$FlutterPath\flutter.exe")) {
    Write-Host "❌ Flutter все еще не найден!" -ForegroundColor Red
    Write-Host "Убедитесь, что Flutter правильно распакован." -ForegroundColor Yellow
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

Write-Host "✅ Flutter найден: $FlutterPath" -ForegroundColor Green
Write-Host ""

# Получаем текущий PATH
$CurrentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# Проверяем, есть ли уже Flutter в PATH
if ($CurrentPath -notlike "*$FlutterPath*") {
    Write-Host "Добавляем Flutter в PATH пользователя..." -ForegroundColor Yellow
    
    # Добавляем Flutter в PATH пользователя
    $NewPath = "$FlutterPath;$CurrentPath"
    [Environment]::SetEnvironmentVariable("PATH", $NewPath, "User")
    
    Write-Host "✅ Flutter добавлен в PATH!" -ForegroundColor Green
} else {
    Write-Host "✅ Flutter уже есть в PATH!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Обновляем PATH для текущей сессии..." -ForegroundColor Yellow
$env:PATH = "$FlutterPath;$env:PATH"

Write-Host ""
Write-Host "✅ Настройка завершена!" -ForegroundColor Green
Write-Host ""
Write-Host "Теперь выполните:" -ForegroundColor Cyan
Write-Host "flutter --version" -ForegroundColor White
Write-Host ""

Read-Host "Нажмите Enter для продолжения"
