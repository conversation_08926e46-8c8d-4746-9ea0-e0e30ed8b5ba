# This is a generated file and its contents are an internal implementation detail.
# The download step will be re-executed if anything in this file changes.
# No other meaning or use of this file is supported.

method=url
command=C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe;-DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE;-P;D:/Tabel/tabel_app/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/download-pdfium-download.cmake;COMMAND;C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe;-DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE;-P;D:/Tabel/tabel_app/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/verify-pdfium-download.cmake;COMMAND;C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe;-DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE;-P;D:/Tabel/tabel_app/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/extract-pdfium-download.cmake
source_dir=D:/Tabel/tabel_app/build/windows/x64/pdfium-src
work_dir=D:/Tabel/tabel_app/build/windows/x64
url(s)=https://github.com/bblanchon/pdfium-binaries/releases/download/chromium/5200/pdfium-win-x64.tgz
hash=
      no_extract=

