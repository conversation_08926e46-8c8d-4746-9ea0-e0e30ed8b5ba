# fastlane

## Встановлення

```bash
# Встановлення Homebrew (якщо ще не встановлено)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Встановлення Ruby (якщо ще не встановлено)
brew install ruby

# Додавання Ruby до PATH
echo 'export PATH="/usr/local/opt/ruby/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc

# Встановлення fastlane
gem install fastlane
```

## Налаштування

1. Відкрийте файл `Appfile` та змініть наступні параметри:
   - `app_identifier`: Bundle identifier вашого додатку
   - `apple_id`: Ваш Apple ID
   - `team_id`: Ваш Apple Developer Team ID

2. Відкрийте файл `Matchfile` та змініть наступні параметри:
   - `git_url`: URL вашого Git репозиторію для зберігання сертифікатів
   - `app_identifier`: Bundle identifier вашого додатку
   - `username`: Ваш Apple ID

3. Відкрийте файл `Fastfile` та змініть наступні параметри:
   - `"com.yourcompany.tabelapp"`: Bundle identifier вашого додатку
   - `"Tabel App Development Profile"`: Назва вашого Provisioning Profile

## Використання

### Збірка для розробки (Development)

```bash
cd ios
fastlane build_development
```

### Збірка для App Store

```bash
cd ios
fastlane build_appstore
```

### Збірка для Ad Hoc розповсюдження

```bash
cd ios
fastlane build_adhoc
```

## Вирішення проблем

### Помилка підписання

Якщо ви отримуєте помилку підписання, спробуйте оновити сертифікати:

```bash
cd ios
fastlane match development
```

### Помилка з CocoaPods

Якщо ви отримуєте помилку з CocoaPods, спробуйте оновити залежності:

```bash
cd ios
pod install --repo-update
```

### Помилка з Flutter

Якщо ви отримуєте помилку з Flutter, спробуйте оновити Flutter та залежності:

```bash
flutter upgrade
flutter clean
flutter pub get
```
