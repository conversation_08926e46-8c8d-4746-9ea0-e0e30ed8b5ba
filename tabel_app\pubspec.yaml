name: tabel_app_pro
description: "Tabel App Pro - розширений додаток для управління працівниками та обліку робочого часу з надбавками."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Database
  sqflite: ^2.3.2
  sqflite_common_ffi: ^2.3.2+1
  path_provider: ^2.1.2
  shared_preferences: ^2.2.2

  # Firebase - вимкнено для Windows
  # firebase_core: ^2.25.4
  # firebase_auth: ^4.17.4
  # cloud_firestore: ^4.15.4
  # firebase_storage: ^11.6.5

  # State management
  provider: ^6.1.1

  # UI
  table_calendar: ^3.0.9
  intl: ^0.20.2
  flutter_form_builder: ^10.0.1
  form_builder_validators: ^11.1.2
  fl_chart: ^1.0.0
  flutter_colorpicker: ^1.0.3

  # Utils
  uuid: ^4.3.3
  equatable: ^2.0.5
  connectivity_plus: ^6.1.4
  pdf: ^3.10.7
  printing: ^5.11.1
  excel: ^4.0.2
  # file_picker: ^6.1.1 # Вимкнено для Windows
  path: ^1.8.3
  # open_file: ^3.3.2 # Вимкнено через проблеми з macOS
  flutter_local_notifications: ^19.2.1
  timezone: ^0.10.1
  mailer: ^6.4.1

  # Background processing
  # workmanager: ^0.5.2 # Вимкнено через проблеми з релізною збіркою
  # background_fetch: ^1.2.1 # Вимкнено через проблеми з релізною збіркою

  # HTTP
  http: ^1.2.0

  # Environment variables
  flutter_dotenv: ^5.1.0
  msix: ^3.16.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  flutter_launcher_icons: ^0.14.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/translations/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf

# Конфігурація для іконок додатку
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "icon.png"
  remove_alpha_ios: true

# Конфігурація для MSIX інсталятора Windows
msix_config:
  display_name: Табель робочих годин
  publisher_display_name: MuXaCbKo
  identity_name: com.muxacbko.tabelapp
  publisher: CN=MuXaCbKo, O=MuXaCbKo, L=Kyiv, S=Kyiv, C=UA
  msix_version: *******
  logo_path: icon.png
  capabilities: internetClient
  languages: uk-UA, en-US, cs-CZ, sk-SK, pl-PL
  store: false
  architecture: x64
  install_certificate: false
  certificate_path: auto
  certificate_password: 1234
  sign_msix: true
