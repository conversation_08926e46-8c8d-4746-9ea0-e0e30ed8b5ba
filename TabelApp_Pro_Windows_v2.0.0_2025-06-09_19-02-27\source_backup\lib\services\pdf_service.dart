import 'dart:io';

import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'package:open_file/open_file.dart'; // Вимкнено через проблеми з macOS
import '../models/models.dart';
import '../utils/utils.dart';

/// Сервіс для генерації PDF-звітів
class PdfService {
  // Шрифт для PDF
  pw.Font? _font;

  /// Завантаження шрифту з підтримкою кирилиці
  Future<pw.Font> _loadFont() async {
    if (_font != null) return _font!;

    try {
      // Завантажуємо Roboto з асетів
      final fontData = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
      _font = pw.Font.ttf(fontData);
      return _font!;
    } catch (e) {
      // Якщо не вдалося, використовуємо вбудований шрифт
      // Замість print можна використати логування

      try {
        // Спробуємо завантажити шрифт з пакету pdf
        final fontData = await rootBundle.load(
          'packages/pdf/assets/roboto-regular.ttf',
        );
        _font = pw.Font.ttf(fontData);
        return _font!;
      } catch (e2) {
        // Якщо все інше не вдалося, використовуємо стандартний шрифт
        _font = pw.Font.helvetica();
        return _font!;
      }
    }
  }

  /// Генерація PDF-звіту за табелем
  Future<File> generateTimesheetReport({
    required Map<String, dynamic> reportData,
    required Employee employee,
    required DateTime startDate,
    required DateTime endDate,
    required AppCountry country,
    required AppLocalizations localizations,
  }) async {
    // Створення документа
    final pdf = pw.Document();

    // Завантаження шрифту з підтримкою кирилиці
    final font = await _loadFont();

    // Форматування дат
    final dateFormat = DateFormat('dd.MM.yyyy');
    final periodText =
        '${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}';

    // Отримання символу валюти
    final currencySymbol = country.getCurrencySymbol();

    // Додавання сторінки
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Заголовок
              pw.Center(
                child: pw.Text(
                  localizations.translate('timesheet_report'),
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 20,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 10),

              // Інформація про працівника
              pw.Text(
                '${localizations.translate('report_employee')}: ${employee.fullName}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.translate('report_position')}: ${employee.position}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.translate('hourly_rate')}: ${employee.hourlyRate} $currencySymbol',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.translate('report_period')}: $periodText',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.SizedBox(height: 20),

              // Таблиця з даними
              pw.Table(
                border: pw.TableBorder.all(),
                columnWidths: {
                  0: const pw.FlexColumnWidth(2),
                  1: const pw.FlexColumnWidth(1),
                },
                children: [
                  // Заголовок таблиці
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      pw.Padding(
                        padding: pw.EdgeInsets.all(8),
                        child: pw.Text(
                          localizations.translate('category'),
                          style: pw.TextStyle(
                            font: font,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: pw.EdgeInsets.all(8),
                        child: pw.Text(
                          localizations.translate('value'),
                          style: pw.TextStyle(
                            font: font,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Звичайні дні
                  _buildReportRow(
                    localizations.translate('regular'),
                    '${reportData['regularHours']} ${localizations.translate('hours')} - ${reportData['regularHoursWage'].toStringAsFixed(2)} $currencySymbol',
                  ),

                  // Суботи
                  _buildReportRow(
                    localizations.translate('saturday'),
                    '${reportData['saturdayHours']} ${localizations.translate('hours')} - ${reportData['saturdayHoursWage'].toStringAsFixed(2)} $currencySymbol',
                  ),

                  // Неділі
                  _buildReportRow(
                    localizations.translate('sunday'),
                    '${reportData['sundayHours']} ${localizations.translate('hours')} - ${reportData['sundayHoursWage'].toStringAsFixed(2)} $currencySymbol',
                  ),

                  // Святкові дні
                  _buildReportRow(
                    localizations.translate('holiday'),
                    '${reportData['holidayHours']} ${localizations.translate('hours')} - ${reportData['holidayHoursWage'].toStringAsFixed(2)} $currencySymbol',
                  ),

                  // Денні зміни
                  _buildReportRow(
                    localizations.translate('day_shift'),
                    '${reportData['dayShiftHours']} ${localizations.translate('hours')}',
                  ),

                  // Нічні зміни
                  _buildReportRow(
                    localizations.translate('night_shift'),
                    '${reportData['nightShiftHours']} ${localizations.translate('hours')} - ${reportData['nightShiftHoursWage'].toStringAsFixed(2)} $currencySymbol',
                  ),

                  // Заголовок розділу надбавок
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      pw.Padding(
                        padding: pw.EdgeInsets.all(8),
                        child: pw.Text(
                          localizations.translate('bonuses'),
                          style: pw.TextStyle(
                            font: font,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '',
                          style: pw.TextStyle(
                            font: font,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Базові надбавки
                  _buildReportRow(
                    localizations.translate('report_saturday_bonus'),
                    '${(reportData['saturdayBonus'] * 100).toStringAsFixed(0)}% - ${(reportData['saturdayHoursWage'] * reportData['saturdayBonus']).toStringAsFixed(2)} $currencySymbol',
                  ),

                  _buildReportRow(
                    localizations.translate('report_sunday_bonus'),
                    '${(reportData['sundayBonus'] * 100).toStringAsFixed(0)}% - ${(reportData['sundayHoursWage'] * reportData['sundayBonus']).toStringAsFixed(2)} $currencySymbol',
                  ),

                  _buildReportRow(
                    localizations.translate('report_holiday_bonus'),
                    '${(reportData['holidayBonus'] * 100).toStringAsFixed(0)}% - ${(reportData['holidayHoursWage'] * reportData['holidayBonus']).toStringAsFixed(2)} $currencySymbol',
                  ),

                  _buildReportRow(
                    localizations.translate('report_night_shift_bonus'),
                    '${(reportData['nightShiftBonus'] * 100).toStringAsFixed(0)}% - ${(reportData['nightShiftHoursWage'] * reportData['nightShiftBonus']).toStringAsFixed(2)} $currencySymbol',
                  ),

                  // Додаткові надбавки
                  if (reportData['hazardousHoursWage'] > 0)
                    _buildReportRow(
                      localizations.translate('report_hazardous_bonus'),
                      '${(reportData['hazardousBonus'] * 100).toStringAsFixed(0)}% - ${reportData['hazardousHoursWage'].toStringAsFixed(2)} $currencySymbol',
                    ),

                  if (reportData['overtimeHoursWage'] > 0)
                    _buildReportRow(
                      localizations.translate('report_overtime_bonus'),
                      '${(reportData['overtimeBonus'] * 100).toStringAsFixed(0)}% - ${reportData['overtimeHoursWage'].toStringAsFixed(2)} $currencySymbol',
                    ),

                  // Надбавки за стаж та кваліфікацію
                  if (reportData['experienceBonusWage'] > 0)
                    _buildReportRow(
                      localizations.translate('report_experience_bonus'),
                      '${reportData['employeeExperienceYears']} ${localizations.translate('years')} × ${(reportData['experienceBonus'] * 100).toStringAsFixed(0)}% - ${reportData['experienceBonusWage'].toStringAsFixed(2)} $currencySymbol',
                    ),

                  if (reportData['qualificationBonusWage'] > 0)
                    _buildReportRow(
                      localizations.translate('report_qualification_bonus'),
                      '${localizations.translate('level')} ${reportData['employeeQualificationLevel']} × ${(reportData['qualificationBonus'] * 100).toStringAsFixed(0)}% - ${reportData['qualificationBonusWage'].toStringAsFixed(2)} $currencySymbol',
                    ),

                  // Персональні надбавки
                  if (reportData['personalBonusesWage'] > 0)
                    _buildReportRow(
                      localizations.translate('report_personal_bonuses'),
                      '${reportData['personalBonusesWage'].toStringAsFixed(2)} $currencySymbol',
                    ),

                  // Загальна сума
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey200),
                    children: [
                      pw.Padding(
                        padding: pw.EdgeInsets.all(8),
                        child: pw.Text(
                          localizations.translate('total'),
                          style: pw.TextStyle(
                            font: font,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '${reportData['totalWage'].toStringAsFixed(2)} $currencySymbol',
                          style: pw.TextStyle(
                            font: font,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              pw.SizedBox(height: 30),

              // Підписи
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        '${localizations.translate('employee_signature')}:',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                      pw.SizedBox(height: 20),
                      pw.Container(
                        width: 150,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                      ),
                    ],
                  ),
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        '${localizations.translate('manager_signature')}:',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                      pw.SizedBox(height: 20),
                      pw.Container(
                        width: 150,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              pw.SizedBox(height: 30),

              // Дата створення звіту
              pw.Text(
                '${localizations.translate('report_date')}: ${dateFormat.format(DateTime.now())}',
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
          );
        },
      ),
    );

    // Збереження файлу
    final output = await getTemporaryDirectory();
    final file = File(
      '${output.path}/timesheet_report_${DateFormat('yyyyMMdd').format(DateTime.now())}.pdf',
    );
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  /// Створення рядка таблиці звіту
  pw.TableRow _buildReportRow(String title, String value) {
    final font = _font; // Використовуємо поточний шрифт

    return pw.TableRow(
      children: [
        pw.Padding(
          padding: pw.EdgeInsets.all(8),
          child: pw.Text(
            title,
            style: font != null ? pw.TextStyle(font: font) : null,
          ),
        ),
        pw.Padding(
          padding: pw.EdgeInsets.all(8),
          child: pw.Text(
            value,
            style: font != null ? pw.TextStyle(font: font) : null,
          ),
        ),
      ],
    );
  }

  /// Попередній перегляд PDF
  Future<void> previewPdf(File file) async {
    await Printing.layoutPdf(onLayout: (_) async => file.readAsBytes());
  }

  /// Друк PDF
  Future<void> printPdf(File file) async {
    await Printing.layoutPdf(
      onLayout: (_) async => file.readAsBytes(),
      name: file.path.split('/').last,
      format: PdfPageFormat.a4,
    );
  }

  /// Відкриття PDF
  Future<void> openPdf(File file) async {
    // Вимкнено через проблеми з пакетом open_file
    // await OpenFile.open(file.path);

    // Замість цього використовуємо перегляд PDF
    await previewPdf(file);
  }
}
