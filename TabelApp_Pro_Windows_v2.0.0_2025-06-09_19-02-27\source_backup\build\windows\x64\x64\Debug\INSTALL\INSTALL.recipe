﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\flutter\x64\Debug\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\plugins\connectivity_plus\Debug\connectivity_plus_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\plugins\flutter_local_notifications_windows\shared\Debug\flutter_local_notifications_windows.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\plugins\printing\Debug\printing_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\runner\Debug\tabel_app.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\Tabel\tabel_app\build\windows\x64\x64\Debug\INSTALL</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>