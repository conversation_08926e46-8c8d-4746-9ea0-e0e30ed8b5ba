^D:\TABEL\TABEL_APP\BUILD\WINDOWS\X64\CMAKEFILES\5353E21E9AAD1E679D6954EF03FC28CE\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Tabel/tabel_app/windows -BD:/Tabel/tabel_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/Tabel/tabel_app/build/windows/x64/tabel_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
