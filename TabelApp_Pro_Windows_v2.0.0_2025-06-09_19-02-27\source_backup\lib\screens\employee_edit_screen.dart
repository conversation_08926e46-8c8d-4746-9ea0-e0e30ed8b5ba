import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';

/// Екран додавання/редагування працівника (Android)
class EmployeeEditScreen extends StatefulWidget {
  final Employee? employee;

  const EmployeeEditScreen({super.key, this.employee});

  @override
  State<EmployeeEditScreen> createState() => _EmployeeEditScreenState();
}

class _EmployeeEditScreenState extends State<EmployeeEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _positionController = TextEditingController();
  final _hourlyRateController = TextEditingController();
  final _experienceYearsController = TextEditingController();
  final _qualificationLevelController = TextEditingController();

  DateTime? _hireDate;
  bool _isAdmin = false;
  bool _isLoading = false;

  bool get _isEditing => widget.employee != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadEmployeeData();
    }
  }

  void _loadEmployeeData() {
    final employee = widget.employee!;
    _firstNameController.text = employee.firstName;
    _lastNameController.text = employee.lastName;
    _emailController.text = employee.email;
    _positionController.text = employee.position;
    _hourlyRateController.text = employee.hourlyRate.toString();
    _experienceYearsController.text =
        employee.experienceYears?.toString() ?? '';
    _qualificationLevelController.text =
        employee.qualificationLevel?.toString() ?? '';
    _hireDate = employee.hireDate;
    _isAdmin = employee.isAdmin;
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _positionController.dispose();
    _hourlyRateController.dispose();
    _experienceYearsController.dispose();
    _qualificationLevelController.dispose();
    super.dispose();
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );

      final employee = Employee(
        id: _isEditing ? widget.employee!.id : null,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        email: _emailController.text.trim(),
        position: _positionController.text.trim(),
        hourlyRate: double.parse(_hourlyRateController.text.trim()),
        experienceYears:
            _experienceYearsController.text.isEmpty
                ? null
                : int.parse(_experienceYearsController.text.trim()),
        qualificationLevel:
            _qualificationLevelController.text.isEmpty
                ? null
                : int.parse(_qualificationLevelController.text.trim()),
        hireDate: _hireDate,
        isAdmin: _isAdmin,
      );

      if (_isEditing) {
        await databaseService.updateEmployee(employee);
      } else {
        await databaseService.insertEmployee(employee);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'Працівника оновлено' : 'Працівника додано',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(_isEditing ? employee : true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Помилка збереження: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Редагувати працівника' : 'Додати працівника'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveEmployee,
              child: Text(
                _isEditing ? 'ЗБЕРЕГТИ' : 'ДОДАТИ',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Основна інформація
            _buildSectionCard('Основна інформація', Icons.person, [
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _firstNameController,
                      decoration: const InputDecoration(
                        labelText: 'Ім\'я *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Введіть ім\'я';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _lastNameController,
                      decoration: const InputDecoration(
                        labelText: 'Прізвище *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Введіть прізвище';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Email *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Введіть email';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'Введіть коректний email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _positionController,
                decoration: const InputDecoration(
                  labelText: 'Посада *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.work),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Введіть посаду';
                  }
                  return null;
                },
              ),
            ]),
            const SizedBox(height: 24),
            // Фінансова інформація
            _buildSectionCard('Фінансова інформація', Icons.attach_money, [
              TextFormField(
                controller: _hourlyRateController,
                decoration: const InputDecoration(
                  labelText: 'Погодинна ставка (грн) *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.monetization_on),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Введіть ставку';
                  }
                  final rate = double.tryParse(value);
                  if (rate == null || rate <= 0) {
                    return 'Введіть коректну ставку';
                  }
                  return null;
                },
              ),
            ]),
            const SizedBox(height: 24),
            // Додаткова інформація
            _buildSectionCard('Додаткова інформація', Icons.info, [
              InkWell(
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _hireDate ?? DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now(),
                  );
                  if (date != null) {
                    setState(() => _hireDate = date);
                  }
                },
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Дата прийому на роботу',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _hireDate != null
                        ? AppDateUtils.formatDate(_hireDate!)
                        : 'Оберіть дату',
                    style: TextStyle(
                      color: _hireDate != null ? null : Colors.grey[600],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _experienceYearsController,
                      decoration: const InputDecoration(
                        labelText: 'Досвід роботи (років)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.work_history),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _qualificationLevelController,
                      decoration: const InputDecoration(
                        labelText: 'Рівень кваліфікації (1-5)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.star),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final level = int.tryParse(value);
                          if (level == null || level < 1 || level > 5) {
                            return 'Від 1 до 5';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ]),
            const SizedBox(height: 24),
            // Права доступу
            _buildSectionCard('Права доступу', Icons.security, [
              SwitchListTile(
                title: const Text('Адміністратор'),
                subtitle: const Text(
                  'Доступ до управління працівниками та налаштувань',
                ),
                value: _isAdmin,
                onChanged: (value) {
                  setState(() => _isAdmin = value);
                },
                secondary: Icon(
                  _isAdmin ? Icons.admin_panel_settings : Icons.person,
                  color: _isAdmin ? Colors.orange : Colors.grey,
                ),
              ),
            ]),
            const SizedBox(height: 32),
            // Кнопки дій
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('СКАСУВАТИ'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveEmployee,
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : Text(_isEditing ? 'ЗБЕРЕГТИ' : 'ДОДАТИ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }
}
