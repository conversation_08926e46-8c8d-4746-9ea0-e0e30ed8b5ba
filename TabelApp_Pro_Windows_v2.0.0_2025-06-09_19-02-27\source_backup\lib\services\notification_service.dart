import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import '../models/models.dart';
import '../utils/utils.dart';
import 'database_service.dart';
import 'calendar_service.dart';
import 'ios_notification_service.dart';

/// Сервіс для роботи з нагадуваннями та сповіщеннями
class NotificationService {
  final DatabaseService _databaseService = DatabaseService();
  final CalendarService _calendarService = CalendarService();
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // iOS-специфічний сервіс сповіщень
  late IOSNotificationService _iosNotificationService;

  bool _isInitialized = false;

  /// Ініціалізація сервісу
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Ініціалізація часових поясів
    tz_data.initializeTimeZones();

    // Ініціалізація плагіну сповіщень
    const AndroidInitializationSettings androidInitializationSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings iosInitializationSettings =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: androidInitializationSettings,
          iOS: iosInitializationSettings,
        );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );

    // Ініціалізація iOS-специфічного сервісу сповіщень
    if (Platform.isIOS) {
      _iosNotificationService = IOSNotificationService(
        _flutterLocalNotificationsPlugin,
      );
      await _iosNotificationService.initialize();
    }

    _isInitialized = true;
  }

  /// Обробка натискання на сповіщення
  void _onNotificationTap(NotificationResponse notificationResponse) {
    // Тут можна додати логіку для обробки натискання на сповіщення
    // Наприклад, відкриття відповідного екрану
  }

  /// Перевірка дозволів на сповіщення
  Future<bool> requestPermissions() async {
    // Для Android
    final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
        _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >();

    if (androidPlugin != null) {
      final bool? granted =
          await androidPlugin.requestNotificationsPermission();
      return granted ?? false;
    }

    // Для iOS
    if (Platform.isIOS) {
      return await _iosNotificationService.requestPermissions();
    }

    return true;
  }

  /// Створення нагадування
  Future<void> createReminder(Reminder reminder) async {
    await _databaseService.insertReminder(reminder);
    await _scheduleNotification(reminder);
  }

  /// Оновлення нагадування
  Future<void> updateReminder(Reminder reminder) async {
    await _databaseService.updateReminder(reminder);
    await _cancelNotification(reminder.id);

    if (reminder.status == ReminderStatus.active ||
        reminder.status == ReminderStatus.snoozed) {
      await _scheduleNotification(reminder);
    }
  }

  /// Видалення нагадування
  Future<void> deleteReminder(String id) async {
    await _databaseService.deleteReminder(id);
    await _cancelNotification(id);
  }

  /// Отримання нагадування за ідентифікатором
  Future<Reminder?> getReminder(String id) async {
    return await _databaseService.getReminder(id);
  }

  /// Отримання всіх нагадувань для працівника
  Future<List<Reminder>> getRemindersByEmployee(String employeeId) async {
    return await _databaseService.getRemindersByEmployee(employeeId);
  }

  /// Отримання активних нагадувань для працівника
  Future<List<Reminder>> getActiveRemindersByEmployee(String employeeId) async {
    return await _databaseService.getRemindersByEmployeeAndStatus(
      employeeId,
      ReminderStatus.active,
    );
  }

  /// Відкладення нагадування
  Future<void> snoozeReminder(String id, Duration duration) async {
    final reminder = await _databaseService.getReminder(id);
    if (reminder == null) return;

    final updatedReminder = reminder.copyWith(
      status: ReminderStatus.snoozed,
      scheduledFor: DateTime.now().add(duration),
    );

    await updateReminder(updatedReminder);
  }

  /// Закриття нагадування
  Future<void> dismissReminder(String id) async {
    final reminder = await _databaseService.getReminder(id);
    if (reminder == null) return;

    final updatedReminder = reminder.copyWith(status: ReminderStatus.dismissed);

    await updateReminder(updatedReminder);
  }

  /// Перевірка відсутності даних у табелі за попередній день
  Future<void> checkMissingTimesheetData(
    String employeeId,
    String employeeName,
    AppLocalizations localizations,
  ) async {
    try {
      // Перевірка, чи існує таблиця reminders
      final tableExists = await _checkIfTableExists('reminders');
      if (!tableExists) {
        // Якщо таблиця не існує, створюємо її
        await _createRemindersTable();
      }

      // Отримання вчорашньої дати
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      final yesterdayNormalized = DateTime(
        yesterday.year,
        yesterday.month,
        yesterday.day,
      );

      // Перевірка, чи є вчорашній день вихідним або святковим
      // Отримуємо тип дня (звичайний, субота, неділя, святковий)
      final dayType = await _calendarService.getDayType(
        yesterdayNormalized,
        'UA',
      ); // Використовуємо Україну як країну за замовчуванням

      // Якщо день є вихідним або святковим, не створюємо нагадування
      if (dayType == DayType.saturday ||
          dayType == DayType.sunday ||
          dayType == DayType.holiday) {
        debugPrint(
          'Skipping reminder for non-working day: $yesterdayNormalized, type: $dayType',
        );
        return;
      }

      // Перевірка, чи є дані за вчорашній день
      final workDays = await _databaseService.getWorkDaysByEmployeeAndDate(
        employeeId,
        yesterdayNormalized,
      );

      // Якщо даних немає, створюємо нагадування
      if (workDays.isEmpty) {
        try {
          // Перевірка, чи не було вже створено нагадування за цю дату
          final existingReminders = await _databaseService
              .getRemindersByEmployeeAndRelatedDate(
                employeeId,
                yesterdayNormalized,
              );

          if (existingReminders.isEmpty) {
            // Створення нагадування
            final title = localizations.translate('missing_timesheet_title');
            final message = localizations
                .translate('missing_timesheet_message')
                .replaceAll(
                  '{date}',
                  localizations.formatDate(yesterdayNormalized),
                );

            final reminder = Reminder(
              employeeId: employeeId,
              type: ReminderType.missingTimesheet,
              category: ReminderCategory.timesheet, // Додано категорію
              title: title,
              message: message,
              scheduledFor: DateTime.now().add(
                const Duration(minutes: 1),
              ), // Для тестування
              relatedDate: yesterdayNormalized,
            );

            await createReminder(reminder);
          }
        } catch (e) {
          debugPrint('Error checking existing reminders: $e');
        }
      }
    } catch (e) {
      debugPrint('Error in checkMissingTimesheetData: $e');
      // Не передаємо помилку далі, щоб не зупиняти роботу додатку
    }
  }

  /// Планування сповіщення
  Future<void> _scheduleNotification(Reminder reminder) async {
    await initialize();

    // Конвертація часу в часовий пояс
    final scheduledDate = tz.TZDateTime.from(reminder.scheduledFor, tz.local);

    // Для iOS
    if (Platform.isIOS) {
      await _iosNotificationService.scheduleNotification(
        id: int.parse(reminder.id.hashCode.toString().replaceAll('-', '')),
        title: reminder.title,
        body: reminder.message,
        scheduledDate: scheduledDate,
      );
      return;
    }

    // Для Android та інших платформ
    final androidDetails = const AndroidNotificationDetails(
      'timesheet_reminders',
      'Timesheet Reminders',
      channelDescription: 'Notifications for timesheet reminders',
      importance: Importance.high,
      priority: Priority.high,
    );

    final iosDetails = const DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _flutterLocalNotificationsPlugin.zonedSchedule(
      int.parse(reminder.id.hashCode.toString().replaceAll('-', '')),
      reminder.title,
      reminder.message,
      scheduledDate,
      notificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  /// Скасування сповіщення
  Future<void> _cancelNotification(String id) async {
    final notificationId = int.parse(
      id.hashCode.toString().replaceAll('-', ''),
    );

    // Для iOS
    if (Platform.isIOS) {
      await _iosNotificationService.cancelNotification(notificationId);
      return;
    }

    // Для Android та інших платформ
    await _flutterLocalNotificationsPlugin.cancel(notificationId);
  }

  /// Скасування всіх сповіщень
  Future<void> cancelAllNotifications() async {
    // Для iOS
    if (Platform.isIOS) {
      await _iosNotificationService.cancelAllNotifications();
      return;
    }

    // Для Android та інших платформ
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  /// Перевірка, чи існує таблиця
  Future<bool> _checkIfTableExists(String tableName) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName],
      );
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking if table exists: $e');
      return false;
    }
  }

  /// Створення таблиці reminders
  Future<void> _createRemindersTable() async {
    try {
      final db = await _databaseService.database;
      await db.execute('''
        CREATE TABLE reminders (
          id TEXT PRIMARY KEY,
          employeeId TEXT NOT NULL,
          type INTEGER NOT NULL,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          createdAt INTEGER NOT NULL,
          scheduledFor INTEGER NOT NULL,
          relatedDate INTEGER NOT NULL,
          status INTEGER NOT NULL,
          updatedAt INTEGER NOT NULL,
          FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE
        )
      ''');
      debugPrint('Created reminders table');
    } catch (e) {
      debugPrint('Error creating reminders table: $e');
      rethrow;
    }
  }
}
