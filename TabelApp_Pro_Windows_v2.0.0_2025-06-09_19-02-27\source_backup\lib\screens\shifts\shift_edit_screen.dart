import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import '../../widgets/widgets.dart';

/// Екран редагування зміни
class ShiftEditScreen extends StatefulWidget {
  /// Зміна для редагування (null для створення нової)
  final Shift? shift;

  /// Конструктор
  const ShiftEditScreen({Key? key, this.shift}) : super(key: key);

  @override
  State<ShiftEditScreen> createState() => _ShiftEditScreenState();
}

class _ShiftEditScreenState extends State<ShiftEditScreen> {
  final ShiftScheduleService _shiftScheduleService = ShiftScheduleService();
  final _formKey = GlobalKey<FormState>();
  
  late String _name;
  late TimeOfDay _startTime;
  late TimeOfDay _endTime;
  late Color _color;
  late bool _isNightShift;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  /// Ініціалізація полів форми
  void _initializeFields() {
    if (widget.shift != null) {
      _name = widget.shift!.name;
      _startTime = widget.shift!.startTime;
      _endTime = widget.shift!.endTime;
      _color = widget.shift!.color;
      _isNightShift = widget.shift!.isNightShift;
    } else {
      _name = '';
      _startTime = const TimeOfDay(hour: 8, minute: 0);
      _endTime = const TimeOfDay(hour: 17, minute: 0);
      _color = Colors.blue;
      _isNightShift = false;
    }
  }

  /// Вибір часу початку зміни
  Future<void> _selectStartTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _startTime,
    );
    if (picked != null && picked != _startTime) {
      setState(() {
        _startTime = picked;
      });
    }
  }

  /// Вибір часу закінчення зміни
  Future<void> _selectEndTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _endTime,
    );
    if (picked != null && picked != _endTime) {
      setState(() {
        _endTime = picked;
      });
    }
  }

  /// Вибір кольору зміни
  void _selectColor() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Виберіть колір'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: _color,
            onColorChanged: (color) {
              setState(() {
                _color = color;
              });
            },
            pickerAreaHeightPercent: 0.8,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Готово'),
          ),
        ],
      ),
    );
  }

  /// Збереження зміни
  Future<void> _saveShift() async {
    if (!_formKey.currentState!.validate()) return;
    
    _formKey.currentState!.save();
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final shift = Shift(
        id: widget.shift?.id,
        name: _name,
        startTime: _startTime,
        endTime: _endTime,
        color: _color,
        isNightShift: _isNightShift,
      );
      
      if (widget.shift == null) {
        await _shiftScheduleService.insertShift(shift);
      } else {
        await _shiftScheduleService.updateShift(shift);
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Зміну збережено')),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка збереження зміни: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.shift == null ? 'Нова зміна' : 'Редагування зміни'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      initialValue: _name,
                      decoration: const InputDecoration(
                        labelText: 'Назва зміни',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Введіть назву зміни';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _name = value!;
                      },
                    ),
                    const SizedBox(height: 16.0),
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: _selectStartTime,
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Початок зміни',
                                border: OutlineInputBorder(),
                              ),
                              child: Text(
                                '${_startTime.hour.toString().padLeft(2, '0')}:${_startTime.minute.toString().padLeft(2, '0')}',
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16.0),
                        Expanded(
                          child: InkWell(
                            onTap: _selectEndTime,
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Кінець зміни',
                                border: OutlineInputBorder(),
                              ),
                              child: Text(
                                '${_endTime.hour.toString().padLeft(2, '0')}:${_endTime.minute.toString().padLeft(2, '0')}',
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16.0),
                    InkWell(
                      onTap: _selectColor,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Колір зміни',
                          border: OutlineInputBorder(),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 24.0,
                              height: 24.0,
                              decoration: BoxDecoration(
                                color: _color,
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                            ),
                            const SizedBox(width: 8.0),
                            Text(
                              '#${_color.value.toRadixString(16).substring(2).toUpperCase()}',
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    SwitchListTile(
                      title: const Text('Нічна зміна'),
                      subtitle: const Text(
                        'Автоматично нараховувати бонус за нічну зміну',
                      ),
                      value: _isNightShift,
                      onChanged: (value) {
                        setState(() {
                          _isNightShift = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16.0),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveShift,
                        child: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          child: Text('Зберегти'),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
