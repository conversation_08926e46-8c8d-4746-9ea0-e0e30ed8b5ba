# Інструкція з встановлення Flutter SDK

## 1. Завантаження Flutter

1. Перейдіть на https://flutter.dev/docs/get-started/install/windows
2. Завантажте останню стабільну версію Flutter SDK
3. Розпакуйте архів у папку `C:\flutter` (або іншу зручну папку)

## 2. Налаштування змінних середовища

1. Відкрийте "Панель керування" → "Система" → "Додаткові параметри системи"
2. Натисніть "Змінні середовища"
3. У розділі "Системні змінні" знайдіть змінну `Path`
4. Додайте шлях до Flutter: `C:\flutter\bin`

## 3. Перевірка встановлення

Відкрийте командний рядок і виконайте:
```cmd
flutter doctor
```

## 4. Встановлення залежностей

Якщо `flutter doctor` показує проблеми:

### Android Studio (для Android розробки):
```cmd
flutter doctor --android-licenses
```

### Visual Studio (для Windows розробки):
- Встановіть Visual Studio 2022 Community
- Включіть "Desktop development with C++"

### Chrome (для веб-розробки):
- Встановіть Google Chrome

## 5. Відновлення проекту

Після встановлення Flutter:

```cmd
cd tabel_app
flutter clean
flutter pub get
flutter run -d windows
```

## Альтернативні варіанти запуску:

### Веб-версія:
```cmd
flutter run -d chrome
```

### Android (якщо є емулятор):
```cmd
flutter run -d android
```

## Якщо проблеми залишаються:

1. Видаліть папку `build`:
```cmd
rmdir /s build
```

2. Очистіть кеш:
```cmd
flutter clean
flutter pub cache repair
```

3. Перебудуйте проект:
```cmd
flutter pub get
flutter run
```
