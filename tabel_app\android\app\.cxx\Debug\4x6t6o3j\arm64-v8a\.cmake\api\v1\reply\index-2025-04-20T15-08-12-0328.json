{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-45bc9f8082f6a69e4e0b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-d450d683f8cefbfa34fa.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-9e67e09b8d8dbd734468.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-d450d683f8cefbfa34fa.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-9e67e09b8d8dbd734468.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-45bc9f8082f6a69e4e0b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}