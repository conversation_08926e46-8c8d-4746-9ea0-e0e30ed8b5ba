import '../models/models.dart';
import 'database_service.dart';
import 'calendar_service.dart';

/// Сервіс для управління відпустками та іншими відсутностями
class AbsenceService {
  final DatabaseService _databaseService = DatabaseService();
  final CalendarService _calendarService = CalendarService();

  /// Створення нової відсутності
  Future<void> createAbsence(Absence absence) async {
    await _databaseService.insertAbsence(absence);
  }

  /// Оновлення відсутності
  Future<void> updateAbsence(Absence absence) async {
    await _databaseService.updateAbsence(absence);
  }

  /// Видалення відсутності
  Future<void> deleteAbsence(String id) async {
    await _databaseService.deleteAbsence(id);
  }

  /// Отримання відсутності за ідентифікатором
  Future<Absence?> getAbsence(String id) async {
    return await _databaseService.getAbsence(id);
  }

  /// Отримання всіх відсутностей працівника
  Future<List<Absence>> getAbsencesByEmployee(String employeeId) async {
    return await _databaseService.getAbsencesByEmployee(employeeId);
  }

  /// Отримання відсутностей працівника за період
  Future<List<Absence>> getAbsencesByEmployeeAndPeriod(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    return await _databaseService.getAbsencesByEmployeeAndPeriod(
      employeeId,
      startDate,
      endDate,
    );
  }

  /// Отримання відсутностей працівника за типом та статусом
  Future<List<Absence>> getAbsencesByEmployeeAndTypeAndStatus(
    String employeeId,
    AbsenceType type,
    AbsenceStatus status,
  ) async {
    return await _databaseService.getAbsencesByEmployeeAndTypeAndStatus(
      employeeId,
      type,
      status,
    );
  }

  /// Отримання всіх відсутностей за статусом
  Future<List<Absence>> getAbsencesByStatus(AbsenceStatus status) async {
    return await _databaseService.getAbsencesByStatus(status);
  }

  /// Перевірка, чи є у працівника активна відсутність на вказану дату
  Future<bool> hasActiveAbsence(String employeeId, DateTime date) async {
    return await _databaseService.hasActiveAbsence(employeeId, date);
  }

  /// Отримання активної відсутності працівника на вказану дату
  Future<Absence?> getActiveAbsence(String employeeId, DateTime date) async {
    return await _databaseService.getActiveAbsence(employeeId, date);
  }

  /// Затвердження відсутності
  Future<void> approveAbsence(
    String absenceId,
    String approvedById,
    String? comment,
  ) async {
    final absence = await _databaseService.getAbsence(absenceId);
    if (absence == null) {
      throw Exception('Відсутність не знайдена');
    }

    final updatedAbsence = absence.copyWith(
      status: AbsenceStatus.approved,
      approvedById: approvedById,
      approvedAt: DateTime.now(),
      approvalComment: comment,
      updatedAt: DateTime.now(),
    );

    await _databaseService.updateAbsence(updatedAbsence);
  }

  /// Відхилення відсутності
  Future<void> rejectAbsence(
    String absenceId,
    String approvedById,
    String? comment,
  ) async {
    final absence = await _databaseService.getAbsence(absenceId);
    if (absence == null) {
      throw Exception('Відсутність не знайдена');
    }

    final updatedAbsence = absence.copyWith(
      status: AbsenceStatus.rejected,
      approvedById: approvedById,
      approvedAt: DateTime.now(),
      approvalComment: comment,
      updatedAt: DateTime.now(),
    );

    await _databaseService.updateAbsence(updatedAbsence);
  }

  /// Скасування відсутності
  Future<void> cancelAbsence(String absenceId) async {
    final absence = await _databaseService.getAbsence(absenceId);
    if (absence == null) {
      throw Exception('Відсутність не знайдена');
    }

    final updatedAbsence = absence.copyWith(
      status: AbsenceStatus.cancelled,
      updatedAt: DateTime.now(),
    );

    await _databaseService.updateAbsence(updatedAbsence);
  }

  /// Розрахунок кількості робочих днів у відпустці
  Future<int> calculateWorkingDaysInAbsence(
    DateTime startDate,
    DateTime endDate,
    String country,
  ) async {
    int workingDays = 0;
    DateTime currentDate = startDate;

    while (!currentDate.isAfter(endDate)) {
      final dayType = await _calendarService.getDayType(currentDate, country);
      
      // Рахуємо тільки робочі дні (не вихідні та не свята)
      if (dayType == DayType.regular) {
        workingDays++;
      }
      
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return workingDays;
  }

  /// Розрахунок залишку днів щорічної відпустки
  Future<int> calculateRemainingVacationDays(
    String employeeId,
    int totalVacationDays,
    int year,
  ) async {
    // Отримання всіх затверджених відпусток працівника за вказаний рік
    final startOfYear = DateTime(year, 1, 1);
    final endOfYear = DateTime(year, 12, 31);
    
    final absences = await _databaseService.getAbsencesByEmployeeAndPeriod(
      employeeId,
      startOfYear,
      endOfYear,
    );
    
    // Фільтрація тільки затверджених щорічних відпусток
    final approvedVacations = absences.where((absence) => 
      absence.type == AbsenceType.vacation && 
      absence.status == AbsenceStatus.approved
    ).toList();
    
    // Отримання країни для розрахунку робочих днів
    final employee = await _databaseService.getEmployee(employeeId);
    if (employee == null) {
      throw Exception('Працівника не знайдено');
    }
    
    // Отримання країни з налаштувань додатку
    final appSettings = await _getAppSettings();
    final country = appSettings.countryCode;
    
    // Розрахунок використаних днів відпустки
    int usedVacationDays = 0;
    for (var vacation in approvedVacations) {
      usedVacationDays += await calculateWorkingDaysInAbsence(
        vacation.startDate,
        vacation.endDate,
        country,
      );
    }
    
    // Розрахунок залишку днів відпустки
    return totalVacationDays - usedVacationDays;
  }
  
  /// Отримання налаштувань додатку
  Future<AppSettings> _getAppSettings() async {
    // Тут має бути логіка отримання налаштувань додатку
    // Наразі повертаємо значення за замовчуванням
    return const AppSettings();
  }
  
  /// Перевірка можливості взяття відпустки
  Future<bool> canTakeVacation(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
    int totalVacationDays,
    int year,
  ) async {
    // Перевірка, чи не перетинається з іншими відсутностями
    final existingAbsences = await _databaseService.getAbsencesByEmployeeAndPeriod(
      employeeId,
      startDate,
      endDate,
    );
    
    final approvedAbsences = existingAbsences.where((absence) => 
      absence.status == AbsenceStatus.approved
    ).toList();
    
    if (approvedAbsences.isNotEmpty) {
      return false; // Є перетин з іншими відсутностями
    }
    
    // Отримання країни з налаштувань додатку
    final appSettings = await _getAppSettings();
    final country = appSettings.countryCode;
    
    // Розрахунок кількості робочих днів у запитуваній відпустці
    final requestedWorkingDays = await calculateWorkingDaysInAbsence(
      startDate,
      endDate,
      country,
    );
    
    // Розрахунок залишку днів відпустки
    final remainingDays = await calculateRemainingVacationDays(
      employeeId,
      totalVacationDays,
      year,
    );
    
    // Перевірка, чи вистачає днів відпустки
    return requestedWorkingDays <= remainingDays;
  }
}
