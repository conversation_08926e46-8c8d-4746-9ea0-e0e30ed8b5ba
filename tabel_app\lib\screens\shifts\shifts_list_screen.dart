import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import '../../widgets/widgets.dart';
import 'shift_edit_screen.dart';

/// Екран списку змін
class ShiftsListScreen extends StatefulWidget {
  /// Конструктор
  const ShiftsListScreen({Key? key}) : super(key: key);

  @override
  State<ShiftsListScreen> createState() => _ShiftsListScreenState();
}

class _ShiftsListScreenState extends State<ShiftsListScreen> {
  final ShiftScheduleService _shiftScheduleService = ShiftScheduleService();
  List<Shift> _shifts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadShifts();
  }

  /// Завантаження списку змін
  Future<void> _loadShifts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _shifts = await _shiftScheduleService.getAllShifts();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Помилка завантаження змін: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Видалення зміни
  Future<void> _deleteShift(String id) async {
    try {
      await _shiftScheduleService.deleteShift(id);
      await _loadShifts();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Зміну видалено')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка видалення зміни: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Зміни'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _shifts.isEmpty
              ? const Center(child: Text('Немає доступних змін'))
              : ListView.builder(
                  itemCount: _shifts.length,
                  itemBuilder: (context, index) {
                    final shift = _shifts[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 8.0,
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: shift.color,
                          child: Text(
                            shift.name.substring(0, 1),
                            style: TextStyle(
                              color: shift.color.computeLuminance() > 0.5
                                  ? Colors.black
                                  : Colors.white,
                            ),
                          ),
                        ),
                        title: Text(shift.name),
                        subtitle: Text(
                          '${shift.formattedTime} ${shift.isNightShift ? '(Нічна)' : ''}',
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () async {
                                await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => ShiftEditScreen(
                                      shift: shift,
                                    ),
                                  ),
                                );
                                _loadShifts();
                              },
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () {
                                showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: const Text('Видалення зміни'),
                                    content: Text(
                                      'Ви впевнені, що хочете видалити зміну "${shift.name}"?',
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                        },
                                        child: const Text('Скасувати'),
                                      ),
                                      TextButton(
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                          _deleteShift(shift.id);
                                        },
                                        child: const Text('Видалити'),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                        onTap: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ShiftEditScreen(
                                shift: shift,
                              ),
                            ),
                          );
                          _loadShifts();
                        },
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ShiftEditScreen(),
            ),
          );
          _loadShifts();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
