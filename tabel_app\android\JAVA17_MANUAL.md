# Інструкція з ручного встановлення Java 17

Ця інструкція допоможе вам вручну встановити Java 17, яка необхідна для збірки Flutter-додатку для Android.

## 1. Розпакування архіву Java 17

Якщо ви вже завантажили архів Java 17 за допомогою скрипту `get_java17.bat`, виконайте наступні кроки:

1. Відкрийте Провідник Windows і перейдіть до директорії `C:\Java17`
2. Знайдіть файл `java17.zip` або `zulu17.46.19-ca-jdk17.0.9-win_x64.zip`
3. Правою кнопкою миші клацніть на архів і виберіть "Извлечь все..." або "Extract All..."
4. Вкажіть директорію `C:\Java17` як місце призначення
5. Натисніть "Извлечь" або "Extract"

Після розпакування, в директорії `C:\Java17` повинна з'явитися піддиректорія з назвою на зразок `zulu17.46.19-ca-jdk17.0.9-win_x64`. Це і є ваша Java 17.

## 2. Завантаження Java 17 вручну

Якщо ви ще не завантажили Java 17, виконайте наступні кроки:

1. Перейдіть на один з наступних сайтів:
   - [Eclipse Temurin (OpenJDK) 17](https://adoptium.net/temurin/releases/?version=17)
   - [Azul Zulu 17](https://www.azul.com/downloads/?version=java-17-lts&package=jdk)
   - [Oracle JDK 17](https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html)

2. Завантажте архів Java 17 для Windows (zip або msi)

3. Якщо ви завантажили zip-архів:
   - Створіть директорію `C:\Java17`
   - Розпакуйте архів в цю директорію

4. Якщо ви завантажили msi-інсталятор:
   - Запустіть інсталятор
   - Слідуйте інструкціям на екрані
   - Запам'ятайте шлях, куди встановлюється Java (зазвичай це `C:\Program Files\Java\jdk-17.x.x`)

## 3. Перевірка встановлення Java 17

Щоб перевірити, чи правильно встановлена Java 17:

1. Відкрийте командний рядок
2. Виконайте наступну команду:
   ```
   "C:\Java17\zulu17.46.19-ca-jdk17.0.9-win_x64\bin\java" -version
   ```
   (замініть `zulu17.46.19-ca-jdk17.0.9-win_x64` на правильну назву піддиректорії, якщо вона відрізняється)

Ви повинні побачити інформацію про версію Java 17.

## 4. Збірка додатку з Java 17

Після встановлення Java 17, ви можете зібрати ваш додаток:

1. Відкрийте командний рядок
2. Перейдіть до директорії `D:\Tabel\tabel_app\android`
3. Запустіть скрипт для збірки додатку з використанням встановленої Java 17:
   ```
   build_java17.bat
   ```

Якщо ви встановили Java 17 в іншу директорію, ви можете використати скрипт з аргументом шляху до Java 17:
```
build_with_java17.bat "C:\шлях\до\вашої\Java17"
```

## Вирішення проблем

Якщо у вас виникають проблеми з розпакуванням архіву:

1. Спробуйте використати інший архіватор, наприклад, [7-Zip](https://www.7-zip.org/)
2. Спробуйте завантажити інсталятор (msi) замість архіву (zip)
3. Спробуйте встановити Java 17 в іншу директорію, без пробілів і кириличних символів у шляху
