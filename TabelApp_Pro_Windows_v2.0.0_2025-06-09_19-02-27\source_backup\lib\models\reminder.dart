import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';

/// Типи нагадувань
enum ReminderType {
  /// Нагадування про відсутність даних у табелі
  missingTimesheet,

  /// Інші типи нагадувань можна додати тут
}

/// Категорії нагадувань
enum ReminderCategory {
  /// Нагадування про заповнення табеля
  timesheet,

  /// Нагадування про відпустку
  vacation,

  /// Нагадування про лікарняний
  sickLeave,

  /// Інші нагадування
  other,
}

/// Статуси нагадувань
enum ReminderStatus {
  /// Активне (ще не показане)
  active,

  /// Показане
  shown,

  /// Відкладене
  snoozed,

  /// Закрите
  dismissed,
}

/// Модель нагадування
class Reminder extends Equatable {
  /// Унікальний ідентифікатор
  final String id;

  /// Ідентифікатор працівника
  final String employeeId;

  /// Тип нагадування
  final ReminderType type;

  /// Категорія нагадування
  final ReminderCategory category;

  /// Заголовок нагадування
  final String title;

  /// Текст нагадування
  final String message;

  /// Дата створення нагадування
  final DateTime createdAt;

  /// Дата, коли нагадування має бути показане
  final DateTime scheduledFor;

  /// Дата, до якої відноситься нагадування (наприклад, дата відсутнього запису)
  final DateTime relatedDate;

  /// Статус нагадування
  final ReminderStatus status;

  /// Дата останнього оновлення
  final DateTime updatedAt;

  /// Конструктор
  Reminder({
    String? id,
    required this.employeeId,
    required this.type,
    required this.category,
    required this.title,
    required this.message,
    required this.scheduledFor,
    required this.relatedDate,
    DateTime? createdAt,
    this.status = ReminderStatus.active,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  Reminder copyWith({
    String? employeeId,
    ReminderType? type,
    ReminderCategory? category,
    String? title,
    String? message,
    DateTime? scheduledFor,
    DateTime? relatedDate,
    ReminderStatus? status,
    DateTime? updatedAt,
  }) {
    return Reminder(
      id: this.id,
      employeeId: employeeId ?? this.employeeId,
      type: type ?? this.type,
      category: category ?? this.category,
      title: title ?? this.title,
      message: message ?? this.message,
      scheduledFor: scheduledFor ?? this.scheduledFor,
      relatedDate: relatedDate ?? this.relatedDate,
      createdAt: this.createdAt,
      status: status ?? this.status,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'type': type.index,
      'category': category.index,
      'title': title,
      'message': message,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'scheduledFor': scheduledFor.millisecondsSinceEpoch,
      'relatedDate': relatedDate.millisecondsSinceEpoch,
      'status': status.index,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory Reminder.fromMap(Map<String, dynamic> map) {
    return Reminder(
      id: map['id'],
      employeeId: map['employeeId'],
      type: ReminderType.values[map['type']],
      category: ReminderCategory.values[map['category']],
      title: map['title'],
      message: map['message'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      scheduledFor: DateTime.fromMillisecondsSinceEpoch(map['scheduledFor']),
      relatedDate: DateTime.fromMillisecondsSinceEpoch(map['relatedDate']),
      status: ReminderStatus.values[map['status']],
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  @override
  List<Object?> get props => [
    id,
    employeeId,
    type,
    category,
    title,
    message,
    createdAt,
    scheduledFor,
    relatedDate,
    status,
    updatedAt,
  ];
}

/// Розширення для отримання назви типу нагадування
extension ReminderTypeExtension on ReminderType {
  String getLocalizationKey() {
    switch (this) {
      case ReminderType.missingTimesheet:
        return 'missing_timesheet_reminder';
    }
  }
}

/// Розширення для отримання назви статусу нагадування
extension ReminderStatusExtension on ReminderStatus {
  String getLocalizationKey() {
    switch (this) {
      case ReminderStatus.active:
        return 'reminder_status_active';
      case ReminderStatus.shown:
        return 'reminder_status_shown';
      case ReminderStatus.snoozed:
        return 'reminder_status_snoozed';
      case ReminderStatus.dismissed:
        return 'reminder_status_dismissed';
    }
  }
}
