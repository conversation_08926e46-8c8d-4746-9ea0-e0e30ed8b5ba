import 'package:flutter/material.dart';
import 'tabel_app/lib/models/shift_schedule.dart';
import 'tabel_app/lib/models/day_of_week.dart';

void main() {
  // Тестування нової логіки розрахунку змін
  testShiftScheduleLogic();
}

void testShiftScheduleLogic() {
  print('=== Тестування логіки розрахунку змін ===\n');
  
  // Створюємо тестовий графік змін
  final shiftSchedule = ShiftSchedule(
    name: 'Тестовий графік 3 зміни',
    shiftOrder: ['shift1', 'shift2', 'shift3'], // 3 зміни
    currentShiftIndex: 0, // Починаємо з першої зміни
    startDate: DateTime(2024, 1, 1), // Понеділок, 1 січня 2024
    dayOff1: DayOfWeek.saturday,
    dayOff2: DayOfWeek.sunday,
    hasFloatingDaysOff: false,
    workDaysCount: 5,
    daysOffCount: 2,
  );
  
  print('Графік змін: ${shiftSchedule.name}');
  print('Порядок змін: ${shiftSchedule.shiftOrder}');
  print('Початкова дата: ${shiftSchedule.startDate}');
  print('Вихідні: ${shiftSchedule.dayOff1.name}, ${shiftSchedule.dayOff2?.name ?? "немає"}');
  print('');
  
  // Тестуємо кілька тижнів
  for (int week = 0; week < 4; week++) {
    print('=== Тиждень ${week + 1} ===');
    
    // Понеділок цього тижня
    final mondayOfWeek = shiftSchedule.startDate.add(Duration(days: week * 7));
    
    // Перевіряємо кожен день тижня
    for (int day = 0; day < 7; day++) {
      final currentDate = mondayOfWeek.add(Duration(days: day));
      final dayName = _getDayName(currentDate.weekday);
      
      if (shiftSchedule.isDayOff(currentDate)) {
        print('$dayName ${currentDate.day}.${currentDate.month}: ВИХІДНИЙ');
      } else {
        final shiftId = shiftSchedule.getShiftIdForDate(currentDate);
        print('$dayName ${currentDate.day}.${currentDate.month}: $shiftId');
      }
    }
    print('');
  }
}

String _getDayName(int weekday) {
  switch (weekday) {
    case 1: return 'Пн';
    case 2: return 'Вт';
    case 3: return 'Ср';
    case 4: return 'Чт';
    case 5: return 'Пт';
    case 6: return 'Сб';
    case 7: return 'Нд';
    default: return '??';
  }
}
