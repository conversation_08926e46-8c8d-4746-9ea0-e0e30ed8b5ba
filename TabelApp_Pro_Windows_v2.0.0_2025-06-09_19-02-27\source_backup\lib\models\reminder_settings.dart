import 'dart:convert';
import 'package:flutter/material.dart';

/// Частота нагадувань
enum ReminderFrequency {
  /// Щодня
  daily,

  /// Тільки в робочі дні
  weekdays,

  /// Користувацькі дні
  custom,
}

/// Розширення для ReminderFrequency
extension ReminderFrequencyExtension on ReminderFrequency {
  /// Отримання назви для відображення
  String get displayName {
    switch (this) {
      case ReminderFrequency.daily:
        return 'Щодня';
      case ReminderFrequency.weekdays:
        return 'Тільки в робочі дні';
      case ReminderFrequency.custom:
        return 'Користувацькі дні';
    }
  }
}

/// Типи нагадувань
enum NotificationType {
  /// Внутрішні нагадування в додатку
  inApp,

  /// Нагадування електронною поштою
  email,

  /// Push-нагадування
  push,
}

/// Розширення для NotificationType
extension NotificationTypeExtension on NotificationType {
  /// Отримання назви для відображення
  String get displayName {
    switch (this) {
      case NotificationType.inApp:
        return 'В додатку';
      case NotificationType.email:
        return 'Електронна пошта';
      case NotificationType.push:
        return 'Push-повідомлення';
    }
  }

  /// Отримання іконки
  IconData get icon {
    switch (this) {
      case NotificationType.inApp:
        return Icons.notifications;
      case NotificationType.email:
        return Icons.email;
      case NotificationType.push:
        return Icons.notifications_active;
    }
  }
}

/// Налаштування нагадувань
class ReminderSettings {
  /// Чи увімкнені нагадування
  final bool enabled;

  /// Частота нагадувань
  final ReminderFrequency frequency;

  /// Час нагадування
  final TimeOfDay reminderTime;

  /// Дні тижня для нагадувань (1-7 для Пн-Нд)
  final List<int> reminderDays;

  /// Типи нагадувань
  final List<NotificationType> reminderTypes;

  /// Чи надсилати нагадування у вихідні та святкові дні
  final bool skipHolidays;

  const ReminderSettings({
    this.enabled = true,
    this.frequency = ReminderFrequency.weekdays,
    this.reminderTime = const TimeOfDay(hour: 20, minute: 0),
    this.reminderDays = const [1, 2, 3, 4, 5], // Пн-Пт за замовчуванням
    this.reminderTypes = const [NotificationType.inApp],
    this.skipHolidays = true,
  });

  /// Перетворення в JSON
  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'frequency': frequency.toString().split('.').last,
      'reminderTimeHour': reminderTime.hour,
      'reminderTimeMinute': reminderTime.minute,
      'reminderDays': reminderDays,
      'reminderTypes':
          reminderTypes.map((type) => type.toString().split('.').last).toList(),
      'skipHolidays': skipHolidays,
    };
  }

  /// Створення з JSON
  factory ReminderSettings.fromJson(Map<String, dynamic> json) {
    return ReminderSettings(
      enabled: json['enabled'] ?? true,
      frequency: ReminderFrequency.values.firstWhere(
        (e) => e.toString().split('.').last == json['frequency'],
        orElse: () => ReminderFrequency.weekdays,
      ),
      reminderTime: TimeOfDay(
        hour: json['reminderTimeHour'] ?? 20,
        minute: json['reminderTimeMinute'] ?? 0,
      ),
      reminderDays:
          (json['reminderDays'] as List<dynamic>?)
              ?.map((day) => day as int)
              .toList() ??
          [1, 2, 3, 4, 5],
      reminderTypes:
          (json['reminderTypes'] as List<dynamic>?)
              ?.map(
                (type) => NotificationType.values.firstWhere(
                  (e) => e.toString().split('.').last == type,
                  orElse: () => NotificationType.inApp,
                ),
              )
              .toList() ??
          [NotificationType.inApp],
      skipHolidays: json['skipHolidays'] ?? true,
    );
  }

  /// Серіалізація в рядок
  String toJsonString() => jsonEncode(toJson());

  /// Десеріалізація з рядка
  static ReminderSettings fromJsonString(String jsonString) {
    return ReminderSettings.fromJson(jsonDecode(jsonString));
  }

  /// Створення копії з оновленими полями
  ReminderSettings copyWith({
    bool? enabled,
    ReminderFrequency? frequency,
    TimeOfDay? reminderTime,
    List<int>? reminderDays,
    List<NotificationType>? reminderTypes,
    bool? skipHolidays,
  }) {
    return ReminderSettings(
      enabled: enabled ?? this.enabled,
      frequency: frequency ?? this.frequency,
      reminderTime: reminderTime ?? this.reminderTime,
      reminderDays: reminderDays ?? this.reminderDays,
      reminderTypes: reminderTypes ?? this.reminderTypes,
      skipHolidays: skipHolidays ?? this.skipHolidays,
    );
  }
}
