# Проверка установки Flutter
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Проверка установки Flutter" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Проверяем доступность flutter команды
try {
    $flutterVersion = flutter --version 2>&1
    Write-Host "✅ Flutter команда доступна!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Версия Flutter:" -ForegroundColor Cyan
    Write-Host $flutterVersion -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "❌ Flutter команда не найдена!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Возможные решения:" -ForegroundColor Yellow
    Write-Host "1. Запустите .\setup_flutter.ps1" -ForegroundColor White
    Write-Host "2. Перезапустите PowerShell" -ForegroundColor White
    Write-Host "3. Проверьте, что Flutter правильно распакован" -ForegroundColor White
    Write-Host ""
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

# Запускаем flutter doctor
Write-Host "Диагностика Flutter:" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
try {
    flutter doctor
} catch {
    Write-Host "❌ Ошибка при выполнении flutter doctor" -ForegroundColor Red
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Проверяем доступные устройства
Write-Host "Доступные устройства:" -ForegroundColor Cyan
try {
    flutter devices
} catch {
    Write-Host "❌ Ошибка при получении списка устройств" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Проверка завершена!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Нажмите Enter для выхода"
