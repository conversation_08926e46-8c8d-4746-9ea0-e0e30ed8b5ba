import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';

/// Екран редагування профілю
class ProfileEditScreen extends StatefulWidget {
  final Employee employee;

  const ProfileEditScreen({super.key, required this.employee});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  // Контролери для полів форми
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _positionController = TextEditingController();
  final TextEditingController _hourlyRateController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _experienceYearsController =
      TextEditingController();
  final TextEditingController _qualificationLevelController =
      TextEditingController();

  // Дата початку роботи
  DateTime? _hireDate;

  // Ключ форми для валідації
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Сервіси
  late DatabaseService _databaseService;

  // Стан
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Ініціалізація сервісів
    _databaseService = Provider.of<DatabaseService>(context, listen: false);

    // Заповнення полів форми
    _firstNameController.text = widget.employee.firstName;
    _lastNameController.text = widget.employee.lastName;
    _positionController.text = widget.employee.position;
    _hourlyRateController.text = widget.employee.hourlyRate.toString();
    _emailController.text = widget.employee.email;
    _experienceYearsController.text =
        widget.employee.experienceYears?.toString() ?? '';
    _qualificationLevelController.text =
        widget.employee.qualificationLevel?.toString() ?? '';
    _hireDate = widget.employee.hireDate;
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _positionController.dispose();
    _hourlyRateController.dispose();
    _emailController.dispose();
    _experienceYearsController.dispose();
    _qualificationLevelController.dispose();
    super.dispose();
  }

  /// Збереження змін
  Future<void> _saveChanges() async {
    // Валідація форми
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Парсинг ставки за годину
      final hourlyRate = double.tryParse(_hourlyRateController.text);
      if (hourlyRate == null || hourlyRate <= 0) {
        setState(() {
          _errorMessage = 'Некоректна ставка за годину';
        });
        return;
      }

      // Парсинг стажу та кваліфікації
      int? experienceYears;
      if (_experienceYearsController.text.isNotEmpty) {
        experienceYears = int.tryParse(_experienceYearsController.text);
        if (experienceYears == null || experienceYears < 0) {
          setState(() {
            _errorMessage = 'Некоректний стаж роботи';
          });
          return;
        }
      }

      int? qualificationLevel;
      if (_qualificationLevelController.text.isNotEmpty) {
        qualificationLevel = int.tryParse(_qualificationLevelController.text);
        if (qualificationLevel == null ||
            qualificationLevel < 1 ||
            qualificationLevel > 5) {
          setState(() {
            _errorMessage = 'Рівень кваліфікації повинен бути від 1 до 5';
          });
          return;
        }
      }

      // Створення оновленого працівника
      final updatedEmployee = widget.employee.copyWith(
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        position: _positionController.text,
        hourlyRate: hourlyRate,
        email: _emailController.text,
        hireDate: _hireDate,
        experienceYears: experienceYears,
        qualificationLevel: qualificationLevel,
      );

      // Збереження змін
      await _databaseService.updateEmployee(updatedEmployee);

      // Оновлення стану додатку
      if (!mounted) return;
      Provider.of<AppState>(
        context,
        listen: false,
      ).setCurrentEmployee(updatedEmployee);

      // Повернення на попередній екран
      if (!mounted) return;
      Navigator.pop(context, true);
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка збереження змін: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('edit_profile')),
        actions: [
          // Кнопка збереження
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveChanges,
            tooltip: localizations.translate('save'),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Ім'я
                      TextFormField(
                        controller: _firstNameController,
                        decoration: InputDecoration(
                          labelText: localizations.translate('first_name'),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return localizations.translate('required_field');
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Прізвище
                      TextFormField(
                        controller: _lastNameController,
                        decoration: InputDecoration(
                          labelText: localizations.translate('last_name'),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return localizations.translate('required_field');
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Посада
                      TextFormField(
                        controller: _positionController,
                        decoration: InputDecoration(
                          labelText: localizations.translate('position'),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return localizations.translate('required_field');
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Ставка за годину
                      TextFormField(
                        controller: _hourlyRateController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: localizations.translate('hourly_rate'),
                          suffixText: 'грн',
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return localizations.translate('required_field');
                          }

                          final hourlyRate = double.tryParse(value);
                          if (hourlyRate == null || hourlyRate <= 0) {
                            return localizations.translate(
                              'invalid_hourly_rate',
                            );
                          }

                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Email
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        decoration: InputDecoration(
                          labelText: 'Email',
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return localizations.translate('required_field');
                          }

                          if (!RegExp(
                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                          ).hasMatch(value)) {
                            return localizations.translate('invalid_email');
                          }

                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Дата початку роботи
                      ListTile(
                        title: Text(localizations.translate('hire_date')),
                        subtitle:
                            _hireDate != null
                                ? Text(
                                  DateFormat('dd.MM.yyyy').format(_hireDate!),
                                )
                                : Text(
                                  localizations.translate('not_specified'),
                                ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (_hireDate != null)
                              IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  setState(() {
                                    _hireDate = null;
                                  });
                                },
                              ),
                            const Icon(Icons.calendar_today),
                          ],
                        ),
                        onTap: () async {
                          final DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: _hireDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime.now(),
                          );
                          if (picked != null) {
                            setState(() {
                              _hireDate = picked;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Стаж роботи
                      TextFormField(
                        controller: _experienceYearsController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: localizations.translate(
                            'experience_years',
                          ),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final years = int.tryParse(value);
                            if (years == null || years < 0) {
                              return localizations.translate(
                                'invalid_experience',
                              );
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Рівень кваліфікації
                      TextFormField(
                        controller: _qualificationLevelController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: localizations.translate(
                            'qualification_level',
                          ),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final level = int.tryParse(value);
                            if (level == null || level < 1 || level > 5) {
                              return localizations.translate(
                                'invalid_qualification',
                              );
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Повідомлення про помилку
                      if (_errorMessage != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
    );
  }
}
