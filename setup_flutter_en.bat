@echo off
chcp 65001 >nul
echo ========================================
echo Flutter PATH Setup
echo ========================================
echo.

REM Check where Flutter is installed
set FLUTTER_PATH=C:\flutter\bin

if not exist "%FLUTTER_PATH%\flutter.exe" (
    echo Flutter not found in %FLUTTER_PATH%
    echo.
    echo Please specify correct Flutter path:
    set /p FLUTTER_PATH="Enter path (e.g., C:\flutter\bin): "
)

if not exist "%FLUTTER_PATH%\flutter.exe" (
    echo Flutter still not found!
    echo Make sure Flutter is properly extracted.
    pause
    exit /b 1
)

echo Flutter found: %FLUTTER_PATH%
echo.

REM Add to PATH for current session
set PATH=%FLUTTER_PATH%;%PATH%

REM Add to system PATH (requires admin rights)
echo Adding Flutter to system PATH...
setx PATH "%FLUTTER_PATH%;%PATH%" /M 2>nul

if %errorlevel% neq 0 (
    echo Could not add to system PATH (need admin rights)
    echo Adding to user PATH...
    setx PATH "%FLUTTER_PATH%;%PATH%"
)

echo.
echo Flutter added to PATH!
echo.
echo Restart command prompt and run:
echo flutter doctor
echo.
pause
