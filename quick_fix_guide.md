# Швидке виправлення без перевстановлення Flutter

## 1. Знайти існуючий виконуваний файл

Якщо додаток раніше працював, можливо є скомпільований .exe файл:

```
tabel_app\build\windows\runner\Release\tabel_app.exe
```

Спробуйте запустити його напряму.

## 2. Очистити дані додатку

### Windows:
1. Натисніть `Win + R`
2. Введіть `%APPDATA%`
3. Знайдіть папку з назвою вашого додатку
4. Видаліть файл `timesheet.db`

### Альтернативно:
```
C:\Users\<USER>\AppData\Roaming\[НазваДодатку]\timesheet.db
```

## 3. Перезапустити додаток

Після видалення бази даних, додаток створить нову чисту базу.

## 4. Якщо є помилки з DLL

Можливо потрібно встановити:
- Visual C++ Redistributable
- .NET Framework

## 5. Портативна версія Flutter

Можна завантажити портативну версію Flutter:
1. Завантажте Flutter ZIP
2. Розпакуйте в будь-яку папку
3. Відкрийте командний рядок в папці проекту
4. Виконайте: `[шлях_до_flutter]\bin\flutter run`

## 6. Використання веб-версії

Створіть простий HTML файл для запуску:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Tabel App</title>
</head>
<body>
    <h1>Tabel App - Веб версія</h1>
    <p>Для повного функціоналу потрібен Flutter SDK</p>
    <a href="https://flutter.dev">Завантажити Flutter</a>
</body>
</html>
```

## 7. Перевірка системних вимог

Переконайтеся, що встановлено:
- Windows 10 або новіша
- Visual C++ Redistributable
- Достатньо місця на диску (мінімум 1.64 GB для Flutter)

## 8. Альтернативні IDE

Якщо є проблеми з командним рядком:
- Android Studio з Flutter плагіном
- VS Code з Flutter розширенням
- IntelliJ IDEA з Flutter плагіном
