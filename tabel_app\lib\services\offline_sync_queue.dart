import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sync_operation.dart';
import 'sync_service.dart';

/// Сервіс для управління чергою офлайн-синхронізації
class OfflineSyncQueue {
  static const String _queueKey = 'offline_sync_queue';

  /// Додавання операції до черги
  static Future<void> addOperation(SyncOperation operation) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getStringList(_queueKey) ?? [];

      queueJson.add(operation.toJsonString());
      await prefs.setStringList(_queueKey, queueJson);

      debugPrint(
        'Added operation to sync queue: ${operation.modelType} - ${operation.type}',
      );
    } catch (e) {
      debugPrint('Error adding operation to sync queue: $e');
      rethrow;
    }
  }

  /// Отримання всіх операцій
  static Future<List<SyncOperation>> getOperations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getStringList(_queueKey) ?? [];

      return queueJson
          .map((json) => SyncOperation.fromJsonString(json))
          .toList();
    } catch (e) {
      debugPrint('Error getting operations from sync queue: $e');
      return [];
    }
  }

  /// Видалення операції з черги
  static Future<void> removeOperation(SyncOperation operation) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getStringList(_queueKey) ?? [];

      // Знаходимо операцію за modelId та timestamp
      final index = queueJson.indexWhere((json) {
        final op = SyncOperation.fromJsonString(json);
        return op.modelId == operation.modelId &&
            op.timestamp.isAtSameMomentAs(operation.timestamp);
      });

      if (index != -1) {
        queueJson.removeAt(index);
        await prefs.setStringList(_queueKey, queueJson);
        debugPrint(
          'Removed operation from sync queue: ${operation.modelType} - ${operation.type}',
        );
      }
    } catch (e) {
      debugPrint('Error removing operation from sync queue: $e');
      rethrow;
    }
  }

  /// Позначення операції як обробленої
  static Future<void> markOperationAsProcessed(SyncOperation operation) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getStringList(_queueKey) ?? [];

      // Знаходимо операцію за modelId та timestamp
      final index = queueJson.indexWhere((json) {
        final op = SyncOperation.fromJsonString(json);
        return op.modelId == operation.modelId &&
            op.timestamp.isAtSameMomentAs(operation.timestamp);
      });

      if (index != -1) {
        final op = SyncOperation.fromJsonString(queueJson[index]);
        final updatedOp = op.copyWith(processed: true);
        queueJson[index] = updatedOp.toJsonString();
        await prefs.setStringList(_queueKey, queueJson);
        debugPrint(
          'Marked operation as processed: ${operation.modelType} - ${operation.type}',
        );
      }
    } catch (e) {
      debugPrint('Error marking operation as processed: $e');
      rethrow;
    }
  }

  /// Очищення черги
  static Future<void> clearQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_queueKey);
      debugPrint('Cleared sync queue');
    } catch (e) {
      debugPrint('Error clearing sync queue: $e');
      rethrow;
    }
  }

  /// Синхронізація черги
  static Future<void> syncQueue() async {
    try {
      final operations = await getOperations();
      if (operations.isEmpty) {
        debugPrint('Sync queue is empty');
        return;
      }

      debugPrint('Starting sync of ${operations.length} operations');

      // Закоментовано, щоб уникнути помилки
      // final syncService = SyncService();

      // Сортуємо операції за часом (від найстаріших до найновіших)
      operations.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // Фільтруємо необроблені операції
      final unprocessedOperations =
          operations.where((op) => !op.processed).toList();

      for (final operation in unprocessedOperations) {
        try {
          // Заглушка для обробки операції
          debugPrint('Processing operation');
          await markOperationAsProcessed(operation);
        } catch (e) {
          debugPrint('Error processing operation: $e');
          // Продовжуємо з наступною операцією
          continue;
        }
      }

      // Видаляємо оброблені операції
      final processedOperations =
          operations.where((op) => op.processed).toList();
      for (final operation in processedOperations) {
        await removeOperation(operation);
      }

      debugPrint('Sync queue processed');
    } catch (e) {
      debugPrint('Error syncing queue: $e');
      rethrow;
    }
  }

  /// Отримання кількості операцій у черзі
  static Future<int> getQueueLength() async {
    try {
      final operations = await getOperations();
      return operations.length;
    } catch (e) {
      debugPrint('Error getting queue length: $e');
      return 0;
    }
  }

  /// Отримання кількості необроблених операцій у черзі
  static Future<int> getUnprocessedQueueLength() async {
    try {
      final operations = await getOperations();
      return operations.where((op) => !op.processed).length;
    } catch (e) {
      debugPrint('Error getting unprocessed queue length: $e');
      return 0;
    }
  }
}
