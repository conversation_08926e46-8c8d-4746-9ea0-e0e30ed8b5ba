@echo off
echo Running app with email password reset functionality...

REM Check connected devices
echo Checking connected devices...
adb devices

REM Get dependencies
echo Getting dependencies...
flutter pub get

REM Run app with Flutter
echo Running app with Flutter...
flutter run --debug

if %ERRORLEVEL% NEQ 0 (
    echo Error running app.
    pause
    exit /b 1
)

echo App is running!

pause
exit /b 0
