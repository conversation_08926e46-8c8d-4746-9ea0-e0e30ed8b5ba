{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Tabel\\tabel_app\\android\\app\\.cxx\\RelWithDebInfo\\3d2m1r2y\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Tabel\\tabel_app\\android\\app\\.cxx\\RelWithDebInfo\\3d2m1r2y\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\AndroidSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\AndroidSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}