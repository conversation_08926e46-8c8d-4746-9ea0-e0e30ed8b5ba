﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}"
	ProjectSection(ProjectDependencies) = postProject
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A} = {3E740C1F-9542-3D9F-A252-D6847AC2C43A}
		{EA99AD1A-E45C-3F57-96F4-1F304620B641} = {EA99AD1A-E45C-3F57-96F4-1F304620B641}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{E9D3F97F-AD0E-3C91-A5FB-E1C0BB078339}"
	ProjectSection(ProjectDependencies) = postProject
		{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C} = {F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A} = {3E740C1F-9542-3D9F-A252-D6847AC2C43A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{3E740C1F-9542-3D9F-A252-D6847AC2C43A}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{E86031CC-FC74-3190-88B4-AC3A26CD581F}"
	ProjectSection(ProjectDependencies) = postProject
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A} = {3E740C1F-9542-3D9F-A252-D6847AC2C43A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}"
	ProjectSection(ProjectDependencies) = postProject
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A} = {3E740C1F-9542-3D9F-A252-D6847AC2C43A}
		{E86031CC-FC74-3190-88B4-AC3A26CD581F} = {E86031CC-FC74-3190-88B4-AC3A26CD581F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "printing_plugin", "printing_plugin.vcxproj", "{EA99AD1A-E45C-3F57-96F4-1F304620B641}"
	ProjectSection(ProjectDependencies) = postProject
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A} = {3E740C1F-9542-3D9F-A252-D6847AC2C43A}
		{E86031CC-FC74-3190-88B4-AC3A26CD581F} = {E86031CC-FC74-3190-88B4-AC3A26CD581F}
		{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221} = {FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}.Debug|x64.ActiveCfg = Debug|x64
		{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}.Debug|x64.Build.0 = Debug|x64
		{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}.Profile|x64.ActiveCfg = Profile|x64
		{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}.Profile|x64.Build.0 = Profile|x64
		{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}.Release|x64.ActiveCfg = Release|x64
		{F4D95F8B-AB24-30D7-9C6C-49E28B51C91C}.Release|x64.Build.0 = Release|x64
		{E9D3F97F-AD0E-3C91-A5FB-E1C0BB078339}.Debug|x64.ActiveCfg = Debug|x64
		{E9D3F97F-AD0E-3C91-A5FB-E1C0BB078339}.Profile|x64.ActiveCfg = Profile|x64
		{E9D3F97F-AD0E-3C91-A5FB-E1C0BB078339}.Release|x64.ActiveCfg = Release|x64
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A}.Debug|x64.ActiveCfg = Debug|x64
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A}.Debug|x64.Build.0 = Debug|x64
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A}.Profile|x64.ActiveCfg = Profile|x64
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A}.Profile|x64.Build.0 = Profile|x64
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A}.Release|x64.ActiveCfg = Release|x64
		{3E740C1F-9542-3D9F-A252-D6847AC2C43A}.Release|x64.Build.0 = Release|x64
		{E86031CC-FC74-3190-88B4-AC3A26CD581F}.Debug|x64.ActiveCfg = Debug|x64
		{E86031CC-FC74-3190-88B4-AC3A26CD581F}.Debug|x64.Build.0 = Debug|x64
		{E86031CC-FC74-3190-88B4-AC3A26CD581F}.Profile|x64.ActiveCfg = Profile|x64
		{E86031CC-FC74-3190-88B4-AC3A26CD581F}.Profile|x64.Build.0 = Profile|x64
		{E86031CC-FC74-3190-88B4-AC3A26CD581F}.Release|x64.ActiveCfg = Release|x64
		{E86031CC-FC74-3190-88B4-AC3A26CD581F}.Release|x64.Build.0 = Release|x64
		{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}.Debug|x64.ActiveCfg = Debug|x64
		{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}.Debug|x64.Build.0 = Debug|x64
		{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}.Profile|x64.ActiveCfg = Profile|x64
		{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}.Profile|x64.Build.0 = Profile|x64
		{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}.Release|x64.ActiveCfg = Release|x64
		{FC5A7A91-ACC2-35ED-9EBE-FD7ED75B7221}.Release|x64.Build.0 = Release|x64
		{EA99AD1A-E45C-3F57-96F4-1F304620B641}.Debug|x64.ActiveCfg = Debug|x64
		{EA99AD1A-E45C-3F57-96F4-1F304620B641}.Debug|x64.Build.0 = Debug|x64
		{EA99AD1A-E45C-3F57-96F4-1F304620B641}.Profile|x64.ActiveCfg = Profile|x64
		{EA99AD1A-E45C-3F57-96F4-1F304620B641}.Profile|x64.Build.0 = Profile|x64
		{EA99AD1A-E45C-3F57-96F4-1F304620B641}.Release|x64.ActiveCfg = Release|x64
		{EA99AD1A-E45C-3F57-96F4-1F304620B641}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2B511794-2522-3F41-860B-5933B58466A5}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
