// import 'package:firebase_auth/firebase_auth.dart';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';
import 'database_service.dart';
import 'email_service.dart';

/// Сервіс для аутентифікації
class AuthService {
  // final FirebaseAuth _auth = FirebaseAuth.instance;
  final DatabaseService _databaseService = DatabaseService();

  /// Поточний користувач Firebase
  // User? get currentUser => _auth.currentUser;
  String? get currentUser => null;

  /// Потік змін стану аутентифікації
  // Stream<User?> get authStateChanges => _auth.authStateChanges();
  Stream<String?> get authStateChanges => Stream.value(null);

  /// Реєстрація нового користувача
  Future<Employee> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required double hourlyRate,
    required String position,
    bool isAdmin = false,
  }) async {
    try {
      // Створення користувача в Firebase - вимкнено для Windows
      // final UserCredential result = await _auth.createUserWithEmailAndPassword(
      //   email: email,
      //   password: password,
      // );

      // Створення працівника в локальній базі даних
      final Employee employee = Employee(
        // id: result.user!.uid,
        firstName: firstName,
        lastName: lastName,
        hourlyRate: hourlyRate,
        position: position,
        email: email,
        isAdmin: isAdmin,
      );

      await _databaseService.insertEmployee(employee);

      return employee;
    } catch (e) {
      rethrow;
    }
  }

  /// Вхід за допомогою email та пароля
  Future<Employee?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // Вхід в Firebase - вимкнено для Windows
      // final UserCredential result = await _auth.signInWithEmailAndPassword(
      //   email: email,
      //   password: password,
      // );

      // Отримання працівника з локальної бази даних
      final Employee? employee = await _databaseService.getEmployeeByEmail(
        email,
      );

      // Збереження інформації про поточного користувача
      if (employee != null) {
        await _saveCurrentUser(employee);
      }

      return employee;
    } catch (e) {
      rethrow;
    }
  }

  /// Вихід з системи
  Future<void> signOut() async {
    try {
      // Вихід з Firebase - вимкнено для Windows
      // await _auth.signOut();

      // Видалення інформації про поточного користувача
      await _clearCurrentUser();
    } catch (e) {
      rethrow;
    }
  }

  /// Зміна пароля
  Future<void> changePassword(String newPassword) async {
    try {
      // await _auth.currentUser?.updatePassword(newPassword);
      // Вимкнено для Windows
      return;
    } catch (e) {
      rethrow;
    }
  }

  /// Коди підтвердження для скидання пароля
  final Map<String, String> _resetCodes = {};

  /// Час дії коду підтвердження (в хвилинах)
  static const int _resetCodeExpirationMinutes = 30;

  /// Час створення коду підтвердження
  final Map<String, DateTime> _resetCodeCreationTime = {};

  /// Сервіс для відправки електронних листів
  final EmailService _emailService = EmailService();

  /// Скидання пароля - генерація коду підтвердження та відправка на email
  Future<void> resetPassword(String email) async {
    try {
      // Перевірка, чи існує користувач з таким email
      final employee = await _databaseService.getEmployeeByEmail(email);
      if (employee == null) {
        throw Exception('Користувача з таким email не знайдено');
      }

      // Генерація випадкового коду підтвердження
      final code = _generateResetCode();

      // Збереження коду та часу його створення
      _resetCodes[email] = code;
      _resetCodeCreationTime[email] = DateTime.now();

      // Отримання поточної мови
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString('languageCode') ?? 'uk';

      // Відправка коду на email
      final success = await _emailService.sendPasswordResetCode(
        email: email,
        resetCode: code,
        language: languageCode,
      );

      if (!success) {
        throw Exception('Помилка відправки коду підтвердження');
      }

      return;
    } catch (e) {
      rethrow;
    }
  }

  /// Перевірка коду підтвердження та зміна пароля
  Future<void> verifyResetCodeAndChangePassword(
    String email,
    String code,
    String newPassword,
  ) async {
    try {
      // Перевірка наявності коду для цього email
      if (!_resetCodes.containsKey(email)) {
        throw Exception('Спочатку потрібно запросити скидання пароля');
      }

      // Перевірка часу дії коду
      final creationTime = _resetCodeCreationTime[email];
      if (creationTime == null) {
        throw Exception('Спочатку потрібно запросити скидання пароля');
      }

      final now = DateTime.now();
      final difference = now.difference(creationTime).inMinutes;
      if (difference > _resetCodeExpirationMinutes) {
        // Видалення простроченого коду
        _resetCodes.remove(email);
        _resetCodeCreationTime.remove(email);
        throw Exception('Код підтвердження прострочений. Запросіть новий код');
      }

      // Перевірка коду
      final savedCode = _resetCodes[email];
      if (code != savedCode) {
        throw Exception('Невірний код підтвердження');
      }

      // Отримання користувача за email
      final employee = await _databaseService.getEmployeeByEmail(email);
      if (employee == null) {
        throw Exception('Користувача з таким email не знайдено');
      }

      // Зміна пароля користувача
      // В реальному додатку тут потрібно оновити пароль у базі даних

      // Видалення коду після успішної зміни пароля
      _resetCodes.remove(email);
      _resetCodeCreationTime.remove(email);

      return;
    } catch (e) {
      rethrow;
    }
  }

  /// Генерація випадкового коду підтвердження
  String _generateResetCode() {
    // Генерація 6-значного коду
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  /// Отримання поточного працівника
  Future<Employee?> getCurrentEmployee() async {
    try {
      // Спочатку перевіряємо локальне сховище
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // Перевіряємо флаг авторизації
      final isLoggedIn = prefs.getBool('isLoggedIn');

      // Отримуємо ідентифікатор користувача
      final String? employeeId = prefs.getString('currentEmployeeId');
      final String? employeeEmail = prefs.getString('currentEmployeeEmail');

      print('Getting current employee from SharedPreferences:');
      print('ID: $employeeId, Email: $employeeEmail, isLoggedIn: $isLoggedIn');

      // Якщо користувач не авторизований, повертаємо null
      if (isLoggedIn == false) {
        print('User is not logged in according to isLoggedIn flag');
        return null;
      }

      if (employeeId != null) {
        // Отримуємо користувача з бази даних
        final employee = await _databaseService.getEmployee(employeeId);

        if (employee != null) {
          print(
            'Employee found in database: ${employee.fullName} (ID: ${employee.id})',
          );

          // Перевіряємо, чи збігається email
          if (employeeEmail != null && employee.email != employeeEmail) {
            print(
              'Warning: Email mismatch! Stored: $employeeEmail, DB: ${employee.email}',
            );
          }

          return employee;
        } else {
          print('Employee with ID $employeeId not found in database');

          // Якщо користувача немає в базі, але є email, спробуємо знайти за email
          if (employeeEmail != null) {
            final employeeByEmail = await _databaseService.getEmployeeByEmail(
              employeeEmail,
            );
            if (employeeByEmail != null) {
              print(
                'Found employee by email: ${employeeByEmail.fullName} (ID: ${employeeByEmail.id})',
              );

              // Оновлюємо ідентифікатор в SharedPreferences
              await _saveCurrentUser(employeeByEmail);
              return employeeByEmail;
            }
          }

          // Якщо користувача не знайдено, видаляємо збережені дані
          await _clearCurrentUser();
          return null;
        }
      } else if (employeeEmail != null) {
        // Якщо немає ідентифікатора, але є email, спробуємо знайти за email
        print(
          'No ID found, but email exists: $employeeEmail. Trying to find by email...',
        );
        final employeeByEmail = await _databaseService.getEmployeeByEmail(
          employeeEmail,
        );

        if (employeeByEmail != null) {
          print(
            'Found employee by email: ${employeeByEmail.fullName} (ID: ${employeeByEmail.id})',
          );

          // Оновлюємо ідентифікатор в SharedPreferences
          await _saveCurrentUser(employeeByEmail);
          return employeeByEmail;
        }
      }
    } catch (e) {
      print('Error getting current employee: $e');
    }

    // Якщо в локальному сховищі немає інформації, перевіряємо Firebase - вимкнено для Windows
    // final User? user = _auth.currentUser;
    // if (user != null) {
    //   final Employee? employee = await _databaseService.getEmployeeByEmail(user.email!);
    //
    //   // Зберігаємо інформацію про поточного користувача
    //   if (employee != null) {
    //     await _saveCurrentUser(employee);
    //   }
    //
    //   return employee;
    // }

    return null;
  }

  /// Збереження інформації про поточного користувача
  Future<void> _saveCurrentUser(Employee employee) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // Зберігаємо ідентифікатор користувача
      await prefs.setString('currentEmployeeId', employee.id);

      // Зберігаємо також email для додаткової перевірки
      await prefs.setString('currentEmployeeEmail', employee.email);

      // Зберігаємо ім'я та прізвище
      await prefs.setString('currentEmployeeFirstName', employee.firstName);
      await prefs.setString('currentEmployeeLastName', employee.lastName);

      // Зберігаємо флаг авторизації
      await prefs.setBool('isLoggedIn', true);

      // Перевіряємо, що дані збереглись
      final savedId = prefs.getString('currentEmployeeId');
      final savedEmail = prefs.getString('currentEmployeeEmail');
      final isLoggedIn = prefs.getBool('isLoggedIn');

      // Додаємо виведення інформації про збереження користувача
      print(
        'Current employee saved: ${employee.fullName} (ID: ${employee.id})',
      );
      print(
        'Verification - Saved ID: $savedId, Email: $savedEmail, isLoggedIn: $isLoggedIn',
      );
    } catch (e) {
      print('Error saving current user: $e');
    }
  }

  /// Видалення інформації про поточного користувача
  Future<void> _clearCurrentUser() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // Зберігаємо ідентифікатор перед видаленням для логування
      final String? employeeId = prefs.getString('currentEmployeeId');
      final String? employeeEmail = prefs.getString('currentEmployeeEmail');

      // Видаляємо всі дані користувача
      await prefs.remove('currentEmployeeId');
      await prefs.remove('currentEmployeeEmail');
      await prefs.remove('currentEmployeeFirstName');
      await prefs.remove('currentEmployeeLastName');
      await prefs.setBool('isLoggedIn', false);

      // Перевіряємо, що дані видалені
      final isLoggedIn = prefs.getBool('isLoggedIn');
      final idAfterClear = prefs.getString('currentEmployeeId');

      // Додаємо виведення інформації про видалення користувача
      print(
        'Current employee cleared, ID was: $employeeId, Email was: $employeeEmail',
      );
      print(
        'Verification after clear - ID: $idAfterClear, isLoggedIn: $isLoggedIn',
      );
    } catch (e) {
      print('Error clearing current user: $e');
    }
  }
}
