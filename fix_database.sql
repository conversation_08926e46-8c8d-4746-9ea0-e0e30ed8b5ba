-- Исправление структуры базы данных Tabel App
-- Добавление недостающих колонок и таблиц

-- 1. Добавляем колонку shiftId в таблицу workDays
ALTER TABLE workDays ADD COLUMN shiftId TEXT;

-- 2. Создаем таблицу shifts (если не существует)
CREATE TABLE IF NOT EXISTS shifts (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    startTimeHour INTEGER NOT NULL,
    startTimeMinute INTEGER NOT NULL,
    endTimeHour INTEGER NOT NULL,
    endTimeMinute INTEGER NOT NULL,
    colorValue INTEGER NOT NULL,
    isNightShift INTEGER NOT NULL
);

-- 3. Создаем таблицу shiftSchedules (если не существует)
CREATE TABLE IF NOT EXISTS shiftSchedules (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    shiftOrder TEXT NOT NULL,
    currentShiftIndex INTEGER NOT NULL,
    startDate INTEGER NOT NULL,
    dayOff1 INTEGER NOT NULL,
    dayOff2 INTEGER,
    hasFloatingDaysOff INTEGER NOT NULL,
    workDaysCount INTEGER NOT NULL DEFAULT 5,
    daysOffCount INTEGER NOT NULL DEFAULT 2
);

-- 4. Создаем таблицу employeeShiftSchedules (если не существует)
CREATE TABLE IF NOT EXISTS employeeShiftSchedules (
    id TEXT PRIMARY KEY,
    employeeId TEXT NOT NULL,
    shiftScheduleId TEXT NOT NULL,
    FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE,
    FOREIGN KEY (shiftScheduleId) REFERENCES shiftSchedules (id) ON DELETE CASCADE
);

-- 5. Проверяем структуру таблиц
.schema workDays
.schema shifts
.schema shiftSchedules
.schema employeeShiftSchedules
