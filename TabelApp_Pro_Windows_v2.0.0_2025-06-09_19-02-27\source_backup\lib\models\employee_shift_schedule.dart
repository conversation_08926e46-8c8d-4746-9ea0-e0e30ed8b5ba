import 'package:uuid/uuid.dart';

/// Модель для зв'язку працівника з графіком змін
class EmployeeShiftSchedule {
  /// Унікальний ідентифікатор зв'язку
  final String id;

  /// Ідентифікатор працівника
  final String employeeId;

  /// Ідентифікатор графіку змін
  final String shiftScheduleId;

  /// Конструктор
  EmployeeShiftSchedule({
    String? id,
    required this.employeeId,
    required this.shiftScheduleId,
  }) : id = id ?? const Uuid().v4();

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'shiftScheduleId': shiftScheduleId,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory EmployeeShiftSchedule.fromMap(Map<String, dynamic> map) {
    return EmployeeShiftSchedule(
      id: map['id'],
      employeeId: map['employeeId'],
      shiftScheduleId: map['shiftScheduleId'],
    );
  }
}
