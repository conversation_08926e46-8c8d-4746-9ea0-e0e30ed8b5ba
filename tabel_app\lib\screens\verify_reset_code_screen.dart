import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import 'login_screen.dart';

/// Екран для введення коду підтвердження та нового пароля
class VerifyResetCodeScreen extends StatefulWidget {
  /// Email користувача
  final String email;

  const VerifyResetCodeScreen({super.key, required this.email});

  @override
  State<VerifyResetCodeScreen> createState() => _VerifyResetCodeScreenState();
}

class _VerifyResetCodeScreenState extends State<VerifyResetCodeScreen> {
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _resetSuccess = false;

  @override
  void dispose() {
    _codeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Перевірка коду підтвердження та зміна пароля
  Future<void> _verifyCodeAndChangePassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _resetSuccess = false;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      await authService.verifyResetCodeAndChangePassword(
        widget.email,
        _codeController.text.trim(),
        _passwordController.text,
      );

      setState(() {
        _resetSuccess = true;
      });

      // Затримка перед переходом на екран входу
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
            (route) => false,
          );
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка зміни пароля: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations.translate('verify_reset_code'))),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Інформаційний текст
              Text(
                localizations.translate('verify_reset_code_info'),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              // Інформація про відправку коду на email
              Card(
                color: Theme.of(context).colorScheme.primaryContainer,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const Icon(Icons.email, size: 48, color: Colors.blue),
                      const SizedBox(height: 16),
                      Text(
                        localizations.translate('code_sent_to_email'),
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        widget.email,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              // Поле для введення коду підтвердження
              TextFormField(
                controller: _codeController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: localizations.translate('reset_code'),
                  prefixIcon: const Icon(Icons.security),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для введення нового пароля
              TextFormField(
                controller: _passwordController,
                obscureText: true,
                decoration: InputDecoration(
                  labelText: localizations.translate('new_password'),
                  prefixIcon: const Icon(Icons.lock),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  if (value.length < 6) {
                    return localizations.translate('password_too_short');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для підтвердження нового пароля
              TextFormField(
                controller: _confirmPasswordController,
                obscureText: true,
                decoration: InputDecoration(
                  labelText: localizations.translate('confirm_password'),
                  prefixIcon: const Icon(Icons.lock),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  if (value != _passwordController.text) {
                    return localizations.translate('password_mismatch');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              // Повідомлення про помилку
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ),
              // Повідомлення про успішне скидання пароля
              if (_resetSuccess)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    localizations.translate('password_reset_success'),
                    style: const TextStyle(color: Colors.green),
                    textAlign: TextAlign.center,
                  ),
                ),
              // Кнопка підтвердження
              ElevatedButton(
                onPressed: _isLoading ? null : _verifyCodeAndChangePassword,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isLoading
                        ? const CircularProgressIndicator()
                        : Text(localizations.translate('submit_new_password')),
              ),
              const SizedBox(height: 16),
              // Кнопка повернення на екран входу
              OutlinedButton(
                onPressed: () {
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(
                      builder: (context) => const LoginScreen(),
                    ),
                    (route) => false,
                  );
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(localizations.translate('back_to_login')),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
