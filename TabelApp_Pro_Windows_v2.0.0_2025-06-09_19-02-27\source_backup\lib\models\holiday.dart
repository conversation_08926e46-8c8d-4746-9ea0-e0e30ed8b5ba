import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Модель святкового дня
class Holiday extends Equatable {
  /// Унікальний ідентифікатор
  final String id;
  
  /// Назва свята
  final String name;
  
  /// Дата свята
  final DateTime date;
  
  /// Країна, до якої відноситься свято
  final String country;
  
  /// Чи є свято щорічним
  final bool isRecurring;

  /// Конструктор
  Holiday({
    String? id,
    required this.name,
    required this.date,
    required this.country,
    this.isRecurring = true,
  }) : id = id ?? const Uuid().v4();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  Holiday copyWith({
    String? name,
    DateTime? date,
    String? country,
    bool? isRecurring,
  }) {
    return Holiday(
      id: this.id,
      name: name ?? this.name,
      date: date ?? this.date,
      country: country ?? this.country,
      isRecurring: isRecurring ?? this.isRecurring,
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'date': date.millisecondsSinceEpoch,
      'country': country,
      'isRecurring': isRecurring ? 1 : 0,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory Holiday.fromMap(Map<String, dynamic> map) {
    return Holiday(
      id: map['id'],
      name: map['name'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      country: map['country'],
      isRecurring: map['isRecurring'] == 1,
    );
  }

  @override
  List<Object?> get props => [id, name, date, country, isRecurring];
}
