import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// Модель для зміни
class Shift {
  /// Унікальний ідентифікатор зміни
  final String id;

  /// Назва зміни (наприклад, "Перша зміна", "Нічна зміна" тощо)
  final String name;

  /// Час початку зміни
  final TimeOfDay startTime;

  /// Час закінчення зміни
  final TimeOfDay endTime;

  /// Колір для відображення зміни в календарі
  final Color color;

  /// Чи є зміна нічною (для автоматичного нарахування бонусів)
  final bool isNightShift;

  /// Конструктор
  Shift({
    String? id,
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.color,
    this.isNightShift = false,
  }) : id = id ?? const Uuid().v4();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  Shift copyWith({
    String? name,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    Color? color,
    bool? isNightShift,
  }) {
    return Shift(
      id: this.id,
      name: name ?? this.name,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      color: color ?? this.color,
      isNightShift: isNightShift ?? this.isNightShift,
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'startTimeHour': startTime.hour,
      'startTimeMinute': startTime.minute,
      'endTimeHour': endTime.hour,
      'endTimeMinute': endTime.minute,
      'colorValue': color.value,
      'isNightShift': isNightShift ? 1 : 0,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory Shift.fromMap(Map<String, dynamic> map) {
    return Shift(
      id: map['id'],
      name: map['name'],
      startTime: TimeOfDay(
        hour: map['startTimeHour'],
        minute: map['startTimeMinute'],
      ),
      endTime: TimeOfDay(
        hour: map['endTimeHour'],
        minute: map['endTimeMinute'],
      ),
      color: Color(map['colorValue']),
      isNightShift: map['isNightShift'] == 1,
    );
  }

  /// Розрахунок тривалості зміни в годинах
  double get durationInHours {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    
    // Якщо кінець зміни на наступний день
    final totalMinutes = endMinutes > startMinutes
        ? endMinutes - startMinutes
        : (24 * 60 - startMinutes) + endMinutes;
    
    return totalMinutes / 60.0;
  }

  /// Перевірка, чи перетинає зміна північ
  bool get crossesMidnight {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    
    return endMinutes <= startMinutes;
  }

  /// Форматований час початку зміни
  String get formattedStartTime {
    final hour = startTime.hour.toString().padLeft(2, '0');
    final minute = startTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Форматований час закінчення зміни
  String get formattedEndTime {
    final hour = endTime.hour.toString().padLeft(2, '0');
    final minute = endTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Форматований час зміни (початок - кінець)
  String get formattedTime {
    return '$formattedStartTime - $formattedEndTime';
  }
}
