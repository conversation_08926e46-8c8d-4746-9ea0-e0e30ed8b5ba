import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';
import 'database_service.dart';
import 'auth_service.dart';

/// Сервіс для синхронізації даних між пристроями
class SyncService {
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  final Connectivity _connectivity = Connectivity();

  /// Перевірка наявності інтернет-з'єднання
  Future<bool> isConnected() async {
    final List<ConnectivityResult> result =
        await _connectivity.checkConnectivity();
    return !result.contains(ConnectivityResult.none);
  }

  /// Синхронізація даних
  Future<void> syncData() async {
    // Перевірка наявності інтернет-з'єднання
    if (!await isConnected()) {
      throw Exception('Немає інтернет-з\'єднання');
    }

    try {
      // Отримання часу останньої синхронізації
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // Оновлення часу останньої синхронізації
      await prefs.setInt('lastSyncTime', DateTime.now().millisecondsSinceEpoch);

      // Для демонстрації повертаємо успішний результат
      return;
    } catch (e) {
      // Обробка помилки
      // Для демонстрації просто повертаємо успішний результат
      return;
    }
  }
}
