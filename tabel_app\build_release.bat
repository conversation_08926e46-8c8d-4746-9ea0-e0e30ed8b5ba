@echo off
echo Building Android app release version...

REM Set Java 17 path
set "JAVA_HOME=C:\Java17\java17"
set PATH=%JAVA_HOME%\bin;%PATH%

REM Check Java version
echo Checking Java version:
java -version
echo.

REM Set environment variables for Gradle
set "GRADLE_OPTS=-Xmx4096m -Dorg.gradle.jvmargs=-Xmx4096m -Dorg.gradle.daemon=false"

REM Go to android directory
cd android

REM Build release APK with <PERSON>radle directly
echo Building release APK with Gradle...
call gradlew assembleRelease --info

if %ERRORLEVEL% NEQ 0 (
    echo Error building APK.
    cd ..
    pause
    exit /b 1
)

REM Go back to root directory
cd ..

REM Create output directory if it doesn't exist
if not exist "build\android" mkdir "build\android"

REM Find APK file
echo Finding APK file...
for /f "tokens=*" %%a in ('dir /s /b android\app\build\outputs\apk\release\*.apk') do (
    set APK_PATH=%%a
    echo Found APK: %%a
)

REM Copy APK to output directory
echo Copying APK to output directory...
if defined APK_PATH (
    copy "%APK_PATH%" "build\android\tabel_app-release.apk"
    echo APK successfully copied to build\android\tabel_app-release.apk
) else (
    echo APK file not found.
)

echo APK successfully built!
echo APK file: build\android\tabel_app-release.apk

REM Open output directory
explorer "build\android"

pause
exit /b 0