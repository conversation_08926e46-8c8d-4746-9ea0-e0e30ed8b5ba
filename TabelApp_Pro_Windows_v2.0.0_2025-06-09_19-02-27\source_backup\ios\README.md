# Інструкція з встановлення Tabel App на iOS

## Передумови

Для встановлення додатку на iOS вам знадобиться:

1. Mac з macOS 10.15 або новіше
2. Xcode 12.0 або новіше
3. Apple Developer аккаунт (для підписання додатку)
4. iPhone або iPad з iOS 12.0 або новіше
5. Flutter SDK

## Налаштування проекту

1. Відкрийте файл `ExportOptions.plist` та змініть наступні параметри:
   - `teamID`: Ваш Apple Developer Team ID
   - `com.yourcompany.tabelapp`: Змініть на ваш Bundle Identifier
   - `Tabel App Development Profile`: Змініть на назву вашого Provisioning Profile

2. Відкрийте файл `Runner.xcworkspace` в Xcode:
   ```
   cd ios
   open Runner.xcworkspace
   ```

3. У Xcode налаштуйте підписання додатку:
   - Виберіть проект Runner у навігаторі проектів
   - Виберіть вкладку "Signing & Capabilities"
   - Виберіть ваш Team
   - Переконайтеся, що Bundle Identifier унікальний

## Збірка та встановлення

### Метод 1: Використання скрипту

1. Зробіть скрипт виконуваним:
   ```
   chmod +x build_ios.sh
   ```

2. Запустіть скрипт:
   ```
   ./build_ios.sh
   ```

3. Після успішної збірки, IPA файл буде знаходитися в директорії `build/ios/ipa`

### Метод 2: Ручна збірка через Xcode

1. Відкрийте проект в Xcode:
   ```
   cd ios
   open Runner.xcworkspace
   ```

2. Виберіть реальний пристрій як ціль для збірки (не симулятор)

3. Виберіть меню Product > Archive

4. Після завершення архівування, відкриється вікно Organizer

5. Виберіть архів та натисніть "Distribute App"

6. Виберіть метод розповсюдження "Development"

7. Слідуйте інструкціям на екрані для завершення процесу

## Встановлення на пристрій

### Через iTunes (для старих версій iOS)

1. Підключіть ваш iPhone або iPad до комп'ютера

2. Відкрийте iTunes

3. Перетягніть IPA файл в iTunes

4. Синхронізуйте пристрій

### Через Apple Configurator 2 (рекомендований метод)

1. Встановіть Apple Configurator 2 з Mac App Store

2. Підключіть ваш iPhone або iPad до комп'ютера

3. Відкрийте Apple Configurator 2

4. Перетягніть IPA файл на іконку вашого пристрою

5. Слідуйте інструкціям на екрані

### Через TestFlight (для тестування)

1. Завантажте додаток в App Store Connect

2. Додайте тестувальників в TestFlight

3. Тестувальники отримають запрошення по електронній пошті

4. Тестувальники можуть встановити додаток через додаток TestFlight

## Вирішення проблем

### Помилка підписання

Якщо ви отримуєте помилку підписання, переконайтеся, що:

1. Ваш Apple Developer аккаунт активний
2. Ви правильно налаштували Provisioning Profile
3. Bundle Identifier унікальний
4. Ви вибрали правильний Team в налаштуваннях проекту

### Помилка встановлення

Якщо додаток не встановлюється на пристрій:

1. Переконайтеся, що пристрій зареєстрований у вашому Apple Developer аккаунті
2. Переконайтеся, що на пристрої дозволено встановлення додатків з неперевірених джерел (Settings > General > Device Management)
3. Перевірте, чи відповідає версія iOS мінімальним вимогам (iOS 12.0 або новіше)
