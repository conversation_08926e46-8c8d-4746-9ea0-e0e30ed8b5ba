@echo off
echo ========================================
echo Восстановление проекта Tabel App
echo ========================================
echo.

REM Проверяем Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter не найден!
    echo Сначала установите Flutter и запустите setup_flutter_path.bat
    pause
    exit /b 1
)

REM Проверяем наличие проекта
if not exist "tabel_app\pubspec.yaml" (
    echo ❌ Проект tabel_app не найден!
    echo Убедитесь, что вы находитесь в правильной папке.
    pause
    exit /b 1
)

echo ✅ Flutter найден!
echo ✅ Проект найден!
echo.

REM Переходим в папку проекта
cd tabel_app

REM Очищаем кеш
echo Очищаем кеш...
flutter clean
echo.

REM Удаляем старые файлы сборки
echo Удаляем старые файлы сборки...
if exist "build" rmdir /s /q build
if exist ".dart_tool" rmdir /s /q .dart_tool
echo.

REM Получаем зависимости
echo Получаем зависимости...
flutter pub get
echo.

REM Генерируем код (если нужно)
echo Генерируем код...
flutter packages pub run build_runner build --delete-conflicting-outputs
echo.

REM Проверяем проект
echo Проверяем проект...
flutter analyze
echo.

echo ========================================
echo Проект восстановлен!
echo ========================================
echo.
echo Теперь можно запускать:
echo flutter run -d windows
echo.
pause
