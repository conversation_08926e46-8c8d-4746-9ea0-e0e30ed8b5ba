import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../models/models.dart';
import '../main.dart';
import 'home_screen.dart';

/// Екран реєстрації
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _hourlyRateController = TextEditingController();
  final TextEditingController _positionController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _isAdmin = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _hourlyRateController.dispose();
    _positionController.dispose();
    super.dispose();
  }

  /// Реєстрація нового користувача
  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final employee = await authService.registerWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        hourlyRate: double.parse(_hourlyRateController.text.trim()),
        position: _positionController.text.trim(),
        isAdmin: _isAdmin,
      );

      if (!mounted) return;
      Provider.of<AppState>(
        context,
        listen: false,
      ).setCurrentEmployee(employee);

      // Перехід на головний екран після успішної реєстрації
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const HomeScreen()),
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка реєстрації: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations.translate('register'))),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Поле для введення імені
              TextFormField(
                controller: _firstNameController,
                decoration: InputDecoration(
                  labelText: localizations.translate('first_name'),
                  prefixIcon: const Icon(Icons.person),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для введення прізвища
              TextFormField(
                controller: _lastNameController,
                decoration: InputDecoration(
                  labelText: localizations.translate('last_name'),
                  prefixIcon: const Icon(Icons.person),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для введення посади
              TextFormField(
                controller: _positionController,
                decoration: InputDecoration(
                  labelText: localizations.translate('position'),
                  prefixIcon: const Icon(Icons.work),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для введення ставки за годину
              TextFormField(
                controller: _hourlyRateController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: localizations.translate('hourly_rate'),
                  prefixIcon: const Icon(Icons.attach_money),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  try {
                    final rate = double.parse(value);
                    if (rate <= 0) {
                      return localizations.translate('invalid_hourly_rate');
                    }
                  } catch (e) {
                    return localizations.translate('invalid_hourly_rate');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для введення email
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  labelText: localizations.translate('email'),
                  prefixIcon: const Icon(Icons.email),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return localizations.translate('invalid_email');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для введення пароля
              TextFormField(
                controller: _passwordController,
                obscureText: true,
                decoration: InputDecoration(
                  labelText: localizations.translate('password'),
                  prefixIcon: const Icon(Icons.lock),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  if (value.length < 6) {
                    return localizations.translate('password_too_short');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Поле для підтвердження пароля
              TextFormField(
                controller: _confirmPasswordController,
                obscureText: true,
                decoration: InputDecoration(
                  labelText: localizations.translate('confirm_password'),
                  prefixIcon: const Icon(Icons.lock),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.translate('required_field');
                  }
                  if (value != _passwordController.text) {
                    return localizations.translate('password_mismatch');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Перемикач для ролі адміністратора
              SwitchListTile(
                title: Text(localizations.translate('admin')),
                value: _isAdmin,
                onChanged: (value) {
                  setState(() {
                    _isAdmin = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              // Повідомлення про помилку
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ),
              // Кнопка реєстрації
              ElevatedButton(
                onPressed: _isLoading ? null : _register,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isLoading
                        ? const CircularProgressIndicator()
                        : Text(localizations.translate('register')),
              ),
              const SizedBox(height: 16),
              // Кнопка повернення на екран входу
              OutlinedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(localizations.translate('login')),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
