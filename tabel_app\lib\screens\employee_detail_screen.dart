import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';
import 'employee_edit_screen.dart';
import 'employee_bonuses_screen.dart';

/// Екран детального перегляду працівника (Android)
class EmployeeDetailScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeDetailScreen({super.key, required this.employee});

  @override
  State<EmployeeDetailScreen> createState() => _EmployeeDetailScreenState();
}

class _EmployeeDetailScreenState extends State<EmployeeDetailScreen> {
  late Employee _employee;
  List<EmployeeBonus> _bonuses = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _employee = widget.employee;
    _loadEmployeeBonuses();
  }

  /// Завантаження надбавок працівника
  Future<void> _loadEmployeeBonuses() async {
    setState(() => _isLoading = true);

    try {
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );
      final bonuses = await databaseService.getEmployeeBonusesByEmployee(
        _employee.id,
      );

      setState(() {
        _bonuses = bonuses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final currentEmployee = appState.currentEmployee;
    final isAdmin = currentEmployee?.isAdmin ?? false;
    final currency = appState.currency;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar з фото
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                _employee.fullName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 3,
                      color: Colors.black45,
                    ),
                  ],
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _employee.isAdmin
                          ? Colors.orange.shade300
                          : Colors.blue.shade300,
                      _employee.isAdmin
                          ? Colors.orange.shade600
                          : Colors.blue.shade600,
                    ],
                  ),
                ),
                child: Center(
                  child: Hero(
                    tag: 'employee_avatar_${_employee.id}',
                    child: CircleAvatar(
                      radius: 50,
                      backgroundColor: Colors.white.withOpacity(0.3),
                      child: Text(
                        _employee.firstName.isNotEmpty
                            ? _employee.firstName[0].toUpperCase()
                            : '',
                        style: const TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              if (isAdmin)
                PopupMenuButton<String>(
                  onSelected: (value) async {
                    switch (value) {
                      case 'edit':
                        final result = await Navigator.of(
                          context,
                        ).push<Employee>(
                          MaterialPageRoute(
                            builder:
                                (context) =>
                                    EmployeeEditScreen(employee: _employee),
                          ),
                        );
                        if (result != null) {
                          setState(() => _employee = result);
                        }
                        break;
                      case 'bonuses':
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder:
                                (context) =>
                                    EmployeeBonusesScreen(employee: _employee),
                          ),
                        );
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 20),
                              SizedBox(width: 8),
                              Text('Редагувати'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'bonuses',
                          child: Row(
                            children: [
                              Icon(Icons.card_giftcard, size: 20),
                              SizedBox(width: 8),
                              Text('Надбавки'),
                            ],
                          ),
                        ),
                      ],
                ),
            ],
          ),
          // Контент
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Основна інформація
                  _buildInfoSection(),
                  const SizedBox(height: 24),
                  // Фінансова інформація
                  _buildFinancialSection(currency),
                  const SizedBox(height: 24),
                  // Надбавки
                  _buildBonusesSection(),
                  const SizedBox(height: 24),
                  // Статистика
                  _buildStatsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Основна інформація',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Ім\'я', _employee.firstName),
            _buildInfoRow('Прізвище', _employee.lastName),
            _buildInfoRow('Посада', _employee.position),
            _buildInfoRow('Email', _employee.email),
            if (_employee.hireDate != null)
              _buildInfoRow(
                'Дата прийому',
                AppDateUtils.formatDate(_employee.hireDate!),
              ),
            if (_employee.experienceYears != null)
              _buildInfoRow(
                'Досвід роботи',
                '${_employee.experienceYears} років',
              ),
            if (_employee.qualificationLevel != null)
              _buildInfoRow(
                'Рівень кваліфікації',
                '${_employee.qualificationLevel}/5',
              ),
            _buildInfoRow(
              'Роль',
              _employee.isAdmin ? 'Адміністратор' : 'Працівник',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSection(Currency currency) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.attach_money, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Фінансова інформація',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.schedule, color: Colors.green.shade700),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Погодинна ставка',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${_employee.hourlyRate.toStringAsFixed(2)} ${currency.symbol}',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            // Розрахункові дані
            Row(
              children: [
                Expanded(
                  child: _buildCalculationCard(
                    'За день (8 год)',
                    '${(_employee.hourlyRate * 8).toStringAsFixed(2)} ${currency.symbol}',
                    Icons.today,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildCalculationCard(
                    'За місяць (160 год)',
                    '${(_employee.hourlyRate * 160).toStringAsFixed(2)} ${currency.symbol}',
                    Icons.calendar_month,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.blue.shade700, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: Colors.blue.shade700,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              color: Colors.blue.shade700,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBonusesSection() {
    final activeBonuses =
        _bonuses.where((bonus) => bonus.isActiveOn(DateTime.now())).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.card_giftcard, color: Colors.orange),
                    const SizedBox(width: 8),
                    Text(
                      'Надбавки',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                EmployeeBonusesScreen(employee: _employee),
                      ),
                    );
                  },
                  child: const Text('Переглянути всі'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (activeBonuses.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.card_giftcard_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Немає активних надбавок',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              )
            else
              ...activeBonuses
                  .take(3)
                  .map((bonus) => _buildBonusItem(bonus))
                  .toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildBonusItem(EmployeeBonus bonus) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Row(
        children: [
          Icon(
            _getBonusIcon(bonus.type),
            color: Colors.orange.shade700,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bonus.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.orange.shade700,
                  ),
                ),
                Text(
                  '${bonus.value.toStringAsFixed(1)}${bonus.calculationType == BonusCalculationType.percentage ? '%' : ' грн'}',
                  style: TextStyle(fontSize: 12, color: Colors.orange.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Статистика',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Статистика буде доступна після додавання робочих днів',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getBonusIcon(BonusType type) {
    switch (type) {
      case BonusType.hazardous:
        return Icons.warning;
      case BonusType.experience:
        return Icons.star;
      case BonusType.qualification:
        return Icons.school;
      case BonusType.overtime:
        return Icons.access_time;
      case BonusType.personal:
        return Icons.person;
      case BonusType.achievement:
        return Icons.card_giftcard;
    }
  }
}
