# Інструкція з встановлення Tabel App на Android

## Передумови

Для збірки та встановлення додатку на Android вам знадобиться:

1. Windows, macOS або Linux
2. Flutter SDK
3. Android SDK
4. Java Development Kit (JDK) 17 (обов'язково версія 17 для нових версій Android Gradle Plugin)
5. Android-пристрій або емулятор з Android 5.0 (API рівень 21) або новіше

## Налаштування підписання додатку

Для публікації додатку в Google Play Store або для встановлення на пристрої, додаток повинен бути підписаний.

### Створення ключа для підписання

1. Відкрийте командний рядок або термінал
2. Створіть директорію для ключа:
   ```
   mkdir -p android/keystore
   ```
3. Створіть ключ за допомогою keytool:
   ```
   keytool -genkey -v -keystore android/keystore/tabel_app.keystore -alias tabel_app -keyalg RSA -keysize 2048 -validity 10000
   ```
4. Слідуйте інструкціям на екрані, щоб ввести пароль та інформацію про ключ
5. Відкрийте файл `android/keystore.properties` та оновіть його з вашими даними:
   ```
   storeFile=../keystore/tabel_app.keystore
   storePassword=your_keystore_password
   keyAlias=tabel_app
   keyPassword=your_key_password
   ```

## Збірка додатку

### Метод 1: Використання скрипту

1. Запустіть скрипт `build_android.bat`:
   ```
   cd android
   build_android.bat
   ```
2. Після успішної збірки, файли будуть знаходитися в директорії `build/android`:
   - `tabel_app.apk` - для прямого встановлення на пристрої
   - `tabel_app.aab` - для публікації в Google Play Store

### Метод 2: Ручна збірка через командний рядок

1. Відкрийте командний рядок або термінал
2. Перейдіть до кореневої директорії проекту
3. Для збірки APK:
   ```
   flutter build apk --release
   ```
4. Для збірки App Bundle (AAB):
   ```
   flutter build appbundle --release
   ```
5. Файли будуть знаходитися в директоріях:
   - APK: `build/app/outputs/flutter-apk/app-release.apk`
   - AAB: `build/app/outputs/bundle/release/app-release.aab`

## Встановлення на пристрій

### Метод 1: Пряме встановлення APK

1. Скопіюйте APK файл на ваш Android-пристрій
2. На пристрої відкрийте файловий менеджер та знайдіть APK файл
3. Натисніть на файл, щоб почати встановлення
4. Якщо з'явиться попередження про встановлення з невідомих джерел, дозвольте встановлення для цього додатку
5. Слідуйте інструкціям на екрані для завершення встановлення

### Метод 2: Встановлення через ADB

1. Підключіть ваш Android-пристрій до комп'ютера через USB
2. Увімкніть режим розробника та USB-налагодження на пристрої
3. Відкрийте командний рядок або термінал
4. Встановіть додаток за допомогою ADB:
   ```
   adb install -r build/android/tabel_app.apk
   ```

### Метод 3: Через Google Play Store

1. Завантажте AAB файл в Google Play Console
2. Створіть новий реліз у відповідному треку (внутрішній, закритий, відкритий або виробничий)
3. Завантажте AAB файл
4. Заповніть необхідну інформацію про реліз
5. Опублікуйте реліз
6. Користувачі зможуть встановити додаток через Google Play Store

## Вирішення проблем

### Помилка підписання

Якщо ви отримуєте помилку підписання, переконайтеся, що:

1. Файл keystore.properties містить правильні дані
2. Файл keystore існує за вказаним шляхом
3. Паролі для keystore та ключа правильні

### Помилка збірки

Якщо ви отримуєте помилку при збірці:

1. Спробуйте очистити проект:
   ```
   flutter clean
   ```
2. Оновіть залежності:
   ```
   flutter pub get
   ```
3. Перевірте, чи всі залежності сумісні з вашою версією Flutter

### Помилка встановлення

Якщо додаток не встановлюється на пристрій:

1. Переконайтеся, що на пристрої дозволено встановлення з невідомих джерел
2. Переконайтеся, що версія Android на пристрої відповідає мінімальним вимогам (Android 5.0 або новіше)
3. Перевірте, чи достатньо вільного місця на пристрої
