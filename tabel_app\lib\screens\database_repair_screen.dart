import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/database_service.dart';
import '../utils/localization.dart';

/// Екран для діагностики та відновлення бази даних
class DatabaseRepairScreen extends StatefulWidget {
  const DatabaseRepairScreen({super.key});

  @override
  State<DatabaseRepairScreen> createState() => _DatabaseRepairScreenState();
}

class _DatabaseRepairScreenState extends State<DatabaseRepairScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _diagnosis;
  bool _isRepairing = false;

  @override
  void initState() {
    super.initState();
    _runDiagnosis();
  }

  /// Запуск діагностики бази даних
  Future<void> _runDiagnosis() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final databaseService = Provider.of<DatabaseService>(context, listen: false);
      final diagnosis = await databaseService.diagnoseDatabaseHealth();
      
      setState(() {
        _diagnosis = diagnosis;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Помилка діагностики: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Відновлення бази даних
  Future<void> _repairDatabase() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Підтвердження'),
        content: const Text(
          'Це видалить всі дані та створить нову базу даних. '
          'Ви впевнені, що хочете продовжити?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Скасувати'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Відновити'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isRepairing = true;
    });

    try {
      final databaseService = Provider.of<DatabaseService>(context, listen: false);
      final success = await databaseService.repairDatabase();
      
      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('База даних успішно відновлена'),
              backgroundColor: Colors.green,
            ),
          );
        }
        
        // Повторна діагностика
        await _runDiagnosis();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Помилка відновлення бази даних'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Помилка: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRepairing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Діагностика бази даних'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runDiagnosis,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _diagnosis == null
              ? const Center(child: Text('Немає даних діагностики'))
              : _buildDiagnosisView(),
    );
  }

  Widget _buildDiagnosisView() {
    final diagnosis = _diagnosis!;
    final isHealthy = diagnosis['isHealthy'] as bool;
    final missingTables = diagnosis['missingTables'] as List<String>;
    final errors = diagnosis['errors'] as List<String>;
    final recommendations = diagnosis['recommendations'] as List<String>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Статус здоров'я
          Card(
            color: isHealthy ? Colors.green.shade50 : Colors.red.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    isHealthy ? Icons.check_circle : Icons.error,
                    color: isHealthy ? Colors.green : Colors.red,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isHealthy ? 'База даних здорова' : 'Виявлені проблеми',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: isHealthy ? Colors.green : Colors.red,
                          ),
                        ),
                        if (diagnosis['employeeCount'] != null)
                          Text('Працівників: ${diagnosis['employeeCount']}'),
                        if (diagnosis['workDayCount'] != null)
                          Text('Робочих днів: ${diagnosis['workDayCount']}'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Відсутні таблиці
          if (missingTables.isNotEmpty) ...[
            Text(
              'Відсутні таблиці:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: missingTables
                      .map((table) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              children: [
                                const Icon(Icons.table_chart, color: Colors.orange),
                                const SizedBox(width: 8),
                                Text(table),
                              ],
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Помилки
          if (errors.isNotEmpty) ...[
            Text(
              'Помилки:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: errors
                      .map((error) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Icon(Icons.error, color: Colors.red),
                                const SizedBox(width: 8),
                                Expanded(child: Text(error)),
                              ],
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Рекомендації
          if (recommendations.isNotEmpty) ...[
            Text(
              'Рекомендації:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: recommendations
                      .map((recommendation) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Icon(Icons.lightbulb, color: Colors.blue),
                                const SizedBox(width: 8),
                                Expanded(child: Text(recommendation)),
                              ],
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Кнопка відновлення
          if (!isHealthy)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRepairing ? null : _repairDatabase,
                icon: _isRepairing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.build),
                label: Text(_isRepairing ? 'Відновлення...' : 'Відновити базу даних'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
