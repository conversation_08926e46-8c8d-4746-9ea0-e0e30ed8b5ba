import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';

import 'models/models.dart';
import 'services/services.dart';
import 'utils/utils.dart';
import 'screens/screens.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Ініціалізація SQLite для Windows
  if (Platform.isWindows || Platform.isLinux) {
    // Ініціалізація sqflite_common_ffi
    sqfliteFfiInit();
    // Встановлення databaseFactory
    databaseFactory = databaseFactoryFfi;
  }

  // Ініціалізація Firebase
  // Вимкнено для Windows, оскільки викликає проблеми
  // try {
  //   await Firebase.initializeApp(
  //     options: DefaultFirebaseOptions.currentPlatform,
  //   );
  // } catch (e) {
  //   print('Firebase initialization error: $e');
  // }

  // Встановлення орієнтації екрану
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // Отримання збережених налаштувань
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  final String languageCode = prefs.getString('languageCode') ?? 'uk';
  final bool isDarkMode = prefs.getBool('isDarkMode') ?? false;
  final String countryCode = prefs.getString('countryCode') ?? 'UA';

  // Імпорт святкових днів
  final calendarService = CalendarService();
  await calendarService.importHolidays(countryCode);

  // Створення тестового користувача для демонстрації
  await _createTestUserIfNeeded();

  runApp(
    MyApp(
      languageCode: languageCode,
      isDarkMode: isDarkMode,
      countryCode: countryCode,
    ),
  );
}

/// Створення тестового користувача для демонстрації
Future<void> _createTestUserIfNeeded() async {
  try {
    final databaseService = DatabaseService();

    // Перевіряємо, чи є вже користувачі в базі
    final employees = await databaseService.getAllEmployees();

    if (employees.isEmpty) {
      // Створюємо тестового користувача
      final testEmployee = Employee(
        firstName: 'Тест',
        lastName: 'Користувач',
        hourlyRate: 150.0,
        position: 'Менеджер',
        email: '<EMAIL>',
        isAdmin: true,
      );

      await databaseService.insertEmployee(testEmployee);
      print('Створено тестового користувача: ${testEmployee.email}');
    }
  } catch (e) {
    print('Помилка створення тестового користувача: $e');
  }
}

class MyApp extends StatefulWidget {
  final String languageCode;
  final bool isDarkMode;
  final String countryCode;

  const MyApp({
    super.key,
    required this.languageCode,
    required this.isDarkMode,
    required this.countryCode,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late AppLanguage _language;
  late bool _isDarkMode;
  late AppCountry _country;
  final AuthService _authService = AuthService();
  Employee? _currentEmployee;

  @override
  void initState() {
    super.initState();
    _language = AppLanguage.values.firstWhere(
      (lang) => lang.code == widget.languageCode,
      orElse: () => AppLanguage.ukrainian,
    );
    _isDarkMode = widget.isDarkMode;
    _country = AppCountry.fromCode(widget.countryCode);
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      // Отримуємо поточного користувача з AuthService
      _currentEmployee = await _authService.getCurrentEmployee();

      // Додаємо виведення інформації про поточного користувача для діагностики
      print(
        'Current employee loaded in _MyAppState: ${_currentEmployee?.fullName ?? "None"}',
      );
      print('Current employee ID: ${_currentEmployee?.id ?? "None"}');
      print('Current employee email: ${_currentEmployee?.email ?? "None"}');

      // Перевіряємо збережені дані в SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? savedId = prefs.getString('currentEmployeeId');
      final String? savedEmail = prefs.getString('currentEmployeeEmail');
      final bool? isLoggedIn = prefs.getBool('isLoggedIn');

      print('SharedPreferences data:');
      print('ID: $savedId, Email: $savedEmail, isLoggedIn: $isLoggedIn');

      // Якщо користувач завантажений, але флаг isLoggedIn не встановлений
      if (_currentEmployee != null && isLoggedIn != true) {
        print('User loaded but isLoggedIn flag is not set. Setting it now...');
        await prefs.setBool('isLoggedIn', true);
      }

      // Якщо користувач не завантажений, але флаг isLoggedIn встановлений
      if (_currentEmployee == null && isLoggedIn == true) {
        print(
          'isLoggedIn flag is set but user is not loaded. Clearing flag...',
        );
        await prefs.setBool('isLoggedIn', false);
      }

      // Додаткова перевірка - якщо є email, але немає користувача
      if (_currentEmployee == null && savedEmail != null) {
        print(
          'No user loaded but email exists: $savedEmail. Trying to find by email...',
        );

        // Створюємо новий екземпляр DatabaseService для пошуку користувача
        final databaseService = DatabaseService();
        final employeeByEmail = await databaseService.getEmployeeByEmail(
          savedEmail,
        );

        if (employeeByEmail != null) {
          print(
            'Found employee by email: ${employeeByEmail.fullName} (ID: ${employeeByEmail.id})',
          );
          _currentEmployee = employeeByEmail;

          // Зберігаємо дані користувача вручну
          await prefs.setString('currentEmployeeId', employeeByEmail.id);
          await prefs.setString('currentEmployeeEmail', employeeByEmail.email);
          await prefs.setString(
            'currentEmployeeFirstName',
            employeeByEmail.firstName,
          );
          await prefs.setString(
            'currentEmployeeLastName',
            employeeByEmail.lastName,
          );
          await prefs.setBool('isLoggedIn', true);

          print('Manually saved user data to SharedPreferences');
        }
      }
    } catch (e) {
      // Помилка завантаження користувача
      print('Error loading current employee: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<AuthService>(create: (_) => _authService),
        Provider<DatabaseService>(create: (_) => DatabaseService()),
        Provider<CalendarService>(create: (_) => CalendarService()),
        Provider<CalculationService>(create: (_) => CalculationService()),
        Provider<SyncService>(create: (_) => SyncService()),
        Provider<PdfService>(create: (_) => PdfService()),
        Provider<AbsenceService>(create: (_) => AbsenceService()),
        Provider<AnalyticsService>(create: (_) => AnalyticsService()),
        Provider<SettingsService>(create: (_) => SettingsService()),
        Provider<NotificationService>(create: (_) => NotificationService()),
        ChangeNotifierProvider<AppState>(
          create: (_) {
            // Створюємо стан додатку з поточним користувачем
            final appState = AppState(
              language: _language,
              isDarkMode: _isDarkMode,
              currentEmployee: _currentEmployee,
              country: _country,
            );

            // Додаємо виведення інформації про поточного користувача в AppState
            print(
              'AppState created with employee: ${_currentEmployee?.fullName ?? "None"}',
            );

            // Перевіряємо збережені дані в SharedPreferences
            SharedPreferences.getInstance().then((prefs) {
              final bool? isLoggedIn = prefs.getBool('isLoggedIn');
              print('AppState checking isLoggedIn: $isLoggedIn');

              // Якщо користувач авторизований, але в AppState немає користувача
              if (isLoggedIn == true && appState.currentEmployee == null) {
                print(
                  'User is logged in but AppState has no employee. Trying to load...',
                );

                // Отримуємо ідентифікатор користувача
                final String? employeeId = prefs.getString('currentEmployeeId');
                final String? employeeEmail = prefs.getString(
                  'currentEmployeeEmail',
                );

                if (employeeId != null) {
                  // Створюємо новий екземпляр DatabaseService для пошуку користувача
                  final databaseService = DatabaseService();
                  databaseService.getEmployee(employeeId).then((employee) {
                    if (employee != null) {
                      print(
                        'Found employee by ID in AppState: ${employee.fullName}',
                      );
                      appState.setCurrentEmployee(employee);
                    }
                  });
                } else if (employeeEmail != null) {
                  // Якщо немає ідентифікатора, але є email
                  final databaseService = DatabaseService();
                  databaseService.getEmployeeByEmail(employeeEmail).then((
                    employee,
                  ) {
                    if (employee != null) {
                      print(
                        'Found employee by email in AppState: ${employee.fullName}',
                      );
                      appState.setCurrentEmployee(employee);
                    }
                  });
                }
              }
            });

            return appState;
          },
        ),
      ],
      child: Consumer<AppState>(
        builder: (context, appState, _) {
          return MaterialApp(
            title: 'Tabel App',
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.blue,
                brightness:
                    appState.isDarkMode ? Brightness.dark : Brightness.light,
              ),
              useMaterial3: true,
            ),
            darkTheme: ThemeData(
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.blue,
                brightness: Brightness.dark,
              ),
              useMaterial3: true,
            ),
            themeMode: appState.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            locale: appState.language.locale,
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            home:
                appState.currentEmployee != null
                    ? const HomeScreen()
                    : const SplashScreen(),
          );
        },
      ),
    );
  }
}

/// Клас для зберігання стану додатку
class AppState extends ChangeNotifier {
  AppLanguage _language;
  bool _isDarkMode;
  Employee? _currentEmployee;
  AppCountry _country;
  Currency _currency;

  AppState({
    required AppLanguage language,
    required bool isDarkMode,
    Employee? currentEmployee,
    AppCountry? country,
  }) : _language = language,
       _isDarkMode = isDarkMode,
       _currentEmployee = currentEmployee,
       _country = country ?? AppCountry.ukraine,
       _currency = Currencies.getCurrencyByCountryCode(
         (country ?? AppCountry.ukraine).code,
       );

  AppLanguage get language => _language;
  bool get isDarkMode => _isDarkMode;
  Employee? get currentEmployee => _currentEmployee;
  AppCountry get country => _country;
  Currency get currency => _currency;

  void setLanguage(AppLanguage language) async {
    _language = language;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', language.code);
    notifyListeners();
  }

  void setThemeMode(bool isDarkMode) async {
    _isDarkMode = isDarkMode;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', isDarkMode);
    notifyListeners();
  }

  void setCountry(AppCountry country) async {
    _country = country;
    _currency = Currencies.getCurrencyByCountryCode(country.code);

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('countryCode', country.code);

    // Імпорт святкових днів для нової країни
    final calendarService = CalendarService();
    await calendarService.importHolidays(country.code);

    notifyListeners();
  }

  void setCurrentEmployee(Employee? employee) {
    _currentEmployee = employee;
    notifyListeners();
  }
}
