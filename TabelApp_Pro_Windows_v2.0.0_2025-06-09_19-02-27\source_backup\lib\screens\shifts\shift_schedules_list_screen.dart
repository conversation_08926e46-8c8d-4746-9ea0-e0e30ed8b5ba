import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import '../../widgets/widgets.dart';
import 'shift_schedule_edit_screen.dart';

/// Екран списку графіків змін
class ShiftSchedulesListScreen extends StatefulWidget {
  /// Конструктор
  const ShiftSchedulesListScreen({Key? key}) : super(key: key);

  @override
  State<ShiftSchedulesListScreen> createState() =>
      _ShiftSchedulesListScreenState();
}

class _ShiftSchedulesListScreenState extends State<ShiftSchedulesListScreen> {
  final ShiftScheduleService _shiftScheduleService = ShiftScheduleService();
  List<ShiftSchedule> _schedules = [];
  Map<String, Shift> _shiftsMap = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Завантаження графіків змін
      _schedules = await _shiftScheduleService.getAllShiftSchedules();
      
      // Завантаження змін
      final shifts = await _shiftScheduleService.getAllShifts();
      _shiftsMap = {for (var shift in shifts) shift.id: shift};
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка завантаження даних: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Видалення графіку змін
  Future<void> _deleteSchedule(String id) async {
    try {
      await _shiftScheduleService.deleteShiftSchedule(id);
      await _loadData();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Графік змін видалено')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка видалення графіку змін: $e')),
        );
      }
    }
  }

  /// Отримання назви зміни за ідентифікатором
  String _getShiftName(String shiftId) {
    return _shiftsMap[shiftId]?.name ?? 'Невідома зміна';
  }

  /// Отримання кольору зміни за ідентифікатором
  Color _getShiftColor(String shiftId) {
    return _shiftsMap[shiftId]?.color ?? Colors.grey;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Графіки змін'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _schedules.isEmpty
              ? const Center(child: Text('Немає доступних графіків змін'))
              : ListView.builder(
                  itemCount: _schedules.length,
                  itemBuilder: (context, index) {
                    final schedule = _schedules[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 8.0,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ListTile(
                            title: Text(schedule.name),
                            subtitle: Text(
                              'Поточна зміна: ${schedule.shiftOrder.isNotEmpty ? _getShiftName(schedule.shiftOrder[schedule.currentShiftIndex]) : 'Не вказано'}',
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () async {
                                    await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            ShiftScheduleEditScreen(
                                          schedule: schedule,
                                        ),
                                      ),
                                    );
                                    _loadData();
                                  },
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete),
                                  onPressed: () {
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: const Text('Видалення графіку'),
                                        content: Text(
                                          'Ви впевнені, що хочете видалити графік "${schedule.name}"?',
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('Скасувати'),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                              _deleteSchedule(schedule.id);
                                            },
                                            child: const Text('Видалити'),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                            onTap: () async {
                              await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ShiftScheduleEditScreen(
                                    schedule: schedule,
                                  ),
                                ),
                              );
                              _loadData();
                            },
                          ),
                          if (schedule.shiftOrder.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 8.0,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Порядок змін:',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  const SizedBox(height: 8.0),
                                  Wrap(
                                    spacing: 8.0,
                                    runSpacing: 8.0,
                                    children: schedule.shiftOrder
                                        .map(
                                          (shiftId) => Chip(
                                            backgroundColor:
                                                _getShiftColor(shiftId),
                                            label: Text(
                                              _getShiftName(shiftId),
                                              style: TextStyle(
                                                color: _getShiftColor(shiftId)
                                                            .computeLuminance() >
                                                        0.5
                                                    ? Colors.black
                                                    : Colors.white,
                                              ),
                                            ),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                  const SizedBox(height: 8.0),
                                  Text(
                                    'Вихідні: ${schedule.hasFloatingDaysOff ? 'Плаваючі (${schedule.workDaysCount} роб. / ${schedule.daysOffCount} вих.)' : '${schedule.dayOff1.name}${schedule.dayOff2 != null ? ', ${schedule.dayOff2!.name}' : ''}'}',
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ShiftScheduleEditScreen(),
            ),
          );
          _loadData();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
