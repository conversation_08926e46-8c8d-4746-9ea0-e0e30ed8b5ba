# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VS Code related
.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
build/
coverage/
lib/generated_plugin_registrant.dart
pubspec.lock

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android specific
android/.gradle/
android/captures/
android/local.properties
android/keystore.properties
android/keystore/
android/*.jks
android/*.keystore
android/app/debug/
android/app/profile/
android/app/release/

# iOS specific
ios/Flutter/.last_build_id
ios/Flutter/ephemeral/
ios/Flutter/app.flx
ios/Flutter/app.zip
ios/Flutter/flutter_assets/
ios/Flutter/flutter_export_environment.sh
ios/Pods/
ios/.symlinks/
ios/Runner.xcworkspace/xcuserdata/
ios/build/

# Keep important iOS files
!ios/Runner.xcodeproj/
!ios/Runner.xcworkspace/
!ios/Runner/
!ios/Flutter/
!ios/Flutter/Generated.xcconfig
!ios/Flutter/AppFrameworkInfo.plist

# macOS specific
macos/Flutter/ephemeral/
macos/Flutter/GeneratedPluginRegistrant.*
macos/Pods/
macos/.symlinks/
macos/build/

# Windows specific
windows/Flutter/ephemeral/
windows/Flutter/generated_plugin_registrant.*
windows/flutter/
windows/.vs/
windows/out/
windows/build/

# Web specific
web/build/

# Temporary files
*.tmp
*.temp

# Gradle specific
.gradle/
gradle-app.setting
!gradle-wrapper.jar
.gradletasknamecache

# Local configuration file
local.properties

# Temporary build files
C:/gradle_temp/
