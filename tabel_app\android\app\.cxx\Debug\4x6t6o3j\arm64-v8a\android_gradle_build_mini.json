{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Tabel\\tabel_app\\android\\app\\.cxx\\Debug\\4x6t6o3j\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Tabel\\tabel_app\\android\\app\\.cxx\\Debug\\4x6t6o3j\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}