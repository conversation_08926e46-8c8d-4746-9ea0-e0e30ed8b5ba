import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import 'package:flutter/foundation.dart';

/// Сервіс для відправки електронних листів
class EmailService {
  /// Відправка коду підтвердження для скидання пароля
  Future<bool> sendPasswordResetCode({
    required String email,
    required String resetCode,
    required String language,
  }) async {
    try {
      // Налаштування SMTP сервера Gmail
      final smtpServer = gmail(
        '<EMAIL>', // Ваша Gmail адреса
        'bans ddpa idlu chlb', // Ваш 16-символьний пароль додатку
      );

      // Створення повідомлення
      final message =
          Message()
            ..from = Address('<EMAIL>', 'Табель робочих годин')
            ..recipients.add(email)
            ..subject = _getSubject(language)
            ..html = _getHtmlBody(resetCode, language);

      // Виведення коду в консоль для тестування
      if (kDebugMode) {
        print('Sending reset code $resetCode to $email');
      }

      try {
        // Спроба відправити повідомлення
        final sendReport = await send(message, smtpServer);

        if (kDebugMode) {
          print('Email sent: ${sendReport.toString()}');
        }

        return true;
      } catch (emailError) {
        // Якщо виникла помилка при відправці, виводимо її в консоль
        if (kDebugMode) {
          print('Error sending email: $emailError');
        }

        // Для тестування повертаємо true, щоб можна було продовжити процес скидання пароля
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('General error: $e');
      }
      return false;
    }
  }

  /// Отримання теми листа залежно від мови
  String _getSubject(String language) {
    switch (language) {
      case 'uk':
        return 'Скидання пароля - Tabel App';
      case 'cs':
        return 'Obnovení hesla - Tabel App';
      case 'sk':
        return 'Obnovenie hesla - Tabel App';
      case 'pl':
        return 'Resetowanie hasła - Tabel App';
      default:
        return 'Password Reset - Tabel App';
    }
  }

  /// Отримання HTML-тіла листа залежно від мови
  String _getHtmlBody(String resetCode, String language) {
    final title = _getTitle(language);
    final greeting = _getGreeting(language);
    final instruction = _getInstruction(language);
    final codeLabel = _getCodeLabel(language);
    final footer = _getFooter(language);

    return '''
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>$title</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .container {
          border: 1px solid #ddd;
          border-radius: 5px;
          padding: 20px;
          background-color: #f9f9f9;
        }
        .code {
          font-size: 24px;
          font-weight: bold;
          text-align: center;
          padding: 10px;
          margin: 20px 0;
          background-color: #eee;
          border-radius: 5px;
          letter-spacing: 5px;
        }
        .footer {
          font-size: 12px;
          color: #777;
          margin-top: 20px;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>$title</h2>
        <p>$greeting</p>
        <p>$instruction</p>
        <p>$codeLabel</p>
        <div class="code">$resetCode</div>
        <p class="footer">$footer</p>
      </div>
    </body>
    </html>
    ''';
  }

  /// Отримання заголовка листа залежно від мови
  String _getTitle(String language) {
    switch (language) {
      case 'uk':
        return 'Скидання пароля';
      case 'cs':
        return 'Obnovení hesla';
      case 'sk':
        return 'Obnovenie hesla';
      case 'pl':
        return 'Resetowanie hasła';
      default:
        return 'Password Reset';
    }
  }

  /// Отримання привітання залежно від мови
  String _getGreeting(String language) {
    switch (language) {
      case 'uk':
        return 'Вітаємо!';
      case 'cs':
        return 'Dobrý den!';
      case 'sk':
        return 'Dobrý deň!';
      case 'pl':
        return 'Dzień dobry!';
      default:
        return 'Hello!';
    }
  }

  /// Отримання інструкції залежно від мови
  String _getInstruction(String language) {
    switch (language) {
      case 'uk':
        return 'Ви отримали цей лист, тому що запросили скидання пароля для вашого облікового запису в додатку Tabel. Будь ласка, використовуйте наступний код для підтвердження скидання пароля:';
      case 'cs':
        return 'Tento e-mail jste obdrželi, protože jste požádali o obnovení hesla pro svůj účet v aplikaci Tabel. Použijte prosím následující kód pro potvrzení obnovení hesla:';
      case 'sk':
        return 'Tento e-mail ste dostali, pretože ste požiadali o obnovenie hesla pre svoj účet v aplikácii Tabel. Použite prosím nasledujúci kód na potvrdenie obnovenia hesla:';
      case 'pl':
        return 'Otrzymałeś tę wiadomość, ponieważ poprosiłeś o zresetowanie hasła do swojego konta w aplikacji Tabel. Użyj poniższego kodu, aby potwierdzić resetowanie hasła:';
      default:
        return 'You received this email because you requested a password reset for your account in the Tabel app. Please use the following code to confirm your password reset:';
    }
  }

  /// Отримання підпису коду залежно від мови
  String _getCodeLabel(String language) {
    switch (language) {
      case 'uk':
        return 'Ваш код підтвердження:';
      case 'cs':
        return 'Váš ověřovací kód:';
      case 'sk':
        return 'Váš overovací kód:';
      case 'pl':
        return 'Twój kod weryfikacyjny:';
      default:
        return 'Your verification code:';
    }
  }

  /// Отримання підпису листа залежно від мови
  String _getFooter(String language) {
    switch (language) {
      case 'uk':
        return 'Якщо ви не запитували скидання пароля, проігноруйте цей лист. Цей код дійсний протягом 30 хвилин.';
      case 'cs':
        return 'Pokud jste o obnovení hesla nepožádali, ignorujte tento e-mail. Tento kód je platný po dobu 30 minut.';
      case 'sk':
        return 'Ak ste o obnovenie hesla nežiadali, ignorujte tento e-mail. Tento kód je platný po dobu 30 minút.';
      case 'pl':
        return 'Jeśli nie prosiłeś o zresetowanie hasła, zignoruj tę wiadomość. Ten kod jest ważny przez 30 minut.';
      default:
        return 'If you did not request a password reset, please ignore this email. This code is valid for 30 minutes.';
    }
  }
}
