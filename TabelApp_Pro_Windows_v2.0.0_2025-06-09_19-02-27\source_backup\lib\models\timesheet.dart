import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import 'work_day.dart';

/// Статус табеля
enum TimesheetStatus {
  /// Чернетка (в процесі заповнення)
  draft,
  
  /// Відправлений на перевірку
  submitted,
  
  /// Затверджений
  approved,
  
  /// Відхилений
  rejected,
}

/// Модель табеля
class Timesheet extends Equatable {
  /// Унікальний ідентифікатор
  final String id;
  
  /// Ідентифікатор працівника
  final String employeeId;
  
  /// Початкова дата періоду
  final DateTime startDate;
  
  /// Кінцева дата періоду
  final DateTime endDate;
  
  /// Список робочих днів
  final List<WorkDay> workDays;
  
  /// Статус табеля
  final TimesheetStatus status;
  
  /// Коментар до табеля
  final String? comment;
  
  /// Дата створення
  final DateTime createdAt;
  
  /// Дата останнього оновлення
  final DateTime updatedAt;

  /// Конструктор
  Timesheet({
    String? id,
    required this.employeeId,
    required this.startDate,
    required this.endDate,
    required this.workDays,
    this.status = TimesheetStatus.draft,
    this.comment,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : 
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  Timesheet copyWith({
    String? employeeId,
    DateTime? startDate,
    DateTime? endDate,
    List<WorkDay>? workDays,
    TimesheetStatus? status,
    String? comment,
    DateTime? updatedAt,
  }) {
    return Timesheet(
      id: this.id,
      employeeId: employeeId ?? this.employeeId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      workDays: workDays ?? this.workDays,
      status: status ?? this.status,
      comment: comment ?? this.comment,
      createdAt: this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'status': status.index,
      'comment': comment,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory Timesheet.fromMap(Map<String, dynamic> map, List<WorkDay> workDays) {
    return Timesheet(
      id: map['id'],
      employeeId: map['employeeId'],
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      endDate: DateTime.fromMillisecondsSinceEpoch(map['endDate']),
      workDays: workDays,
      status: TimesheetStatus.values[map['status']],
      comment: map['comment'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  /// Загальна кількість відпрацьованих годин
  double get totalHours {
    return workDays.fold(0, (sum, day) => sum + day.hoursWorked);
  }

  /// Загальна кількість відпрацьованих годин за типом дня
  double getHoursByDayType(DayType dayType) {
    return workDays
        .where((day) => day.dayType == dayType)
        .fold(0, (sum, day) => sum + day.hoursWorked);
  }

  /// Загальна кількість відпрацьованих годин за типом зміни
  double getHoursByShiftType(ShiftType shiftType) {
    return workDays
        .where((day) => day.shiftType == shiftType)
        .fold(0, (sum, day) => sum + day.hoursWorked);
  }

  @override
  List<Object?> get props => [
    id, 
    employeeId, 
    startDate, 
    endDate, 
    workDays, 
    status, 
    comment, 
    createdAt, 
    updatedAt
  ];
}
