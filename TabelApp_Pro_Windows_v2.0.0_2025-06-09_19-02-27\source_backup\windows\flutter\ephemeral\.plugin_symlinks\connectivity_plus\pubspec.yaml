name: connectivity_plus
description: Flutter plugin for discovering the state of the network (WiFi & mobile/cellular) connectivity on Android and iOS.
version: 6.1.4
homepage: https://github.com/fluttercommunity/plus_plugins
repository: https://github.com/fluttercommunity/plus_plugins/tree/main/packages/connectivity_plus/connectivity_plus
issue_tracker: https://github.com/fluttercommunity/plus_plugins/labels/connectivity_plus
topics:
  - connectivity
  - utils

environment:
  sdk: ">=3.2.0 <4.0.0"
  # Flutter versions prior to 3.7 did not support the
  # sharedDarwinSource option.
  flutter: ">=3.7.0"

flutter:
  plugin:
    platforms:
      android:
        package: dev.fluttercommunity.plus.connectivity
        pluginClass: ConnectivityPlugin
      ios:
        pluginClass: ConnectivityPlusPlugin
      linux:
        dartPluginClass: ConnectivityPlusLinuxPlugin
      macos:
        pluginClass: ConnectivityPlusPlugin
      web:
        pluginClass: ConnectivityPlusWebPlugin
        fileName: src/connectivity_plus_web.dart
      windows:
        pluginClass: ConnectivityPlusWindowsPlugin

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  connectivity_plus_platform_interface: ^2.0.1
  web: ">=0.5.0 <2.0.0"
  meta: ^1.8.0
  nm: ^0.5.0
  collection: ^1.18.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.3.3
  dbus: ^0.7.8
  flutter_lints: ">=4.0.0 <6.0.0"
  mockito: ^5.4.0
  plugin_platform_interface: ^2.1.5
  test: ^1.22.0
