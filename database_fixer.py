#!/usr/bin/env python3
"""
Утилита для исправления базы данных Tabel App
Добавляет недостающие колонки и таблицы
"""

import sqlite3
import os
import sys
from pathlib import Path

def find_database():
    """Поиск файла базы данных"""
    possible_paths = [
        os.path.expanduser("~/AppData/Roaming/tabel_app/timesheet.db"),
        os.path.expanduser("~/AppData/Local/tabel_app/timesheet.db"),
        os.path.expanduser("~/Documents/timesheet.db"),
        "./timesheet.db",
        "../timesheet.db"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_database(db_path):
    """Исправление структуры базы данных"""
    print(f"Исправление базы данных: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем существующие таблицы
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        print(f"Существующие таблицы: {existing_tables}")
        
        # Проверяем структуру workDays
        cursor.execute("PRAGMA table_info(workDays)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"Колонки в workDays: {columns}")
        
        # Добавляем shiftId если его нет
        if 'shiftId' not in columns:
            print("Добавляем колонку shiftId...")
            cursor.execute("ALTER TABLE workDays ADD COLUMN shiftId TEXT")
            print("✓ Колонка shiftId добавлена")
        else:
            print("✓ Колонка shiftId уже существует")
        
        # Создаем таблицу shifts
        if 'shifts' not in existing_tables:
            print("Создаем таблицу shifts...")
            cursor.execute('''
                CREATE TABLE shifts (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    startTimeHour INTEGER NOT NULL,
                    startTimeMinute INTEGER NOT NULL,
                    endTimeHour INTEGER NOT NULL,
                    endTimeMinute INTEGER NOT NULL,
                    colorValue INTEGER NOT NULL,
                    isNightShift INTEGER NOT NULL
                )
            ''')
            print("✓ Таблица shifts создана")
        else:
            print("✓ Таблица shifts уже существует")
        
        # Создаем таблицу shiftSchedules
        if 'shiftSchedules' not in existing_tables:
            print("Создаем таблицу shiftSchedules...")
            cursor.execute('''
                CREATE TABLE shiftSchedules (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    shiftOrder TEXT NOT NULL,
                    currentShiftIndex INTEGER NOT NULL,
                    startDate INTEGER NOT NULL,
                    dayOff1 INTEGER NOT NULL,
                    dayOff2 INTEGER,
                    hasFloatingDaysOff INTEGER NOT NULL,
                    workDaysCount INTEGER NOT NULL DEFAULT 5,
                    daysOffCount INTEGER NOT NULL DEFAULT 2
                )
            ''')
            print("✓ Таблица shiftSchedules создана")
        else:
            print("✓ Таблица shiftSchedules уже существует")
        
        # Создаем таблицу employeeShiftSchedules
        if 'employeeShiftSchedules' not in existing_tables:
            print("Создаем таблицу employeeShiftSchedules...")
            cursor.execute('''
                CREATE TABLE employeeShiftSchedules (
                    id TEXT PRIMARY KEY,
                    employeeId TEXT NOT NULL,
                    shiftScheduleId TEXT NOT NULL,
                    FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE,
                    FOREIGN KEY (shiftScheduleId) REFERENCES shiftSchedules (id) ON DELETE CASCADE
                )
            ''')
            print("✓ Таблица employeeShiftSchedules создана")
        else:
            print("✓ Таблица employeeShiftSchedules уже существует")
        
        # Сохраняем изменения
        conn.commit()
        conn.close()
        
        print("\n✅ База данных успешно исправлена!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при исправлении базы данных: {e}")
        return False

def main():
    print("=== Утилита исправления базы данных Tabel App ===\n")
    
    # Поиск базы данных
    db_path = find_database()
    
    if not db_path:
        print("❌ База данных не найдена!")
        print("Проверьте следующие пути:")
        print("- ~/AppData/Roaming/tabel_app/timesheet.db")
        print("- ~/AppData/Local/tabel_app/timesheet.db")
        print("- ~/Documents/timesheet.db")
        return False
    
    print(f"✓ База данных найдена: {db_path}")
    
    # Создаем резервную копию
    backup_path = db_path + ".backup"
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ Резервная копия создана: {backup_path}")
    except Exception as e:
        print(f"⚠️ Не удалось создать резервную копию: {e}")
    
    # Исправляем базу данных
    success = fix_database(db_path)
    
    if success:
        print("\n🎉 Готово! Теперь можно запускать приложение.")
    else:
        print("\n💥 Что-то пошло не так. Проверьте резервную копию.")
    
    return success

if __name__ == "__main__":
    main()
    input("\nНажмите Enter для выхода...")
