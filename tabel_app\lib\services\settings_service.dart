import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sync_settings.dart';
import '../models/reminder_settings.dart';

/// Сервіс для управління налаштуваннями додатку
class SettingsService {
  static const String _syncSettingsKey = 'sync_settings';
  static const String _reminderSettingsKey = 'reminder_settings';
  static const String _lastSyncDatePrefix = 'last_sync_date_';

  /// Отримання налаштувань синхронізації
  Future<SyncSettings> getSyncSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final json = prefs.getString(_syncSettingsKey);

      if (json == null) {
        return SyncSettings();
      }

      return SyncSettings.fromJson(jsonDecode(json));
    } catch (e) {
      debugPrint('Error getting sync settings: $e');
      return SyncSettings();
    }
  }

  /// Збереження налаштувань синхронізації
  Future<void> saveSyncSettings(SyncSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_syncSettingsKey, jsonEncode(settings.toJson()));
    } catch (e) {
      debugPrint('Error saving sync settings: $e');
      rethrow;
    }
  }

  /// Отримання налаштувань нагадувань
  Future<ReminderSettings> getReminderSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final json = prefs.getString(_reminderSettingsKey);

      if (json == null) {
        return ReminderSettings();
      }

      return ReminderSettings.fromJson(jsonDecode(json));
    } catch (e) {
      debugPrint('Error getting reminder settings: $e');
      return ReminderSettings();
    }
  }

  /// Збереження налаштувань нагадувань
  Future<void> saveReminderSettings(ReminderSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _reminderSettingsKey,
        jsonEncode(settings.toJson()),
      );
    } catch (e) {
      debugPrint('Error saving reminder settings: $e');
      rethrow;
    }
  }

  /// Отримання дати останньої синхронізації для певного типу даних
  Future<DateTime?> getLastSyncDate(String dataType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('${_lastSyncDatePrefix}$dataType');

      if (timestamp == null) {
        return null;
      }

      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      debugPrint('Error getting last sync date for $dataType: $e');
      return null;
    }
  }

  /// Встановлення дати останньої синхронізації для певного типу даних
  Future<void> setLastSyncDate(String dataType, DateTime date) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        '${_lastSyncDatePrefix}$dataType',
        date.millisecondsSinceEpoch,
      );
    } catch (e) {
      debugPrint('Error setting last sync date for $dataType: $e');
      rethrow;
    }
  }
}
