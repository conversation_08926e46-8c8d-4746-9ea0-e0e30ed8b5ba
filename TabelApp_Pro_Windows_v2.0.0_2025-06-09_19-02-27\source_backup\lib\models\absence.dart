import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Типи відсутності
enum AbsenceType {
  /// Щорічна відпустка
  vacation,

  /// Лікарняний
  sickLeave,

  /// Відгул
  dayOff,

  /// Відпустка за власний рахунок
  unpaidLeave,

  /// Навчальна відпустка
  studyLeave,

  /// Декретна відпустка
  maternityLeave,

  /// Відпустка по догляду за дитиною
  childcareLeave,

  /// Інше
  other,
}

/// Статус відсутності
enum AbsenceStatus {
  /// Запланована (очікує затвердження)
  planned,

  /// Затверджена
  approved,

  /// Відхилена
  rejected,

  /// Скасована
  cancelled,
}

/// Модель відсутності (відпустки, лікарняні, відгули)
class Absence extends Equatable {
  /// Унікальний ідентифікатор
  final String id;

  /// Ідентифікатор працівника
  final String employeeId;

  /// Тип відсутності
  final AbsenceType type;

  /// Дата початку відсутності
  final DateTime startDate;

  /// Дата закінчення відсутності
  final DateTime endDate;

  /// Статус відсутності
  final AbsenceStatus status;

  /// Коментар до відсутності
  final String? comment;

  /// Документ-підстава (номер лікарняного, наказу тощо)
  final String? document;

  /// Ідентифікатор користувача, який затвердив/відхилив відсутність
  final String? approvedById;

  /// Дата затвердження/відхилення
  final DateTime? approvedAt;

  /// Коментар до затвердження/відхилення
  final String? approvalComment;

  /// Дата створення запису
  final DateTime createdAt;

  /// Дата останнього оновлення запису
  final DateTime updatedAt;

  /// Конструктор
  Absence({
    String? id,
    required this.employeeId,
    required this.type,
    required this.startDate,
    required this.endDate,
    this.status = AbsenceStatus.planned,
    this.comment,
    this.document,
    this.approvedById,
    this.approvedAt,
    this.approvalComment,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  Absence copyWith({
    String? employeeId,
    AbsenceType? type,
    DateTime? startDate,
    DateTime? endDate,
    AbsenceStatus? status,
    String? comment,
    String? document,
    String? approvedById,
    DateTime? approvedAt,
    String? approvalComment,
    DateTime? updatedAt,
  }) {
    return Absence(
      id: id,
      employeeId: employeeId ?? this.employeeId,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      comment: comment ?? this.comment,
      document: document ?? this.document,
      approvedById: approvedById ?? this.approvedById,
      approvedAt: approvedAt ?? this.approvedAt,
      approvalComment: approvalComment ?? this.approvalComment,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'type': type.index,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'status': status.index,
      'comment': comment,
      'document': document,
      'approvedById': approvedById,
      'approvedAt': approvedAt?.millisecondsSinceEpoch,
      'approvalComment': approvalComment,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory Absence.fromMap(Map<String, dynamic> map) {
    return Absence(
      id: map['id'],
      employeeId: map['employeeId'],
      type: AbsenceType.values[map['type']],
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      endDate: DateTime.fromMillisecondsSinceEpoch(map['endDate']),
      status: AbsenceStatus.values[map['status']],
      comment: map['comment'],
      document: map['document'],
      approvedById: map['approvedById'],
      approvedAt:
          map['approvedAt'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['approvedAt'])
              : null,
      approvalComment: map['approvalComment'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  /// Тривалість відсутності в днях
  int get durationInDays {
    return endDate.difference(startDate).inDays + 1;
  }

  /// Перевірка, чи відсутність активна на вказану дату
  bool isActiveOnDate(DateTime date) {
    final normalizedDate = DateTime(date.year, date.month, date.day);
    final normalizedStartDate = DateTime(
      startDate.year,
      startDate.month,
      startDate.day,
    );
    final normalizedEndDate = DateTime(
      endDate.year,
      endDate.month,
      endDate.day,
    );

    return status == AbsenceStatus.approved &&
        normalizedDate.compareTo(normalizedStartDate) >= 0 &&
        normalizedDate.compareTo(normalizedEndDate) <= 0;
  }

  @override
  List<Object?> get props => [
    id,
    employeeId,
    type,
    startDate,
    endDate,
    status,
    comment,
    document,
    approvedById,
    approvedAt,
    approvalComment,
    createdAt,
    updatedAt,
  ];
}

/// Розширення для отримання назви типу відсутності
extension AbsenceTypeExtension on AbsenceType {
  String getLocalizationKey() {
    switch (this) {
      case AbsenceType.vacation:
        return 'vacation';
      case AbsenceType.sickLeave:
        return 'sick_leave';
      case AbsenceType.dayOff:
        return 'day_off';
      case AbsenceType.unpaidLeave:
        return 'unpaid_leave';
      case AbsenceType.studyLeave:
        return 'study_leave';
      case AbsenceType.maternityLeave:
        return 'maternity_leave';
      case AbsenceType.childcareLeave:
        return 'childcare_leave';
      case AbsenceType.other:
        return 'other';
    }
  }

  String getName(String languageCode) {
    // Цей метод залишено для сумісності, але рекомендується використовувати AppLocalizations
    switch (languageCode) {
      case 'uk':
        switch (this) {
          case AbsenceType.vacation:
            return 'Щорічна відпустка';
          case AbsenceType.sickLeave:
            return 'Лікарняний';
          case AbsenceType.dayOff:
            return 'Відгул';
          case AbsenceType.unpaidLeave:
            return 'Відпустка за власний рахунок';
          case AbsenceType.studyLeave:
            return 'Навчальна відпустка';
          case AbsenceType.maternityLeave:
            return 'Декретна відпустка';
          case AbsenceType.childcareLeave:
            return 'Відпустка по догляду за дитиною';
          case AbsenceType.other:
            return 'Інше';
        }
      case 'en':
        switch (this) {
          case AbsenceType.vacation:
            return 'Annual Leave';
          case AbsenceType.sickLeave:
            return 'Sick Leave';
          case AbsenceType.dayOff:
            return 'Day Off';
          case AbsenceType.unpaidLeave:
            return 'Unpaid Leave';
          case AbsenceType.studyLeave:
            return 'Study Leave';
          case AbsenceType.maternityLeave:
            return 'Maternity Leave';
          case AbsenceType.childcareLeave:
            return 'Childcare Leave';
          case AbsenceType.other:
            return 'Other';
        }
      default:
        switch (this) {
          case AbsenceType.vacation:
            return 'Annual Leave';
          case AbsenceType.sickLeave:
            return 'Sick Leave';
          case AbsenceType.dayOff:
            return 'Day Off';
          case AbsenceType.unpaidLeave:
            return 'Unpaid Leave';
          case AbsenceType.studyLeave:
            return 'Study Leave';
          case AbsenceType.maternityLeave:
            return 'Maternity Leave';
          case AbsenceType.childcareLeave:
            return 'Childcare Leave';
          case AbsenceType.other:
            return 'Other';
        }
    }
  }
}

/// Розширення для отримання назви статусу відсутності
extension AbsenceStatusExtension on AbsenceStatus {
  String getLocalizationKey() {
    switch (this) {
      case AbsenceStatus.planned:
        return 'planned_status';
      case AbsenceStatus.approved:
        return 'approved_status';
      case AbsenceStatus.rejected:
        return 'rejected_status';
      case AbsenceStatus.cancelled:
        return 'cancelled';
    }
  }

  String getName(String languageCode) {
    // Цей метод залишено для сумісності, але рекомендується використовувати AppLocalizations
    switch (languageCode) {
      case 'uk':
        switch (this) {
          case AbsenceStatus.planned:
            return 'Очікує затвердження';
          case AbsenceStatus.approved:
            return 'Затверджено';
          case AbsenceStatus.rejected:
            return 'Відхилено';
          case AbsenceStatus.cancelled:
            return 'Скасовано';
        }
      case 'en':
        switch (this) {
          case AbsenceStatus.planned:
            return 'Pending Approval';
          case AbsenceStatus.approved:
            return 'Approved';
          case AbsenceStatus.rejected:
            return 'Rejected';
          case AbsenceStatus.cancelled:
            return 'Cancelled';
        }
      default:
        switch (this) {
          case AbsenceStatus.planned:
            return 'Pending Approval';
          case AbsenceStatus.approved:
            return 'Approved';
          case AbsenceStatus.rejected:
            return 'Rejected';
          case AbsenceStatus.cancelled:
            return 'Cancelled';
        }
    }
  }
}
