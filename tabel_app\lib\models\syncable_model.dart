import 'package:equatable/equatable.dart';

/// Базовий клас для моделей, які підтримують синхронізацію
abstract class SyncableModel extends Equatable {
  /// Унікальний ідентифікатор
  final String id;
  
  /// Дата створення
  final DateTime createdAt;
  
  /// Дата останнього оновлення
  final DateTime updatedAt;
  
  /// Ідентифікатор користувача, який останнім модифікував запис
  final String lastModifiedBy;
  
  /// Прапорець видалення (для soft delete)
  final bool isDeleted;
  
  /// Прапорець синхронізації
  final bool isSynced;

  const SyncableModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.lastModifiedBy,
    this.isDeleted = false,
    this.isSynced = false,
  });
  
  /// Перевірка на конфлікт з іншою моделлю
  bool hasConflictWith(SyncableModel other) {
    if (id != other.id) return false;
    return updatedAt != other.updatedAt;
  }
  
  /// Перетворення моделі в Map
  Map<String, dynamic> toMap();
  
  /// Створення копії моделі з оновленими полями
  SyncableModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? lastModifiedBy,
    bool? isDeleted,
    bool? isSynced,
  });
  
  @override
  List<Object?> get props => [
    id, 
    createdAt, 
    updatedAt, 
    lastModifiedBy, 
    isDeleted,
    isSynced,
  ];
}
