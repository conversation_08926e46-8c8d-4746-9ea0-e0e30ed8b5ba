import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
// import '../widgets/widgets.dart'; // Не використовується

/// Екран нагадувань
class RemindersScreen extends StatefulWidget {
  /// Конструктор
  const RemindersScreen({super.key});

  @override
  State<RemindersScreen> createState() => _RemindersScreenState();
}

class _RemindersScreenState extends State<RemindersScreen> {
  final NotificationService _notificationService = NotificationService();
  final AuthService _authService = AuthService();

  List<Reminder> _reminders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReminders();
  }

  Future<void> _loadReminders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentEmployee = await _authService.getCurrentEmployee();
      if (currentEmployee != null) {
        try {
          final reminders = await _notificationService.getRemindersByEmployee(
            currentEmployee.id,
          );

          setState(() {
            _reminders = reminders;
            _isLoading = false;
          });
        } catch (e) {
          // Помилка при отриманні нагадувань (наприклад, таблиця не існує)
          setState(() {
            _reminders = [];
            _isLoading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  AppLocalizations.of(context).translate('no_reminders'),
                ),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        }
      } else {
        setState(() {
          _reminders = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _reminders = [];
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _dismissReminder(String id) async {
    await _notificationService.dismissReminder(id);
    await _loadReminders();
  }

  Future<void> _snoozeReminder(String id, Duration duration) async {
    await _notificationService.snoozeReminder(id, duration);
    await _loadReminders();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('reminders')),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReminders,
            tooltip: localizations.translate('refresh'),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _reminders.isEmpty
              ? Center(
                child: Text(
                  localizations.translate('no_reminders'),
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              )
              : ListView.builder(
                itemCount: _reminders.length,
                itemBuilder: (context, index) {
                  final reminder = _reminders[index];
                  return _buildReminderCard(context, reminder);
                },
              ),
    );
  }

  Widget _buildReminderCard(BuildContext context, Reminder reminder) {
    final localizations = AppLocalizations.of(context);
    final theme = Theme.of(context);

    // Визначення кольору картки в залежності від статусу
    Color cardColor;
    switch (reminder.status) {
      case ReminderStatus.active:
        cardColor = Color.fromRGBO(
          theme.colorScheme.primary.red.toInt(),
          theme.colorScheme.primary.green.toInt(),
          theme.colorScheme.primary.blue.toInt(),
          0.1,
        );
        break;
      case ReminderStatus.shown:
        cardColor = Color.fromRGBO(
          theme.colorScheme.secondary.red.toInt(),
          theme.colorScheme.secondary.green.toInt(),
          theme.colorScheme.secondary.blue.toInt(),
          0.1,
        );
        break;
      case ReminderStatus.snoozed:
        cardColor = Color.fromRGBO(
          theme.colorScheme.tertiary.red.toInt(),
          theme.colorScheme.tertiary.green.toInt(),
          theme.colorScheme.tertiary.blue.toInt(),
          0.1,
        );
        break;
      case ReminderStatus.dismissed:
        cardColor = theme.colorScheme.surface;
        break;
    }

    return Card(
      color: cardColor,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _showReminderDetails(context, reminder),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      reminder.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    localizations.translate(
                      reminder.status.getLocalizationKey(),
                    ),
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                reminder.message,
                style: theme.textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.formatDateTime(reminder.scheduledFor),
                    style: theme.textTheme.bodySmall,
                  ),
                  if (reminder.status != ReminderStatus.dismissed)
                    Row(
                      children: [
                        if (reminder.status != ReminderStatus.snoozed)
                          TextButton(
                            onPressed:
                                () => _showSnoozeOptions(context, reminder),
                            child: Text(localizations.translate('snooze')),
                          ),
                        TextButton(
                          onPressed: () => _dismissReminder(reminder.id),
                          child: Text(localizations.translate('dismiss')),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showReminderDetails(BuildContext context, Reminder reminder) {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations.translate('reminder_details')),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  reminder.title,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(reminder.message),
                const SizedBox(height: 16),
                Text(
                  '${localizations.translate('status')}: ${localizations.translate(reminder.status.getLocalizationKey())}',
                ),
                const SizedBox(height: 8),
                Text(
                  '${localizations.translate('date')}: ${localizations.formatDate(reminder.relatedDate)}',
                ),
                const SizedBox(height: 8),
                Text(
                  '${localizations.translate('scheduled_for')}: ${localizations.formatDateTime(reminder.scheduledFor)}',
                ),
              ],
            ),
            actions: [
              if (reminder.status != ReminderStatus.dismissed)
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _dismissReminder(reminder.id);
                  },
                  child: Text(localizations.translate('dismiss')),
                ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.translate('close')),
              ),
            ],
          ),
    );
  }

  void _showSnoozeOptions(BuildContext context, Reminder reminder) {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder:
          (context) => SimpleDialog(
            title: Text(localizations.translate('snooze_for')),
            children: [
              SimpleDialogOption(
                onPressed: () {
                  Navigator.of(context).pop();
                  _snoozeReminder(reminder.id, const Duration(hours: 1));
                },
                child: Text(localizations.translate('snooze_for_1_hour')),
              ),
              SimpleDialogOption(
                onPressed: () {
                  Navigator.of(context).pop();
                  _snoozeReminder(reminder.id, const Duration(hours: 3));
                },
                child: Text(localizations.translate('snooze_for_3_hours')),
              ),
              SimpleDialogOption(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Відкладення до завтра (9:00)
                  final now = DateTime.now();
                  final tomorrow = DateTime(
                    now.year,
                    now.month,
                    now.day + 1,
                    9,
                    0,
                  );
                  final duration = tomorrow.difference(now);
                  _snoozeReminder(reminder.id, duration);
                },
                child: Text(localizations.translate('snooze_for_tomorrow')),
              ),
            ],
          ),
    );
  }
}
