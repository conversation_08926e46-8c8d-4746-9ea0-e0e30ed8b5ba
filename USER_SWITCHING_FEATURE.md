# Функціонал переключення між користувачами

## 🎯 Опис функціоналу

Додано можливість швидкого переключення між різними працівниками/акаунтами без необхідності повного виходу з системи та повторного входу.

## ✨ Основні можливості

### 🔄 **Швидке переключення**
- Переключення між користувачами одним кліком
- Збереження списку недавніх користувачів (до 5)
- Автоматичне оновлення стану додатку

### 👥 **Управління користувачами**
- Перегляд всіх доступних працівників
- Відображення недавніх користувачів окремо
- Візуальне позначення поточного користувача

### 🎨 **Зручний інтерфейс**
- Карточки користувачів з аватарами
- Інформація про посаду та email
- Кнопка додавання нового користувача

## 📱 Як користуватися

### 1. **Доступ до функції**
- Відкрийте екран "Профіль"
- Натисніть кнопку "Переключити користувача" (синя кнопка з іконкою)

### 2. **Вибір користувача**
- У розділі "Недавні користувачі" - швидкий доступ до останніх 5 користувачів
- У розділі "Всі користувачі" - повний список працівників
- Поточний користувач позначений зеленою галочкою

### 3. **Переключення**
- Натисніть на карточку бажаного користувача
- Додаток автоматично переключиться та поверне на головний екран

### 4. **Додавання нового користувача**
- Натисніть кнопку "Додати користувача" (FAB)
- Перейдете на екран входу для нового акаунту

## 🔧 Технічна реалізація

### 📁 **Нові файли:**
- `user_switch_screen.dart` - екран переключення користувачів

### 🔄 **Змінені файли:**
- `auth_service.dart` - додані методи для переключення
- `profile_screen.dart` - додана кнопка переключення
- `uk.dart` / `en.dart` - додані переклади

### 🗄️ **Збереження даних:**
- Список недавніх користувачів зберігається в `SharedPreferences`
- Максимум 5 недавніх користувачів
- Автоматичне оновлення при кожному переключенні

## 📋 Методи AuthService

### `switchToUser(Employee employee)`
- Переключення на вибраного користувача
- Збереження в недавні користувачі
- Оновлення поточного стану

### `getAllEmployeesForSwitch()`
- Отримання списку всіх працівників для переключення

### `getRecentUsers()`
- Отримання списку недавніх користувачів (до 5)

### `_addToRecentUsers(Employee employee)`
- Додавання користувача до списку недавніх
- Автоматичне обмеження до 5 записів

## 🌍 Переклади

### Українська:
- `switch_user` - "Переключити користувача"
- `recent_users` - "Недавні користувачі"
- `all_users` - "Всі користувачі"
- `recent` - "Недавній"
- `add_user` - "Додати користувача"
- `no_users_found` - "Користувачів не знайдено"

### English:
- `switch_user` - "Switch User"
- `recent_users` - "Recent Users"
- `all_users` - "All Users"
- `recent` - "Recent"
- `add_user` - "Add User"
- `no_users_found` - "No users found"

## 🎨 UI/UX особливості

### **Карточки користувачів:**
- Аватар з першою літерою імені
- Повне ім'я користувача
- Email адреса
- Посада (якщо вказана)
- Мітка "Недавній" для недавніх користувачів
- Зелена галочка для поточного користувача

### **Кольорова схема:**
- Синя кнопка "Переключити користувача"
- Помаранчева мітка "Недавній"
- Зелена галочка для поточного користувача
- Сіра стрілка для доступних користувачів

## 🔒 Безпека

- Переключення доступне тільки між існуючими працівниками
- Збереження тільки ID користувачів, не паролів
- Автоматичне оновлення сесії при переключенні

## 🚀 Переваги

1. **Швидкість** - миттєве переключення без повторного входу
2. **Зручність** - збереження недавніх користувачів
3. **Безпека** - не зберігаються паролі
4. **Інтуїтивність** - зрозумілий інтерфейс
5. **Багатомовність** - підтримка української та англійської мов

## 📝 Примітки

- Функція особливо корисна для адміністраторів та керівників
- Дозволяє швидко перевіряти дані різних працівників
- Зберігає контекст роботи при переключенні
- Автоматично оновлює всі екрани додатку
