# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Kotlin specific rules
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**

# SQLite specific rules
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }

# Зберігаємо моделі даних
-keep class com.yourcompany.tabelapp.models.** { *; }

# Правила для JSON серіалізації
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Загальні правила для Android
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception
-keep class androidx.** { *; }
-keep class android.** { *; }

# Правила для збереження стеку помилок
-renamesourcefileattribute SourceFile
