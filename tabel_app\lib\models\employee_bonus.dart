import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Тип персональної надбавки
enum BonusType {
  /// Надбавка за шкідливі умови
  hazardous,

  /// Надбавка за стаж
  experience,

  /// Надбавка за кваліфікацію
  qualification,

  /// Надбавка за понаднормову роботу
  overtime,

  /// Персональна надбавка
  personal,

  /// Надбавка за досягнення
  achievement,
}

/// Тип нарахування надбавки
enum BonusCalculationType {
  /// Відсоток від ставки
  percentage,

  /// Фіксована сума на годину
  hourly,

  /// Фіксована сума на день
  daily,

  /// Фіксована сума на місяць
  monthly,
}

/// Модель персональної надбавки працівника
class EmployeeBonus extends Equatable {
  /// Унікальний ідентифікатор
  final String id;

  /// Ідентифікатор працівника
  final String employeeId;

  /// Тип надбавки
  final BonusType type;

  /// Значення надбавки (відсоток або сума)
  final double value;

  /// Тип нарахування надбавки
  final BonusCalculationType calculationType;

  /// Назва надбавки
  final String name;

  /// Опис надбавки
  final String? description;

  /// Дата початку дії надбавки
  final DateTime startDate;

  /// Дата закінчення дії надбавки (null - безстроково)
  final DateTime? endDate;

  /// Конструктор
  EmployeeBonus({
    String? id,
    required this.employeeId,
    required this.type,
    required this.value,
    required this.name,
    this.description,
    required this.startDate,
    this.endDate,
    this.calculationType = BonusCalculationType.percentage,
  }) : id = id ?? const Uuid().v4();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  EmployeeBonus copyWith({
    String? employeeId,
    BonusType? type,
    double? value,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    BonusCalculationType? calculationType,
  }) {
    return EmployeeBonus(
      id: this.id,
      employeeId: employeeId ?? this.employeeId,
      type: type ?? this.type,
      value: value ?? this.value,
      name: name ?? this.name,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      calculationType: calculationType ?? this.calculationType,
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'type': type.index,
      'value': value,
      'name': name,
      'description': description,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate?.millisecondsSinceEpoch,
      'calculationType': calculationType.index,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory EmployeeBonus.fromMap(Map<String, dynamic> map) {
    return EmployeeBonus(
      id: map['id'],
      employeeId: map['employeeId'],
      type: BonusType.values[map['type']],
      value: map['value'],
      name: map['name'],
      description: map['description'],
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      endDate:
          map['endDate'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['endDate'])
              : null,
      calculationType:
          map['calculationType'] != null
              ? BonusCalculationType.values[map['calculationType']]
              : BonusCalculationType.percentage,
    );
  }

  /// Перевірка, чи активна надбавка на вказану дату
  bool isActiveOn(DateTime date) {
    return startDate.isBefore(date) &&
        (endDate == null || endDate!.isAfter(date));
  }

  @override
  List<Object?> get props => [
    id,
    employeeId,
    type,
    value,
    name,
    description,
    startDate,
    endDate,
    calculationType,
  ];
}
