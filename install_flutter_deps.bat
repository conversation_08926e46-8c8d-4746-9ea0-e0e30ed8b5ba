@echo off
echo ========================================
echo Установка зависимостей Flutter
echo ========================================
echo.

REM Проверяем Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter не найден! Сначала запустите setup_flutter_path.bat
    pause
    exit /b 1
)

echo ✅ Flutter найден!
echo.

REM Включаем поддержку Windows desktop
echo Включаем поддержку Windows desktop...
flutter config --enable-windows-desktop
echo.

REM Включаем поддержку Web
echo Включаем поддержку Web...
flutter config --enable-web
echo.

REM Обновляем Flutter
echo Обновляем Flutter...
flutter upgrade
echo.

REM Проверяем доктора
echo Запускаем диагностику...
flutter doctor
echo.

REM Принимаем лицензии Android (если нужно)
echo Принимаем лицензии Android...
flutter doctor --android-licenses
echo.

echo ========================================
echo Установка зависимостей завершена!
echo ========================================
echo.
echo Теперь можно запускать Flutter проекты:
echo - flutter run -d windows (для Windows)
echo - flutter run -d chrome (для Web)
echo.
pause
