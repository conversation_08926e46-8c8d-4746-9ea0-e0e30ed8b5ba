# Run with `dart run ffigen --config ffigen.yaml`.
name: NotificationsPluginBindings
description: |
  Bindings for `src/ffi_api.h`.

  Regenerate bindings with `dart run ffigen --config ffigen.yaml`.
output: 'lib/src/ffi/bindings.dart'

silence-enum-warning: true

headers:
  entry-points:
    - 'src/ffi_api.h'
  include-directives:
    - 'src/ffi_api.h'

preamble: |
  // ignore_for_file: always_specify_types
  // ignore_for_file: camel_case_types
  // ignore_for_file: non_constant_identifier_names

comments:
  style: any
  length: full

type-map:
  native-types:
    'char':  # Converts `char` to `Utf8` instead of `Char`
      'lib': 'pkg_ffi'
      'c-type': 'Utf8'
      'dart-type': 'Utf8'
