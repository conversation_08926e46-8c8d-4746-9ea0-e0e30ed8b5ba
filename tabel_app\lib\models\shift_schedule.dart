import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'day_of_week.dart';

/// Модель для графіку змін
class ShiftSchedule {
  /// Унікальний ідентифікатор графіку
  final String id;

  /// Назва графіку (наприклад, "Тризмінний графік", "Графік 2/2" тощо)
  final String name;

  /// Список ID змін в порядку їх чергування
  final List<String> shiftOrder;

  /// Індекс поточної зміни в списку shiftOrder
  final int currentShiftIndex;

  /// Дата початку графіку
  final DateTime startDate;

  /// Перший вихідний день тижня
  final DayOfWeek dayOff1;

  /// Другий вихідний день тижня (опціонально)
  final DayOfWeek? dayOff2;

  /// Чи є вихідні плаваючими (не прив'язані до днів тижня)
  final bool hasFloatingDaysOff;

  /// Кількість робочих днів підряд (для плаваючих вихідних)
  final int workDaysCount;

  /// Кількість вихідних днів підряд (для плаваючих вихідних)
  final int daysOffCount;

  /// Конструктор
  ShiftSchedule({
    String? id,
    required this.name,
    required this.shiftOrder,
    required this.currentShiftIndex,
    required this.startDate,
    required this.dayOff1,
    this.dayOff2,
    this.hasFloatingDaysOff = false,
    this.workDaysCount = 5,
    this.daysOffCount = 2,
  }) : id = id ?? const Uuid().v4();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  ShiftSchedule copyWith({
    String? name,
    List<String>? shiftOrder,
    int? currentShiftIndex,
    DateTime? startDate,
    DayOfWeek? dayOff1,
    DayOfWeek? dayOff2,
    bool? hasFloatingDaysOff,
    int? workDaysCount,
    int? daysOffCount,
  }) {
    return ShiftSchedule(
      id: this.id,
      name: name ?? this.name,
      shiftOrder: shiftOrder ?? this.shiftOrder,
      currentShiftIndex: currentShiftIndex ?? this.currentShiftIndex,
      startDate: startDate ?? this.startDate,
      dayOff1: dayOff1 ?? this.dayOff1,
      dayOff2: dayOff2 ?? this.dayOff2,
      hasFloatingDaysOff: hasFloatingDaysOff ?? this.hasFloatingDaysOff,
      workDaysCount: workDaysCount ?? this.workDaysCount,
      daysOffCount: daysOffCount ?? this.daysOffCount,
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'shiftOrder': jsonEncode(shiftOrder),
      'currentShiftIndex': currentShiftIndex,
      'startDate': startDate.millisecondsSinceEpoch,
      'dayOff1': dayOff1.value,
      'dayOff2': dayOff2?.value,
      'hasFloatingDaysOff': hasFloatingDaysOff ? 1 : 0,
      'workDaysCount': workDaysCount,
      'daysOffCount': daysOffCount,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory ShiftSchedule.fromMap(Map<String, dynamic> map) {
    return ShiftSchedule(
      id: map['id'],
      name: map['name'],
      shiftOrder: List<String>.from(jsonDecode(map['shiftOrder'])),
      currentShiftIndex: map['currentShiftIndex'],
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      dayOff1: DayOfWeek.fromValue(map['dayOff1']),
      dayOff2:
          map['dayOff2'] != null ? DayOfWeek.fromValue(map['dayOff2']) : null,
      hasFloatingDaysOff: map['hasFloatingDaysOff'] == 1,
      workDaysCount: map['workDaysCount'] ?? 5,
      daysOffCount: map['daysOffCount'] ?? 2,
    );
  }

  /// Отримання ID зміни для вказаної дати
  String? getShiftIdForDate(DateTime date) {
    if (shiftOrder.isEmpty) {
      return null;
    }

    // Якщо дата вихідний, повертаємо null
    if (isDayOff(date)) {
      return null;
    }

    // Розрахунок кількості днів від початку графіку
    final difference = date.difference(startDate).inDays;

    // Якщо дата раніше початку графіку, повертаємо null
    if (difference < 0) {
      return null;
    }

    // Кількість робочих тижнів від початку графіку до вказаної дати
    int workWeeksCount = 0;

    // Знаходимо понеділок тижня для вказаної дати
    final mondayOfWeek = date.subtract(Duration(days: date.weekday - 1));

    // Знаходимо понеділок тижня для початкової дати
    final startMondayOfWeek = startDate.subtract(
      Duration(days: startDate.weekday - 1),
    );

    // Розрахунок кількості тижнів між понеділками
    final weeksDifference =
        mondayOfWeek.difference(startMondayOfWeek).inDays ~/ 7;

    // Якщо тиждень раніше початкового тижня, повертаємо null
    if (weeksDifference < 0) {
      return null;
    }

    // Перебираємо всі тижні від початкового до поточного
    for (int week = 0; week <= weeksDifference; week++) {
      final weekStart = startMondayOfWeek.add(Duration(days: week * 7));

      // Перевіряємо, чи є в цьому тижні хоча б один робочий день
      bool hasWorkDays = false;
      for (int day = 0; day < 7; day++) {
        final checkDate = weekStart.add(Duration(days: day));
        if (!isDayOff(checkDate) && checkDate.weekday <= 5) {
          // Понеділок-П'ятниця
          hasWorkDays = true;
          break;
        }
      }

      if (hasWorkDays) {
        workWeeksCount++;
      }
    }

    // Розрахунок індексу зміни для вказаного тижня
    // Віднімаємо 1, оскільки рахуємо з 0
    final adjustedIndex =
        (currentShiftIndex + workWeeksCount - 1) % shiftOrder.length;

    return shiftOrder[adjustedIndex];
  }

  /// Перевірка, чи є вказана дата вихідним
  bool isDayOff(DateTime date) {
    if (hasFloatingDaysOff) {
      // Для плаваючих вихідних
      final totalDays = workDaysCount + daysOffCount;
      final daysSinceStart = date.difference(startDate).inDays;

      // Якщо дата раніше початку графіку, вважаємо робочим днем
      if (daysSinceStart < 0) {
        return false;
      }

      // Розрахунок позиції в циклі
      final cyclePosition = daysSinceStart % totalDays;

      // Вихідні дні йдуть після робочих днів
      return cyclePosition >= workDaysCount;
    } else {
      // Для фіксованих вихідних
      final dayOfWeek = DayOfWeek.fromDateTime(date);
      return dayOfWeek == dayOff1 || dayOfWeek == dayOff2;
    }
  }

  /// Отримання наступного індексу зміни
  int getNextShiftIndex() {
    return (currentShiftIndex + 1) % shiftOrder.length;
  }

  /// Отримання попереднього індексу зміни
  int getPreviousShiftIndex() {
    return (currentShiftIndex - 1 + shiftOrder.length) % shiftOrder.length;
  }
}
