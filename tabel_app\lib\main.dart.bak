import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';

import 'models/models.dart';
import 'services/services.dart';
import 'utils/utils.dart';
import 'screens/screens.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Ініціалізація SQLite для Windows
  if (Platform.isWindows || Platform.isLinux) {
    // Ініціалізація sqflite_common_ffi
    sqfliteFfiInit();
    // Встановлення databaseFactory
    databaseFactory = databaseFactoryFfi;
  }

  // Ініціалізація Firebase
  // Вимкнено для Windows, оскільки викликає проблеми
  // try {
  //   await Firebase.initializeApp(
  //     options: DefaultFirebaseOptions.currentPlatform,
  //   );
  // } catch (e) {
  //   print('Firebase initialization error: $e');
  // }

  // Встановлення орієнтації екрану
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // Отримання збережених налаштувань
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  final String languageCode = prefs.getString('languageCode') ?? 'uk';
  final bool isDarkMode = prefs.getBool('isDarkMode') ?? false;
  final String countryCode = prefs.getString('countryCode') ?? 'UA';

  // Імпорт святкових днів
  final calendarService = CalendarService();
  await calendarService.importHolidays(countryCode);

  runApp(
    MyApp(
      languageCode: languageCode,
      isDarkMode: isDarkMode,
      countryCode: countryCode,
    ),
  );
}

class MyApp extends StatefulWidget {
  final String languageCode;
  final bool isDarkMode;
  final String countryCode;

  const MyApp({
    super.key,
    required this.languageCode,
    required this.isDarkMode,
    required this.countryCode,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late AppLanguage _language;
  late bool _isDarkMode;
  late AppCountry _country;
  final AuthService _authService = AuthService();
  Employee? _currentEmployee;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _language = AppLanguage.values.firstWhere(
      (lang) => lang.code == widget.languageCode,
      orElse: () => AppLanguage.ukrainian,
    );
    _isDarkMode = widget.isDarkMode;
    _country = AppCountry.fromCode(widget.countryCode);
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      _currentEmployee = await _authService.getCurrentEmployee();
    } catch (e) {
      // Помилка завантаження користувача
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<AuthService>(create: (_) => _authService),
        Provider<DatabaseService>(create: (_) => DatabaseService()),
        Provider<CalendarService>(create: (_) => CalendarService()),
        Provider<CalculationService>(create: (_) => CalculationService()),
        Provider<SyncService>(create: (_) => SyncService()),
        Provider<PdfService>(create: (_) => PdfService()),
        Provider<AbsenceService>(create: (_) => AbsenceService()),
        Provider<AnalyticsService>(create: (_) => AnalyticsService()),
        ChangeNotifierProvider<AppState>(
          create:
              (_) => AppState(
                language: _language,
                isDarkMode: _isDarkMode,
                currentEmployee: _currentEmployee,
                country: _country,
              ),
        ),
      ],
      child: Consumer<AppState>(
        builder: (context, appState, _) {
          return MaterialApp(
            title: 'Tabel App',
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.blue,
                brightness:
                    appState.isDarkMode ? Brightness.dark : Brightness.light,
              ),
              useMaterial3: true,
            ),
            darkTheme: ThemeData(
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.blue,
                brightness: Brightness.dark,
              ),
              useMaterial3: true,
            ),
            themeMode: appState.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            locale: appState.language.locale,
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            home:
                _isLoading
                    ? const SplashScreen()
                    : appState.currentEmployee == null
                    ? const LoginScreen()
                    : const HomeScreen(),
          );
        },
      ),
    );
  }
}

/// Клас для зберігання стану додатку
class AppState extends ChangeNotifier {
  AppLanguage _language;
  bool _isDarkMode;
  Employee? _currentEmployee;
  AppCountry _country;

  AppState({
    required AppLanguage language,
    required bool isDarkMode,
    Employee? currentEmployee,
    AppCountry? country,
  }) : _language = language,
       _isDarkMode = isDarkMode,
       _currentEmployee = currentEmployee,
       _country = country ?? AppCountry.ukraine;

  AppLanguage get language => _language;
  bool get isDarkMode => _isDarkMode;
  Employee? get currentEmployee => _currentEmployee;
  AppCountry get country => _country;

  void setLanguage(AppLanguage language) async {
    _language = language;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', language.code);
    notifyListeners();
  }

  void setThemeMode(bool isDarkMode) async {
    _isDarkMode = isDarkMode;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', isDarkMode);
    notifyListeners();
  }

  void setCountry(AppCountry country) async {
    _country = country;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('countryCode', country.code);

    // Імпорт святкових днів для нової країни
    final calendarService = CalendarService();
    await calendarService.importHolidays(country.code);

    notifyListeners();
  }

  void setCurrentEmployee(Employee? employee) {
    _currentEmployee = employee;
    notifyListeners();
  }
}
