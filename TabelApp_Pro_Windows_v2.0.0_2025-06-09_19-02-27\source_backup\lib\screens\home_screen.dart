import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';
import 'timesheet_tab.dart';
import 'reports_tab.dart';
import 'settings_tab.dart';
import 'profile_screen.dart';
import 'employee_bonuses_screen.dart';
import 'employees_list_screen.dart';
import 'absence_list_screen.dart';
import 'reminders_screen.dart';
import 'login_screen.dart';

/// Головний екран додатку
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  final NotificationService _notificationService = NotificationService();

  @override
  void initState() {
    super.initState();
    // Ініціалізація сервісу нагадувань та перевірка відсутності даних у табелі
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    try {
      await _notificationService.initialize();
      await _notificationService.requestPermissions();

      // Перевірка відсутності даних у табелі за попередній день
      final authService = AuthService();
      final employee = await authService.getCurrentEmployee();
      if (employee != null) {
        final localizations = AppLocalizations(const Locale('uk'));
        try {
          await _notificationService.checkMissingTimesheetData(
            employee.id,
            employee.fullName,
            localizations,
          );
        } catch (e) {
          // Ігноруємо помилки при перевірці відсутності даних
          // Наприклад, якщо таблиця reminders ще не створена
          debugPrint('Error checking missing timesheet data: $e');
        }
      }
    } catch (e) {
      // Ігноруємо помилки при ініціалізації сервісу нагадувань
      debugPrint('Error initializing notification service: $e');
    }
  }

  /// Зміна вибраного індексу
  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  /// Отримання заголовка екрану
  String _getTitle(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (_selectedIndex) {
      case 0:
        return localizations.translate('timesheet');
      case 1:
        return localizations.translate('reports');
      case 2:
        return localizations.translate('absences');
      case 3:
        return localizations.translate('settings');
      default:
        return localizations.translate('app_name');
    }
  }

  /// Отримання вмісту екрану
  Widget _getBody() {
    switch (_selectedIndex) {
      case 0:
        return const TimesheetTab();
      case 1:
        return const ReportsTab();
      case 2:
        return const AbsenceListScreen();
      case 3:
        return const SettingsTab();
      default:
        return const TimesheetTab();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final appState = Provider.of<AppState>(context);
    final employee = appState.currentEmployee;

    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle(context)),
        actions: [
          // Кнопка нагадувань
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const RemindersScreen(),
                ),
              );
            },
            tooltip: localizations.translate('reminders'),
          ),
          // Кнопка синхронізації
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: () async {
              try {
                final syncService = Provider.of<SyncService>(
                  context,
                  listen: false,
                );

                // Перевірка підключення до інтернету
                final isConnected = await syncService.isConnected();
                if (!isConnected) {
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(localizations.translate('no_internet')),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Синхронізація даних
                await syncService.syncData();

                if (!mounted) return;
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(localizations.translate('sync_success')),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                if (!mounted) return;
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      '${localizations.translate('sync_error')}: ${e.toString()}',
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            tooltip: localizations.translate('sync'),
          ),
          // Кнопка профілю
          IconButton(
            icon: const Icon(Icons.account_circle),
            onPressed: () {
              // Перехід на екран профілю
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ProfileScreen()),
              );
            },
            tooltip: localizations.translate('profile'),
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            // Шапка бокового меню
            UserAccountsDrawerHeader(
              accountName: Text(employee?.fullName ?? ''),
              accountEmail: Text(employee?.email ?? ''),
              currentAccountPicture: CircleAvatar(
                backgroundColor: Colors.white,
                child: Text(
                  employee?.firstName.isNotEmpty == true
                      ? employee!.firstName[0].toUpperCase()
                      : '',
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            // Пункти меню
            ListTile(
              leading: const Icon(Icons.access_time),
              title: Text(localizations.translate('timesheet')),
              selected: _selectedIndex == 0,
              onTap: () {
                _onItemTapped(0);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.bar_chart),
              title: Text(localizations.translate('reports')),
              selected: _selectedIndex == 1,
              onTap: () {
                _onItemTapped(1);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.event_busy),
              title: Text(localizations.translate('absences')),
              selected: _selectedIndex == 2,
              onTap: () {
                _onItemTapped(2);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: Text(localizations.translate('settings')),
              selected: _selectedIndex == 3,
              onTap: () {
                _onItemTapped(3);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: Text(localizations.translate('reminders')),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RemindersScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.account_circle),
              title: Text(localizations.translate('profile')),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
            ),
            // Пункт меню "Працівники" (тільки для адміністраторів)
            if (employee?.isAdmin == true)
              ListTile(
                leading: const Icon(Icons.people),
                title: const Text('Працівники'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const EmployeesListScreen(),
                    ),
                  );
                },
              ),
            const Divider(),
            // Кнопка виходу
            ListTile(
              leading: const Icon(Icons.exit_to_app),
              title: Text(localizations.translate('logout')),
              onTap: () async {
                try {
                  final authService = Provider.of<AuthService>(
                    context,
                    listen: false,
                  );
                  await authService.signOut();
                  if (!mounted) return;
                  Provider.of<AppState>(
                    context,
                    listen: false,
                  ).setCurrentEmployee(null);

                  // Перехід на екран входу з видаленням всіх попередніх екранів
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(
                      builder: (context) => const LoginScreen(),
                    ),
                    (route) => false,
                  );
                } catch (e) {
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error: ${e.toString()}')),
                  );
                }
              },
            ),
          ],
        ),
      ),
      body: _getBody(),
      bottomNavigationBar: BottomNavigationBar(
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.access_time),
            label: localizations.translate('timesheet'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.bar_chart),
            label: localizations.translate('reports'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.event_busy),
            label: localizations.translate('absences'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings),
            label: localizations.translate('settings'),
          ),
        ],
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}

// Вкладка табеля перенесена в окремий файл timesheet_tab.dart

// Вкладка звітів перенесена в окремий файл reports_tab.dart

// Вкладка налаштувань перенесена в окремий файл settings_tab.dart
