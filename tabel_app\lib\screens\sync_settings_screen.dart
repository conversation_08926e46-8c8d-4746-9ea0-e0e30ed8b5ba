import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/sync_settings.dart';
import '../models/conflict_resolution.dart';
import '../services/settings_service.dart';
import '../services/background_sync_service.dart';
import '../services/sync_service.dart';
import '../services/offline_sync_queue.dart';
import '../utils/utils.dart';

/// Екран налаштувань синхронізації
class SyncSettingsScreen extends StatefulWidget {
  const SyncSettingsScreen({Key? key}) : super(key: key);

  @override
  _SyncSettingsScreenState createState() => _SyncSettingsScreenState();
}

class _SyncSettingsScreenState extends State<SyncSettingsScreen> {
  late SyncSettings _settings;
  bool _isLoading = true;
  bool _isSyncing = false;
  int _pendingOperations = 0;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// Завантаження налаштувань
  Future<void> _loadSettings() async {
    try {
      final settingsService = Provider.of<SettingsService>(
        context,
        listen: false,
      );
      final settings = await settingsService.getSyncSettings();

      final pendingOperations =
          await OfflineSyncQueue.getUnprocessedQueueLength();

      debugPrint('Loaded sync settings: ${settings.toJsonString()}');

      setState(() {
        _settings = settings;
        _pendingOperations = pendingOperations;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading sync settings: $e');

      setState(() {
        _settings = SyncSettings();
        _isLoading = false;
      });
    }
  }

  /// Збереження налаштувань
  Future<void> _saveSettings() async {
    try {
      final settingsService = Provider.of<SettingsService>(
        context,
        listen: false,
      );
      debugPrint('Saving sync settings: ${_settings.toJsonString()}');

      await settingsService.saveSyncSettings(_settings);

      // Оновлення планування фонової синхронізації
      await BackgroundSyncService.scheduleSync(_settings);

      // Перевірка збережених налаштувань
      final savedSettings = await settingsService.getSyncSettings();
      debugPrint('Verified saved settings: ${savedSettings.toJsonString()}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).translate('settings_saved'),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving sync settings: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).translate('error_saving_settings')}: ${e.toString()}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Запуск синхронізації
  Future<void> _syncNow() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      // Заглушка для перевірки підключення до мережі
      debugPrint('Checking network connection...');

      // Заглушка для синхронізації даних
      debugPrint('Syncing data...');

      // Оновлення кількості операцій у черзі
      final pendingOperations =
          await OfflineSyncQueue.getUnprocessedQueueLength();

      setState(() {
        _pendingOperations = pendingOperations;
        _isSyncing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).translate('sync_completed'),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error syncing data: $e');

      setState(() {
        _isSyncing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).translate('sync_error')}: ${e.toString()}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations.translate('sync_settings'))),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Увімкнення/вимкнення синхронізації
                    SwitchListTile(
                      title: Text(localizations.translate('enable_sync')),
                      subtitle: Text(
                        localizations.translate('enable_sync_description'),
                      ),
                      value: _settings.enabled,
                      onChanged: (value) {
                        setState(() {
                          _settings = _settings.copyWith(enabled: value);
                        });
                      },
                    ),

                    const Divider(),

                    // Частота синхронізації
                    ListTile(
                      title: Text(localizations.translate('sync_frequency')),
                      subtitle: Text(_settings.frequency.displayName),
                      enabled: _settings.enabled,
                      onTap:
                          _settings.enabled
                              ? () => _showFrequencyDialog()
                              : null,
                    ),

                    // Стратегія вирішення конфліктів
                    ListTile(
                      title: Text(
                        localizations.translate('conflict_resolution'),
                      ),
                      subtitle: Text(
                        _getConflictStrategyName(_settings.conflictStrategy),
                      ),
                      enabled: _settings.enabled,
                      onTap:
                          _settings.enabled
                              ? () => _showConflictStrategyDialog()
                              : null,
                    ),

                    // Синхронізація тільки через Wi-Fi
                    SwitchListTile(
                      title: Text(localizations.translate('wifi_only')),
                      subtitle: Text(
                        localizations.translate('wifi_only_description'),
                      ),
                      value: _settings.wifiOnly,
                      onChanged:
                          _settings.enabled
                              ? (value) {
                                setState(() {
                                  _settings = _settings.copyWith(
                                    wifiOnly: value,
                                  );
                                });
                              }
                              : null,
                    ),

                    // Синхронізація тільки при достатньому заряді батареї
                    SwitchListTile(
                      title: Text(localizations.translate('battery_not_low')),
                      subtitle: Text(
                        localizations.translate('battery_not_low_description'),
                      ),
                      value: _settings.batteryNotLow,
                      onChanged:
                          _settings.enabled
                              ? (value) {
                                setState(() {
                                  _settings = _settings.copyWith(
                                    batteryNotLow: value,
                                  );
                                });
                              }
                              : null,
                    ),

                    const Divider(),

                    // Інформація про останню синхронізацію
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        localizations.translate('sync_status'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),

                    // Кількість операцій у черзі
                    ListTile(
                      title: Text(
                        localizations.translate('pending_operations'),
                      ),
                      subtitle: Text(_pendingOperations.toString()),
                      trailing:
                          _pendingOperations > 0
                              ? const Icon(Icons.warning, color: Colors.orange)
                              : const Icon(Icons.check, color: Colors.green),
                    ),

                    const SizedBox(height: 16.0),

                    // Кнопка синхронізації
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isSyncing ? null : _syncNow,
                        icon:
                            _isSyncing
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.0,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : const Icon(Icons.sync),
                        label: Text(
                          _isSyncing
                              ? localizations.translate('syncing')
                              : localizations.translate('sync_now'),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12.0),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16.0),

                    // Кнопка збереження
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveSettings,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12.0),
                        ),
                        child: Text(localizations.translate('save_settings')),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  /// Відображення діалогу вибору частоти синхронізації
  Future<void> _showFrequencyDialog() async {
    final localizations = AppLocalizations.of(context);

    final result = await showDialog<SyncFrequency>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations.translate('select_sync_frequency')),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children:
                    SyncFrequency.values.map((frequency) {
                      return RadioListTile<SyncFrequency>(
                        title: Text(frequency.displayName),
                        value: frequency,
                        groupValue: _settings.frequency,
                        onChanged: (value) {
                          Navigator.of(context).pop(value);
                        },
                      );
                    }).toList(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.translate('cancel')),
              ),
            ],
          ),
    );

    if (result != null) {
      setState(() {
        _settings = _settings.copyWith(frequency: result);
      });
    }
  }

  /// Відображення діалогу вибору стратегії вирішення конфліктів
  Future<void> _showConflictStrategyDialog() async {
    final localizations = AppLocalizations.of(context);

    final result = await showDialog<ConflictResolutionStrategy>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations.translate('select_conflict_strategy')),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children:
                    ConflictResolutionStrategy.values.map((strategy) {
                      return RadioListTile<ConflictResolutionStrategy>(
                        title: Text(_getConflictStrategyName(strategy)),
                        subtitle: Text(
                          _getConflictStrategyDescription(strategy),
                        ),
                        value: strategy,
                        groupValue: _settings.conflictStrategy,
                        onChanged: (value) {
                          Navigator.of(context).pop(value);
                        },
                      );
                    }).toList(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.translate('cancel')),
              ),
            ],
          ),
    );

    if (result != null) {
      setState(() {
        _settings = _settings.copyWith(conflictStrategy: result);
      });
    }
  }

  /// Отримання назви стратегії вирішення конфліктів
  String _getConflictStrategyName(ConflictResolutionStrategy strategy) {
    final localizations = AppLocalizations.of(context);

    switch (strategy) {
      case ConflictResolutionStrategy.useLocal:
        return localizations.translate('use_local');
      case ConflictResolutionStrategy.useRemote:
        return localizations.translate('use_remote');
      case ConflictResolutionStrategy.useNewest:
        return localizations.translate('use_newest');
      case ConflictResolutionStrategy.useOldest:
        return localizations.translate('use_oldest');
      case ConflictResolutionStrategy.manual:
        return localizations.translate('manual_resolution');
    }
  }

  /// Отримання опису стратегії вирішення конфліктів
  String _getConflictStrategyDescription(ConflictResolutionStrategy strategy) {
    final localizations = AppLocalizations.of(context);

    switch (strategy) {
      case ConflictResolutionStrategy.useLocal:
        return localizations.translate('use_local_description');
      case ConflictResolutionStrategy.useRemote:
        return localizations.translate('use_remote_description');
      case ConflictResolutionStrategy.useNewest:
        return localizations.translate('use_newest_description');
      case ConflictResolutionStrategy.useOldest:
        return localizations.translate('use_oldest_description');
      case ConflictResolutionStrategy.manual:
        return localizations.translate('manual_resolution_description');
    }
  }
}
