@echo off
echo ========================================
echo Очищення бази даних Tabel App
echo ========================================
echo.
echo УВАГА: Це видалить всі ваші дані!
echo Переконайтеся, що у вас є резервна копія.
echo.
set /p confirm="Продовжити? (y/N): "
if /i not "%confirm%"=="y" (
    echo Скасовано.
    pause
    exit /b 0
)

echo.
echo Пошук та видалення файлів бази даних...

REM Знаходимо та видаляємо файли бази даних
if exist "%APPDATA%\tabel_app\timesheet.db" (
    echo Видаляємо: %APPDATA%\tabel_app\timesheet.db
    del /Q "%APPDATA%\tabel_app\timesheet.db"
)

if exist "%LOCALAPPDATA%\tabel_app\timesheet.db" (
    echo Видаляємо: %LOCALAPPDATA%\tabel_app\timesheet.db
    del /Q "%LOCALAPPDATA%\tabel_app\timesheet.db"
)

if exist "%USERPROFILE%\Documents\timesheet.db" (
    echo Видаляємо: %USERPROFILE%\Documents\timesheet.db
    del /Q "%USERPROFILE%\Documents\timesheet.db"
)

REM Очищуємо папки з даними додатку
if exist "%APPDATA%\tabel_app" (
    echo Видаляємо папку: %APPDATA%\tabel_app
    rmdir /S /Q "%APPDATA%\tabel_app"
)

if exist "%LOCALAPPDATA%\tabel_app" (
    echo Видаляємо папку: %LOCALAPPDATA%\tabel_app
    rmdir /S /Q "%LOCALAPPDATA%\tabel_app"
)

echo.
echo ✅ База даних очищена!
echo.
echo Тепер запустіть додаток - він створить нову базу даних
echo з правильною структурою.
echo.
pause
