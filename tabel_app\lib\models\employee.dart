import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import 'employee_bonus.dart';

/// Модель працівника
class Employee extends Equatable {
  /// Унікальний ідентифікатор працівника
  final String id;

  /// Ім'я працівника
  final String firstName;

  /// Прізвище працівника
  final String lastName;

  /// Ставка за годину (в грошових одиницях)
  final double hourlyRate;

  /// Посада працівника
  final String position;

  /// Email працівника (використовується для авторизації)
  final String email;

  /// Чи є працівник адміністратором
  final bool isAdmin;

  /// Дата початку роботи
  final DateTime? hireDate;

  /// Стаж роботи (в роках)
  final int? experienceYears;

  /// Рівень кваліфікації (1-5)
  final int? qualificationLevel;

  /// Список персональних надбавок
  final List<EmployeeBonus>? bonuses;

  /// Конструктор
  Employee({
    String? id,
    required this.firstName,
    required this.lastName,
    required this.hourlyRate,
    required this.position,
    required this.email,
    this.isAdmin = false,
    this.hireDate,
    this.experienceYears,
    this.qualificationLevel,
    this.bonuses,
  }) : id = id ?? const Uuid().v4();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  Employee copyWith({
    String? firstName,
    String? lastName,
    double? hourlyRate,
    String? position,
    String? email,
    bool? isAdmin,
    DateTime? hireDate,
    int? experienceYears,
    int? qualificationLevel,
    List<EmployeeBonus>? bonuses,
  }) {
    return Employee(
      id: this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      position: position ?? this.position,
      email: email ?? this.email,
      isAdmin: isAdmin ?? this.isAdmin,
      hireDate: hireDate ?? this.hireDate,
      experienceYears: experienceYears ?? this.experienceYears,
      qualificationLevel: qualificationLevel ?? this.qualificationLevel,
      bonuses: bonuses ?? this.bonuses,
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'hourlyRate': hourlyRate,
      'position': position,
      'email': email,
      'isAdmin': isAdmin ? 1 : 0, // Перетворюємо bool в int для SQLite
      'hireDate': hireDate?.millisecondsSinceEpoch,
      'experienceYears': experienceYears,
      'qualificationLevel': qualificationLevel,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'],
      firstName: map['firstName'],
      lastName: map['lastName'],
      hourlyRate: map['hourlyRate'],
      position: map['position'],
      email: map['email'],
      isAdmin: map['isAdmin'] == 1, // Перетворюємо int в bool з SQLite
      hireDate:
          map['hireDate'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['hireDate'])
              : null,
      experienceYears: map['experienceYears'],
      qualificationLevel: map['qualificationLevel'],
    );
  }

  /// Повне ім'я працівника
  String get fullName => '$firstName $lastName';

  @override
  List<Object?> get props => [
    id,
    firstName,
    lastName,
    hourlyRate,
    position,
    email,
    isAdmin,
    hireDate,
    experienceYears,
    qualificationLevel,
    bonuses,
  ];
}
