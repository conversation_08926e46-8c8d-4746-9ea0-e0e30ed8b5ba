@echo off
echo ========================================
echo Запуск Tabel App
echo ========================================
echo.

REM Перевіряємо наявність файлу
if not exist "tabel_app\build\windows\x64\runner\Release\tabel_app.exe" (
    echo ПОМИЛКА: Файл tabel_app.exe не знайдено!
    echo Шлях: tabel_app\build\windows\x64\runner\Release\tabel_app.exe
    pause
    exit /b 1
)

echo Файл знайдено: tabel_app.exe
echo.

REM Перевіряємо залежності
echo Перевірка залежностей...
if not exist "tabel_app\build\windows\x64\runner\Release\flutter_windows.dll" (
    echo ПОПЕРЕДЖЕННЯ: flutter_windows.dll не знайдено!
)

echo.
echo Запуск додатку...
cd /d "tabel_app\build\windows\x64\runner\Release"

REM Запускаємо з перенаправленням помилок
tabel_app.exe 2>error.log

REM Перевіряємо, чи є помилки
if exist error.log (
    echo.
    echo ПОМИЛКИ ПРИ ЗАПУСКУ:
    type error.log
    echo.
)

echo.
echo Додаток завершено.
pause
