workflows:
  ios-build:
    name: iOS Build without Signing
    instance_type: mac_mini_m1
    environment:
      flutter: stable
      xcode: latest
      cocoapods: default
    scripts:
      - name: Set up iOS project
        script: |
          echo "Current directory: $(pwd)"
          echo "Listing files in current directory:"
          ls -la
          echo "\nListing files in ios directory (if exists):"
          if [ -d "ios" ]; then
            ls -la ios
          else
            echo "iOS directory does not exist, creating it..."
            flutter create --platforms=ios .
          fi
          
          if [ ! -d "ios/Runner.xcodeproj" ]; then
            echo "\nRunner.xcodeproj not found, recreating iOS project..."
            rm -rf ios
            flutter create --platforms=ios .
          fi
          
          echo "\nListing files in ios directory after setup:"
          ls -la ios
          
          echo "\nListing files in ios/Runner.xcodeproj (if exists):"
          if [ -d "ios/Runner.xcodeproj" ]; then
            ls -la ios/Runner.xcodeproj
          else
            echo "ios/Runner.xcodeproj directory does not exist!"
          fi
          
          echo "\nListing files in ios/Runner (if exists):"
          if [ -d "ios/Runner" ]; then
            ls -la ios/Runner
          else
            echo "ios/Runner directory does not exist!"
          fi
          
          echo "\nListing files in ios/Flutter (if exists):"
          if [ -d "ios/Flutter" ]; then
            ls -la ios/Flutter
          else
            echo "ios/Flutter directory does not exist!"
            mkdir -p ios/Flutter
            touch ios/Flutter/.gitkeep
          fi
      - name: Get dependencies
        script: |
          flutter pub get
          cd ios
          pod repo update
          pod install
      - name: Build iOS (no code signing)
        script: |
          flutter build ios --release --no-codesign
    artifacts:
      - build/ios/iphoneos/Runner.app
      - build/ios/iphoneos/*.ipa
  
  android-build:
    name: Android Build
    environment:
      flutter: stable
      java: 17
    scripts:
      - name: Set up Android project
        script: |
          if [ ! -d "android" ]; then
            echo "Creating Android project..."
            flutter create --platforms=android .
          fi
      - name: Get dependencies
        script: flutter pub get
      - name: Build Android APK
        script: flutter build apk --release
      - name: Build Android App Bundle
        script: flutter build appbundle --release
    artifacts:
      - build/app/outputs/flutter-apk/app-release.apk
      - build/app/outputs/bundle/release/app-release.aab
