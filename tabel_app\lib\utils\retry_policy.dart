import 'dart:async';
import 'package:flutter/foundation.dart';

/// Клас для реалізації політики повторних спроб
class RetryPolicy {
  /// Максимальна кількість спроб
  final int maxAttempts;
  
  /// Початкова затримка між спробами
  final Duration initialDelay;
  
  /// Коефіцієнт збільшення затримки
  final double backoffFactor;

  const RetryPolicy({
    this.maxAttempts = 3,
    this.initialDelay = const Duration(seconds: 2),
    this.backoffFactor = 2.0,
  });
  
  /// Виконання операції з повторними спробами
  Future<T> execute<T>(Future<T> Function() operation) async {
    int attempts = 0;
    Duration delay = initialDelay;
    
    while (true) {
      try {
        attempts++;
        return await operation();
      } catch (e) {
        if (attempts >= maxAttempts) {
          debugPrint('Max retry attempts reached ($maxAttempts). Giving up.');
          rethrow;
        }
        
        debugPrint('Attempt $attempts failed. Retrying in ${delay.inSeconds} seconds...');
        await Future.delayed(delay);
        
        // Збільшення затримки для наступної спроби
        delay = Duration(milliseconds: 
          (delay.inMilliseconds * backoffFactor).round());
      }
    }
  }
}
