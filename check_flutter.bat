@echo off
echo ========================================
echo Проверка установки Flutter
echo ========================================
echo.

REM Проверяем доступность flutter команды
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter команда не найдена!
    echo.
    echo Возможные решения:
    echo 1. Запустите setup_flutter_path.bat
    echo 2. Перезапустите командную строку
    echo 3. Проверьте, что Flutter правильно распакован
    echo.
    pause
    exit /b 1
)

echo ✅ Flutter команда доступна!
echo.

REM Показываем версию Flutter
echo Версия Flutter:
flutter --version
echo.

REM Запускаем flutter doctor
echo Диагностика Flutter:
echo ========================================
flutter doctor
echo ========================================
echo.

REM Проверяем доступные устройства
echo Доступные устройства:
flutter devices
echo.

echo ========================================
echo Проверка завершена!
echo ========================================
pause
