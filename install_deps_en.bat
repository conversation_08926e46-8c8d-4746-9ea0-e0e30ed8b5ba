@echo off
chcp 65001 >nul
echo ========================================
echo Installing Flutter Dependencies
echo ========================================
echo.

REM Check Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Flutter not found! Run setup_flutter_en.bat first
    pause
    exit /b 1
)

echo Flutter found!
echo.

REM Enable Windows desktop support
echo Enabling Windows desktop support...
flutter config --enable-windows-desktop
echo.

REM Enable Web support
echo Enabling Web support...
flutter config --enable-web
echo.

REM Update Flutter
echo Updating Flutter...
flutter upgrade
echo.

REM Run doctor
echo Running diagnostics...
flutter doctor
echo.

echo ========================================
echo Dependencies installation completed!
echo ========================================
echo.
echo Now you can run Flutter projects:
echo - flutter run -d windows (for Windows)
echo - flutter run -d chrome (for Web)
echo.
pause
