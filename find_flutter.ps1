# Поиск Flutter на компьютере
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Поиск Flutter на компьютере" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Ищем Flutter..." -ForegroundColor Yellow

# Список возможных путей
$PossiblePaths = @(
    "C:\flutter\bin\flutter.exe",
    "C:\src\flutter\bin\flutter.exe", 
    "C:\tools\flutter\bin\flutter.exe",
    "D:\flutter\bin\flutter.exe",
    "$env:USERPROFILE\flutter\bin\flutter.exe",
    "$env:USERPROFILE\AppData\Local\flutter\bin\flutter.exe",
    "$env:PROGRAMFILES\flutter\bin\flutter.exe"
)

$FlutterFound = $false
$FlutterPath = ""

foreach ($Path in $PossiblePaths) {
    Write-Host "Проверяем: $Path" -ForegroundColor Gray
    if (Test-Path $Path) {
        Write-Host "✅ Flutter найден: $Path" -ForegroundColor Green
        $FlutterPath = Split-Path $Path -Parent
        $FlutterFound = $true
        break
    }
}

if (-not $FlutterFound) {
    Write-Host ""
    Write-Host "❌ Flutter не найден в стандартных местах" -ForegroundColor Red
    Write-Host ""
    Write-Host "Выполняем глубокий поиск..." -ForegroundColor Yellow
    
    # Глубокий поиск по диску C:
    try {
        $FlutterExes = Get-ChildItem -Path "C:\" -Name "flutter.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 5
        
        if ($FlutterExes) {
            Write-Host ""
            Write-Host "Найдены следующие файлы flutter.exe:" -ForegroundColor Cyan
            foreach ($exe in $FlutterExes) {
                $FullPath = "C:\$exe"
                Write-Host "  $FullPath" -ForegroundColor White
                
                if (-not $FlutterFound) {
                    $FlutterPath = Split-Path $FullPath -Parent
                    $FlutterFound = $true
                    Write-Host "  ↑ Будем использовать этот" -ForegroundColor Green
                }
            }
        }
    } catch {
        Write-Host "Ошибка при поиске: $_" -ForegroundColor Red
    }
}

Write-Host ""

if ($FlutterFound) {
    Write-Host "✅ Flutter найден в: $FlutterPath" -ForegroundColor Green
    Write-Host ""
    
    # Добавляем в PATH
    Write-Host "Добавляем в PATH..." -ForegroundColor Yellow
    $env:PATH = "$FlutterPath;$env:PATH"
    
    # Проверяем версию
    Write-Host "Проверяем версию..." -ForegroundColor Yellow
    try {
        flutter --version
        Write-Host ""
        Write-Host "✅ Flutter готов к использованию!" -ForegroundColor Green
        
        # Сохраняем путь в переменную среды пользователя
        [Environment]::SetEnvironmentVariable("PATH", "$FlutterPath;$([Environment]::GetEnvironmentVariable('PATH', 'User'))", "User")
        Write-Host "✅ Flutter добавлен в PATH пользователя" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Ошибка при проверке версии Flutter" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Flutter не найден на компьютере" -ForegroundColor Red
    Write-Host ""
    Write-Host "Варианты решения:" -ForegroundColor Yellow
    Write-Host "1. Скачайте Flutter с https://flutter.dev" -ForegroundColor White
    Write-Host "2. Распакуйте в C:\flutter" -ForegroundColor White
    Write-Host "3. Запустите этот скрипт снова" -ForegroundColor White
}

Write-Host ""
Read-Host "Нажмите Enter для выхода"
