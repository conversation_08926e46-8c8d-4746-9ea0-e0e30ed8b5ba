import 'package:flutter/material.dart';
import '../models/models.dart';
import 'services.dart';

/// Сервіс для аналітики та обробки даних для графіків
class AnalyticsService {
  final DatabaseService _databaseService = DatabaseService();

  AnalyticsService();

  /// Отримання даних про робочі години за період (по днях)
  Future<Map<DateTime, double>> getWorkHoursByDay(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Отримуємо робочі дні за період
    final workDays = await _databaseService.getWorkDaysByEmployeeAndPeriod(
      employeeId,
      startDate,
      endDate,
    );

    // Групуємо години по днях
    final Map<DateTime, double> hoursByDay = {};
    for (var workDay in workDays) {
      // Нормалізуємо дату (прибираємо час)
      final date = DateTime(
        workDay.date.year,
        workDay.date.month,
        workDay.date.day,
      );

      hoursByDay[date] = (hoursByDay[date] ?? 0) + workDay.hoursWorked;
    }

    return hoursByDay;
  }

  /// Отримання даних про робочі години за період (по тижнях)
  Future<Map<int, double>> getWorkHoursByWeek(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Отримуємо години по днях
    final hoursByDay = await getWorkHoursByDay(employeeId, startDate, endDate);

    // Групуємо години по тижнях
    final Map<int, double> hoursByWeek = {};
    for (var entry in hoursByDay.entries) {
      final date = entry.key;
      final hours = entry.value;

      // Отримуємо номер тижня в році
      final weekNumber = _getWeekNumber(date);

      hoursByWeek[weekNumber] = (hoursByWeek[weekNumber] ?? 0) + hours;
    }

    return hoursByWeek;
  }

  /// Отримання даних про робочі години за період (по місяцях)
  Future<Map<int, double>> getWorkHoursByMonth(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Отримуємо години по днях
    final hoursByDay = await getWorkHoursByDay(employeeId, startDate, endDate);

    // Групуємо години по місяцях
    final Map<int, double> hoursByMonth = {};
    for (var entry in hoursByDay.entries) {
      final date = entry.key;
      final hours = entry.value;

      // Використовуємо місяць як ключ (1-12)
      final monthKey = date.month;

      hoursByMonth[monthKey] = (hoursByMonth[monthKey] ?? 0) + hours;
    }

    return hoursByMonth;
  }

  /// Отримання даних про заробітну плату за період (по місяцях)
  Future<Map<int, double>> getWageByMonth(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
    CalculationService calculationService,
  ) async {
    // Отримуємо табелі за період
    final timesheets = await _databaseService.getTimesheetsByEmployee(
      employeeId,
    );

    // Фільтруємо табелі за періодом
    final filteredTimesheets =
        timesheets.where((timesheet) {
          return timesheet.startDate.isAfter(
                startDate.subtract(const Duration(days: 1)),
              ) &&
              timesheet.endDate.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();

    // Групуємо заробітну плату по місяцях
    final Map<int, double> wageByMonth = {};
    for (var timesheet in filteredTimesheets) {
      // Розраховуємо заробітну плату для табеля
      final reportData = await calculationService.calculateTimesheetWage(
        timesheet,
      );
      final totalWage = reportData['totalWage'] as double;

      // Використовуємо місяць як ключ (1-12)
      final monthKey = timesheet.startDate.month;

      wageByMonth[monthKey] = (wageByMonth[monthKey] ?? 0) + totalWage;
    }

    return wageByMonth;
  }

  /// Отримання даних про надбавки за період
  Future<Map<String, double>> getBonusesByType(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
    CalculationService calculationService,
  ) async {
    // Отримуємо табелі за період
    final timesheets = await _databaseService.getTimesheetsByEmployee(
      employeeId,
    );

    // Фільтруємо табелі за періодом
    final filteredTimesheets =
        timesheets.where((timesheet) {
          return timesheet.startDate.isAfter(
                startDate.subtract(const Duration(days: 1)),
              ) &&
              timesheet.endDate.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();

    // Ініціалізуємо мапу для надбавок
    final Map<String, double> bonusesByType = {
      'saturdayBonus': 0,
      'sundayBonus': 0,
      'holidayBonus': 0,
      'nightShiftBonus': 0,
      'hazardousBonus': 0,
      'overtimeBonus': 0,
      'experienceBonus': 0,
      'qualificationBonus': 0,
      'personalBonus': 0,
    };

    // Обчислюємо суми надбавок
    for (var timesheet in filteredTimesheets) {
      // Розраховуємо заробітну плату для табеля
      final reportData = await calculationService.calculateTimesheetWage(
        timesheet,
      );

      // Додаємо надбавки
      bonusesByType['saturdayBonus'] =
          (bonusesByType['saturdayBonus'] ?? 0) +
          (reportData['saturdayHoursWage'] * reportData['saturdayBonus']);

      bonusesByType['sundayBonus'] =
          (bonusesByType['sundayBonus'] ?? 0) +
          (reportData['sundayHoursWage'] * reportData['sundayBonus']);

      bonusesByType['holidayBonus'] =
          (bonusesByType['holidayBonus'] ?? 0) +
          (reportData['holidayHoursWage'] * reportData['holidayBonus']);

      bonusesByType['nightShiftBonus'] =
          (bonusesByType['nightShiftBonus'] ?? 0) +
          (reportData['nightShiftHoursWage'] * reportData['nightShiftBonus']);

      bonusesByType['hazardousBonus'] =
          (bonusesByType['hazardousBonus'] ?? 0) +
          (reportData['hazardousHoursWage'] ?? 0);

      bonusesByType['overtimeBonus'] =
          (bonusesByType['overtimeBonus'] ?? 0) +
          (reportData['overtimeHoursWage'] ?? 0);

      bonusesByType['experienceBonus'] =
          (bonusesByType['experienceBonus'] ?? 0) +
          (reportData['experienceBonusWage'] ?? 0);

      bonusesByType['qualificationBonus'] =
          (bonusesByType['qualificationBonus'] ?? 0) +
          (reportData['qualificationBonusWage'] ?? 0);

      bonusesByType['personalBonus'] =
          (bonusesByType['personalBonus'] ?? 0) +
          (reportData['personalBonusesWage'] ?? 0);
    }

    return bonusesByType;
  }

  /// Отримання даних про продуктивність (години по типах днів)
  Future<Map<String, double>> getHoursByDayType(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Отримуємо робочі дні за період
    final workDays = await _databaseService.getWorkDaysByEmployeeAndPeriod(
      employeeId,
      startDate,
      endDate,
    );

    // Ініціалізуємо мапу для годин по типах днів
    final Map<String, double> hoursByDayType = {
      'regular': 0,
      'saturday': 0,
      'sunday': 0,
      'holiday': 0,
    };

    // Групуємо години по типах днів
    for (var workDay in workDays) {
      switch (workDay.dayType) {
        case DayType.regular:
          hoursByDayType['regular'] =
              (hoursByDayType['regular'] ?? 0) + workDay.hoursWorked;
          break;
        case DayType.saturday:
          hoursByDayType['saturday'] =
              (hoursByDayType['saturday'] ?? 0) + workDay.hoursWorked;
          break;
        case DayType.sunday:
          hoursByDayType['sunday'] =
              (hoursByDayType['sunday'] ?? 0) + workDay.hoursWorked;
          break;
        case DayType.holiday:
          hoursByDayType['holiday'] =
              (hoursByDayType['holiday'] ?? 0) + workDay.hoursWorked;
          break;
      }
    }

    return hoursByDayType;
  }

  /// Отримання даних про перепрацювання (порівняння з нормою)
  Future<Map<String, double>> getOvertimeAnalysis(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Отримуємо робочі дні за період
    final workDays = await _databaseService.getWorkDaysByEmployeeAndPeriod(
      employeeId,
      startDate,
      endDate,
    );

    // Отримуємо працівника
    final employee = await _databaseService.getEmployee(employeeId);
    if (employee == null) {
      return {};
    }

    // Стандартна кількість робочих годин на день
    final standardHoursPerDay = 8.0;

    // Ініціалізуємо мапу для аналізу перепрацювань
    final Map<String, double> overtimeAnalysis = {
      'standardHours': 0,
      'actualHours': 0,
      'overtime': 0,
      'undertime': 0,
    };

    // Обчислюємо стандартні години та фактичні години
    for (var workDay in workDays) {
      // Пропускаємо вихідні та святкові дні при розрахунку стандартних годин
      if (workDay.dayType == DayType.regular) {
        overtimeAnalysis['standardHours'] =
            (overtimeAnalysis['standardHours'] ?? 0) + standardHoursPerDay;
      }

      // Додаємо фактичні години
      overtimeAnalysis['actualHours'] =
          (overtimeAnalysis['actualHours'] ?? 0) + workDay.hoursWorked;
    }

    // Обчислюємо перепрацювання або недопрацювання
    final standardHours = overtimeAnalysis['standardHours'] ?? 0;
    final actualHours = overtimeAnalysis['actualHours'] ?? 0;

    if (actualHours > standardHours) {
      overtimeAnalysis['overtime'] = actualHours - standardHours;
      overtimeAnalysis['undertime'] = 0;
    } else {
      overtimeAnalysis['overtime'] = 0;
      overtimeAnalysis['undertime'] = standardHours - actualHours;
    }

    return overtimeAnalysis;
  }

  /// Отримання номера тижня в році
  int _getWeekNumber(DateTime date) {
    // Перший день року
    final firstDayOfYear = DateTime(date.year, 1, 1);

    // Різниця в днях між датою та першим днем року
    final difference = date.difference(firstDayOfYear).inDays;

    // Номер тижня (додаємо 1, оскільки нумерація тижнів починається з 1)
    return (difference / 7).floor() + 1;
  }

  /// Отримання кольору для графіка на основі індексу
  Color getChartColor(int index) {
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.amber,
      Colors.indigo,
      Colors.pink,
    ];

    return colors[index % colors.length];
  }
}
