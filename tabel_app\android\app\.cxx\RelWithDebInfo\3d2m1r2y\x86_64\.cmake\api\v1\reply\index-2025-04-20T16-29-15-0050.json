{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-9e2e5b430b829dbeb64d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-8de87894d8f76ba79359.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-d79ba34b4f52d6b468cc.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-8de87894d8f76ba79359.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-d79ba34b4f52d6b468cc.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-9e2e5b430b829dbeb64d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}