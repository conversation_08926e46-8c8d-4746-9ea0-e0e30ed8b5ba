@echo off
chcp 65001 >nul
echo ========================================
echo Restoring Tabel App Project
echo ========================================
echo.

REM Check Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Flutter not found!
    echo Install Flutter first and run setup_flutter_en.bat
    pause
    exit /b 1
)

REM Check project exists
if not exist "tabel_app\pubspec.yaml" (
    echo Project tabel_app not found!
    echo Make sure you are in the correct folder.
    pause
    exit /b 1
)

echo Flutter found!
echo Project found!
echo.

REM Go to project folder
cd tabel_app

REM Clean cache
echo Cleaning cache...
flutter clean
echo.

REM Remove old build files
echo Removing old build files...
if exist "build" rmdir /s /q build
if exist ".dart_tool" rmdir /s /q .dart_tool
echo.

REM Get dependencies
echo Getting dependencies...
flutter pub get
echo.

REM Enable Windows support
echo Enabling Windows desktop support...
flutter config --enable-windows-desktop
echo.

REM Check project
echo Checking project...
flutter analyze
echo.

echo ========================================
echo Project restored!
echo ========================================
echo.
echo Now you can run:
echo flutter run -d windows
echo.
pause
