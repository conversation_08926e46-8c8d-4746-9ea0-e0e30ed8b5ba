import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';
import 'absence_detail_screen.dart';

/// Екран для перегляду списку відсутностей (відпустки, лікарняні, відгули)
class AbsenceListScreen extends StatefulWidget {
  const AbsenceListScreen({super.key});

  @override
  State<AbsenceListScreen> createState() => _AbsenceListScreenState();
}

class _AbsenceListScreenState extends State<AbsenceListScreen> {
  final AbsenceService _absenceService = AbsenceService();
  List<Absence> _absences = [];
  bool _isLoading = true;
  String? _errorMessage;
  AbsenceType? _selectedType;
  AbsenceStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Отримання поточного працівника
      final employee =
          Provider.of<AppState>(context, listen: false).currentEmployee;
      if (employee == null) {
        setState(() {
          _errorMessage = 'Працівника не знайдено';
        });
        return;
      }

      // Отримання відсутностей
      List<Absence> absences;
      if (_selectedType != null && _selectedStatus != null) {
        absences = await _absenceService.getAbsencesByEmployeeAndTypeAndStatus(
          employee.id,
          _selectedType!,
          _selectedStatus!,
        );
      } else {
        absences = await _absenceService.getAbsencesByEmployee(employee.id);
      }

      setState(() {
        _absences = absences;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка завантаження даних: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final languageCode = appState.language.code;

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).translate('absences')),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: AppLocalizations.of(context).translate('filter'),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _absences.isEmpty
              ? Center(
                child: Text(
                  AppLocalizations.of(context).translate('no_absences'),
                ),
              )
              : ListView.builder(
                itemCount: _absences.length,
                itemBuilder: (context, index) {
                  final absence = _absences[index];
                  return _buildAbsenceCard(absence, languageCode);
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createAbsence,
        tooltip: AppLocalizations.of(context).translate('add_absence'),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Побудова картки відсутності
  Widget _buildAbsenceCard(Absence absence, String languageCode) {
    final dateFormat = DateFormat('dd.MM.yyyy');
    final startDate = dateFormat.format(absence.startDate);
    final endDate = dateFormat.format(absence.endDate);
    final durationDays = absence.durationInDays;

    // Визначення кольору статусу
    Color statusColor;
    switch (absence.status) {
      case AbsenceStatus.planned:
        statusColor = Colors.orange;
        break;
      case AbsenceStatus.approved:
        statusColor = Colors.green;
        break;
      case AbsenceStatus.rejected:
        statusColor = Colors.red;
        break;
      case AbsenceStatus.cancelled:
        statusColor = Colors.grey;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _viewAbsenceDetails(absence),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      AppLocalizations.of(
                        context,
                      ).translate(absence.type.getLocalizationKey()),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(51), // 0.2 * 255 = 51
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: statusColor),
                    ),
                    child: Text(
                      AppLocalizations.of(
                        context,
                      ).translate(absence.status.getLocalizationKey()),
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.date_range, size: 16),
                  const SizedBox(width: 4),
                  Text('$startDate - $endDate'),
                  const Spacer(),
                  Text(
                    '$durationDays ${AppLocalizations.of(context).translate('days')}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              if (absence.comment != null && absence.comment!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.comment, size: 16),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        absence.comment!,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Відображення діалогу фільтрації
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        AbsenceType? tempType = _selectedType;
        AbsenceStatus? tempStatus = _selectedStatus;

        return AlertDialog(
          title: Text(AppLocalizations.of(context).translate('filter')),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Фільтр за типом відсутності
                  DropdownButtonFormField<AbsenceType?>(
                    value: tempType,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(
                        context,
                      ).translate('absence_type'),
                    ),
                    items: [
                      DropdownMenuItem<AbsenceType?>(
                        value: null,
                        child: Text(
                          AppLocalizations.of(context).translate('all'),
                        ),
                      ),
                      ...AbsenceType.values.map((type) {
                        return DropdownMenuItem<AbsenceType?>(
                          value: type,
                          child: Text(
                            AppLocalizations.of(
                              context,
                            ).translate(type.getLocalizationKey()),
                          ),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        tempType = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  // Фільтр за статусом відсутності
                  DropdownButtonFormField<AbsenceStatus?>(
                    value: tempStatus,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(
                        context,
                      ).translate('status'),
                    ),
                    items: [
                      DropdownMenuItem<AbsenceStatus?>(
                        value: null,
                        child: Text(
                          AppLocalizations.of(context).translate('all'),
                        ),
                      ),
                      ...AbsenceStatus.values.map((status) {
                        return DropdownMenuItem<AbsenceStatus?>(
                          value: status,
                          child: Text(
                            AppLocalizations.of(
                              context,
                            ).translate(status.getLocalizationKey()),
                          ),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        tempStatus = value;
                      });
                    },
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(AppLocalizations.of(context).translate('cancel')),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedType = tempType;
                  _selectedStatus = tempStatus;
                });
                Navigator.of(context).pop();
                _loadData();
              },
              child: Text(AppLocalizations.of(context).translate('apply')),
            ),
          ],
        );
      },
    );
  }

  /// Створення нової відсутності
  void _createAbsence() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(builder: (context) => const AbsenceDetailScreen()),
        )
        .then((_) => _loadData());
  }

  /// Перегляд деталей відсутності
  void _viewAbsenceDetails(Absence absence) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => AbsenceDetailScreen(absence: absence),
          ),
        )
        .then((_) => _loadData());
  }
}
