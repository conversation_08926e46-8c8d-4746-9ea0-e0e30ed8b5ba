FROM openjdk:11-jdk

# Install Flutter dependencies
RUN apt-get update && apt-get install -y curl git unzip xz-utils zip libglu1-mesa

# Clone Flutter
RUN git clone https://github.com/flutter/flutter.git /flutter
ENV PATH="/flutter/bin:${PATH}"

# Run flutter doctor
RUN flutter doctor

# Set up working directory
WORKDIR /app

# Copy the app
COPY . .

# Get dependencies
RUN flutter pub get

# Build APK
RUN flutter build apk --release

# The APK will be in /app/build/app/outputs/flutter-apk/app-release.apk
