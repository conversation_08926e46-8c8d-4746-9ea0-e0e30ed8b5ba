import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

/// Сервіс для роботи з сповіщеннями на iOS
class IOSNotificationService {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;

  IOSNotificationService(this._flutterLocalNotificationsPlugin);

  /// Ініціалізація сервісу сповіщень для iOS
  Future<void> initialize() async {
    if (!Platform.isIOS) return;

    // Ініціалізація часових зон
    tz_data.initializeTimeZones();

    // Налаштування для iOS
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestSoundPermission: true,
          requestBadgePermission: true,
          requestAlertPermission: true,
          defaultPresentAlert: true,
          defaultPresentBadge: true,
          defaultPresentSound: true,
        );

    // Ініціалізація плагіна
    await _flutterLocalNotificationsPlugin.initialize(
      const InitializationSettings(
        iOS: initializationSettingsIOS,
        macOS: initializationSettingsIOS,
      ),
    );

    debugPrint('iOS Notification Service initialized');
  }

  /// Запит дозволів на сповіщення
  Future<bool> requestPermissions() async {
    if (!Platform.isIOS) return true;

    final bool? result = await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin
        >()
        ?.requestPermissions(alert: true, badge: true, sound: true);

    return result ?? false;
  }

  /// Планування сповіщення
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
  }) async {
    if (!Platform.isIOS) return;

    // Конвертація DateTime в TZDateTime
    final tz.TZDateTime tzScheduledDate = tz.TZDateTime.from(
      scheduledDate,
      tz.local,
    );

    const DarwinNotificationDetails iosNotificationDetails =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'default',
          badgeNumber: 1,
          threadIdentifier: 'timesheet_reminders',
        );

    await _flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      tzScheduledDate,
      NotificationDetails(iOS: iosNotificationDetails),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  /// Скасування сповіщення
  Future<void> cancelNotification(int id) async {
    if (!Platform.isIOS) return;
    await _flutterLocalNotificationsPlugin.cancel(id);
  }

  /// Скасування всіх сповіщень
  Future<void> cancelAllNotifications() async {
    if (!Platform.isIOS) return;
    await _flutterLocalNotificationsPlugin.cancelAll();
  }
}
