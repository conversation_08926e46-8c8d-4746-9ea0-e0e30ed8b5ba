@echo off
echo ========================================
echo Настройка Flutter PATH
echo ========================================
echo.

REM Проверяем, где установлен Flutter
set FLUTTER_PATH=C:\flutter\bin

if not exist "%FLUTTER_PATH%\flutter.exe" (
    echo ❌ Flutter не найден в %FLUTTER_PATH%
    echo.
    echo Пожалуйста, укажите правильный путь к Flutter:
    set /p FLUTTER_PATH="Введите путь (например, C:\flutter\bin): "
)

if not exist "%FLUTTER_PATH%\flutter.exe" (
    echo ❌ Flutter все еще не найден!
    echo Убедитесь, что Flutter правильно распакован.
    pause
    exit /b 1
)

echo ✅ Flutter найден: %FLUTTER_PATH%
echo.

REM Добавляем в PATH для текущей сессии
set PATH=%FLUTTER_PATH%;%PATH%

REM Добавляем в системный PATH (требует прав администратора)
echo Добавляем Flutter в системный PATH...
setx PATH "%FLUTTER_PATH%;%PATH%" /M 2>nul

if %errorlevel% neq 0 (
    echo ⚠️ Не удалось добавить в системный PATH (нужны права администратора)
    echo Добавляем в пользовательский PATH...
    setx PATH "%FLUTTER_PATH%;%PATH%"
)

echo.
echo ✅ Flutter добавлен в PATH!
echo.
echo Перезапустите командную строку и выполните:
echo flutter doctor
echo.
pause
