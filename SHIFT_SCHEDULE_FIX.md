# Виправлення логіки відображення змін у календарі

## 🔍 Проблема
Раніше в календарі неправильно відображалися зміни протягом тижня. Замість того, щоб показувати 5 однакових змін з понеділка по п'ятницю, система використовувала фіксовані блоки по 5 днів, що призводило до неправильного розподілу.

## ✅ Рішення

### 1. Виправлено метод `getShiftIdForDate` у файлі `shift_schedule.dart`

**Було:**
- Розрахунок базувався на кількості робочих днів з фіксованими блоками по 5 днів
- Не враховувалася структура тижня (понеділок-п'ятниця)

**Стало:**
- Розрахунок базується на кількості робочих тижнів
- Кожен тиждень (понеділок-п'ятниця) має одну і ту ж зміну
- Зміни змінюються щотижня згідно з порядком у `shiftOrder`

### 2. Покращено відображення маркерів змін у файлі `timesheet_tab.dart`

**Було:**
- Показувалися тільки літери "Н" (нічна) або "Д" (денна)

**Стало:**
- Показується скорочена назва зміни (цифра або перші 1-2 символи)
- Краще розпізнавання різних змін у календарі

## 📋 Логіка роботи

### Розрахунок зміни для дати:

1. **Знаходження понеділка тижня** для вказаної дати
2. **Розрахунок кількості робочих тижнів** від початку графіку
3. **Визначення індексу зміни** за формулою:
   ```dart
   adjustedIndex = (currentShiftIndex + workWeeksCount - 1) % shiftOrder.length
   ```

### Приклад роботи:

Якщо є графік з 3 змінами: `['Зміна 1', 'Зміна 2', 'Зміна 3']`

**Тиждень 1 (Пн-Пт):** Зміна 1
**Тиждень 2 (Пн-Пт):** Зміна 2  
**Тиждень 3 (Пн-Пт):** Зміна 3
**Тиждень 4 (Пн-Пт):** Зміна 1 (цикл повторюється)

## 🎯 Результат

Тепер у календарі правильно відображаються зміни:
- **5 однакових змін** з понеділка по п'ятницю
- **Зміни змінюються щотижня** згідно з графіком
- **Краще візуальне розрізнення** різних змін через покращені маркери

## 📁 Змінені файли

1. `tabel_app/lib/models/shift_schedule.dart` - виправлено логіку розрахунку
2. `tabel_app/lib/screens/timesheet_tab.dart` - покращено відображення маркерів

## 🧪 Тестування

Для тестування нової логіки можна використати файл `test_shift_schedule.dart`, який демонструє роботу алгоритму на прикладі 4 тижнів.
