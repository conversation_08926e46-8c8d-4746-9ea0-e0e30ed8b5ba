import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'translations/uk.dart';
import 'translations/en.dart';

/// Підтримувані мови
enum AppLanguage {
  /// Українська
  ukrainian,

  /// Англійська
  english,

  /// Чеська
  czech,

  /// Словацька
  slovak,

  /// Польська
  polish,
}

/// Розширення для AppLanguage
extension AppLanguageExtension on AppLanguage {
  /// Отримання коду мови
  String get code {
    switch (this) {
      case AppLanguage.ukrainian:
        return 'uk';
      case AppLanguage.english:
        return 'en';
      case AppLanguage.czech:
        return 'cs';
      case AppLanguage.slovak:
        return 'sk';
      case AppLanguage.polish:
        return 'pl';
    }
  }

  /// Отримання назви мови
  String get name {
    switch (this) {
      case AppLanguage.ukrainian:
        return 'Українська';
      case AppLanguage.english:
        return 'English';
      case AppLanguage.czech:
        return 'Čeština';
      case AppLanguage.slovak:
        return 'Slovenčina';
      case AppLanguage.polish:
        return 'Polski';
    }
  }

  /// Отримання коду країни
  String get countryCode {
    switch (this) {
      case AppLanguage.ukrainian:
        return 'UA';
      case AppLanguage.english:
        return 'US';
      case AppLanguage.czech:
        return 'CZ';
      case AppLanguage.slovak:
        return 'SK';
      case AppLanguage.polish:
        return 'PL';
    }
  }

  /// Отримання локалі
  Locale get locale => Locale(code, countryCode);
}

/// Клас для локалізації
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  /// Отримання екземпляра AppLocalizations
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  /// Делегат для локалізації
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// Підтримувані мови
  static final List<AppLanguage> supportedLanguages = AppLanguage.values;

  /// Підтримувані локалі
  static final List<Locale> supportedLocales =
      supportedLanguages.map((e) => e.locale).toList();

  /// Переклади
  static final Map<String, Map<String, String>> _localizedValues = {
    'uk': {
      'app_name': 'Табель',
      'login': 'Вхід',
      'register': 'Реєстрація',
      'email': 'Email',
      'password': 'Пароль',
      'confirm_password': 'Підтвердження пароля',
      'first_name': 'Ім\'я',
      'last_name': 'Прізвище',
      'position': 'Посада',
      'hourly_rate': 'Ставка за годину',
      'admin': 'Адміністратор',
      'employee': 'Працівник',
      'role': 'Роль',
      'close': 'Закрити',
      'edit': 'Редагувати',
      'edit_profile': 'Редагування профілю',
      'required_field': "Обов'язкове поле",
      'invalid_hourly_rate': 'Некоректна ставка за годину',
      'invalid_email': 'Некоректний email',
      'country': 'Країна',
      'currency': 'Валюта',
      'change_day_type': 'Змінити тип дня',
      'change': 'Змінити',
      'day_type': 'Тип дня',
      'date': 'Дата',
      'report_saturday_bonus': 'Надбавка за суботу',
      'report_sunday_bonus': 'Надбавка за неділю',
      'report_holiday_bonus': 'Надбавка за святкові дні',
      'report_night_shift_bonus': 'Надбавка за нічні зміни',
      'report_hazardous_bonus': 'Надбавка за шкідливі умови',
      'report_overtime_bonus': 'Надбавка за понаднормову роботу',
      'report_experience_bonus': 'Надбавка за стаж роботи',
      'report_qualification_bonus': 'Надбавка за кваліфікацію',
      'report_personal_bonuses': 'Персональні надбавки',
      'years': 'років',
      'level': 'Рівень',
      'bonuses': 'Надбавки',
      'hire_date': 'Дата початку роботи',
      'not_specified': 'Не вказано',
      'experience_years': 'Стаж роботи (років)',
      'qualification_level': 'Рівень кваліфікації (1-5)',
      'invalid_experience': 'Некоректний стаж',
      'invalid_qualification': 'Рівень повинен бути від 1 до 5',
      'export_pdf': 'Експорт у PDF',
      'print': 'Друк',
      'preview': 'Перегляд',
      'timesheet_report': 'Звіт по табелю',
      'report_employee': 'Працівник',
      'report_position': 'Посада',
      'report_period': 'Період',
      'category': 'Категорія',
      'value': 'Значення',
      'employee_signature': 'Підпис працівника',
      'manager_signature': 'Підпис керівника',
      'report_date': 'Дата звіту',
      'export_success': 'Звіт успішно експортовано',
      'timesheet': 'Табель',
      'timesheets': 'Табелі',
      'work_day': 'Робочий день',
      'work_days': 'Робочі дні',
      'hours': 'Години',
      'hours_short': 'г',
      'report_date_label': 'Дата',
      'report_day_type': 'Тип дня',
      'shift_type': 'Тип зміни',
      'regular': 'Звичайний',
      'saturday': 'Субота',
      'sunday': 'Неділя',
      'holiday': 'Святковий',
      'day_shift': 'Денна зміна',
      'night_shift': 'Нічна зміна',
      'bonus_settings': 'Налаштування надбавок',
      'night_shift_bonus': 'Надбавка за нічну зміну',
      'saturday_bonus': 'Надбавка за роботу в суботу',
      'sunday_bonus': 'Надбавка за роботу в неділю',
      'holiday_bonus': 'Надбавка за роботу в святковий день',
      'save': 'Зберегти',
      'cancel': 'Скасувати',
      'delete': 'Видалити',
      'add': 'Додати',
      'submit': 'Відправити',
      'approve': 'Затвердити',
      'reject': 'Відхилити',
      'draft': 'Чернетка',
      'submitted': 'Відправлено',
      'approved': 'Затверджено',
      'rejected': 'Відхилено',
      'total_hours': 'Загальна кількість годин',
      'total_wage': 'Загальна сума',
      'comment': 'Коментар',
      'settings': 'Налаштування',
      'language': 'Мова',
      'theme': 'Тема',
      'light': 'Світла',
      'dark': 'Темна',
      'system': 'Системна',
      'sync': 'Синхронізація',
      'sync_success': 'Синхронізація успішна',
      'sync_error': 'Помилка синхронізації',
      'no_internet': 'Немає інтернет-з\'єднання',
      'logout': 'Вихід',
      'profile': 'Профіль',
      'change_password': 'Змінити пароль',
      'reset_password': 'Скинути пароль',
      'reset_password_info':
          'Введіть вашу електронну адресу, і ми надішлемо вам інструкції для скидання пароля.',
      'back': 'Назад',
      'current_password': 'Поточний пароль',
      'new_password': 'Новий пароль',
      'password_reset_sent':
          'Інструкції для скидання пароля відправлені на вашу електронну пошту',
      'verify_reset_code': 'Підтвердження коду',
      'verify_reset_code_info': 'Введіть код підтвердження та новий пароль',
      'reset_code': 'Код підтвердження',
      'your_reset_code': 'Ваш код підтвердження',
      'submit_new_password': 'Змінити пароль',
      'password_reset_success': 'Пароль успішно змінено',
      'back_to_login': 'Повернутися до входу',
      'code_sent_to_email':
          'Код підтвердження відправлено на вашу електронну пошту',
      'error': 'Помилка',
      'success': 'Успішно',

      'password_mismatch': 'Паролі не співпадають',
      'password_too_short': 'Пароль повинен містити не менше 6 символів',

      'invalid_hours': 'Невірна кількість годин',
      'export': 'Експорт',
      'import': 'Імпорт',
      'pdf': 'PDF',
      'excel': 'Excel',
      'reports': 'Звіти',
      'report': 'Звіт',
      'period': 'Період',
      'from': 'З',
      'to': 'По',
      'employees': 'Працівники',
      'holidays': 'Святкові дні',
      'add_holiday': 'Додати святковий день',
      'holiday_name': 'Назва свята',
      'recurring': 'Щорічне',
      'ukraine': 'Україна',
      'usa': 'США',
      'czech_republic': 'Чехія',
      'slovakia': 'Словаччина',
      'poland': 'Польща',

      // Відсутності
      'absences': 'Відсутності',
      'absence': 'Відсутність',
      'absence_type': 'Тип відсутності',
      'absence_details': 'Деталі відсутності',
      'new_absence': 'Нова відсутність',
      'start_date': 'Дата початку',
      'end_date': 'Дата закінчення',
      'status': 'Статус',
      'document': 'Документ',
      'document_hint': 'Номер лікарняного, наказу тощо',
      'days': 'днів',
      'no_absences': 'Немає відсутностей',
      'add_absence': 'Додати відсутність',
      'filter': 'Фільтр',
      'all': 'Всі',
      'vacation': 'Щорічна відпустка',
      'sick_leave': 'Лікарняний',
      'day_off': 'Відгул',
      'unpaid_leave': 'Відпустка за власний рахунок',
      'study_leave': 'Навчальна відпустка',
      'maternity_leave': 'Декретна відпустка',
      'childcare_leave': 'Відпустка по догляду за дитиною',
      'other': 'Інше',
      'planned_status': 'Очікує затвердження',
      'approved_status': 'Затверджено',
      'rejected_status': 'Відхилено',
      'cancelled': 'Скасовано',
      'approval_comment': 'Коментар до затвердження/відхилення',
      'processed_by': 'Оброблено користувачем',
      'processed_at': 'Дата обробки',
      'cancel_request': 'Скасувати запит',
      'absence_created': 'Відсутність створено',
      'absence_updated': 'Відсутність оновлено',
      'absence_approved': 'Відсутність затверджено',
      'absence_rejected': 'Відсутність відхилено',
      'absence_cancelled': 'Відсутність скасовано',
      'end_date_before_start_date':
          'Дата закінчення не може бути раніше дати початку',
      'confirmation': 'Підтвердження',
      'delete_work_day_confirm': 'Ви дійсно хочете видалити робочий день?',
      'work_day_deleted': 'Робочий день видалено',
      'error_deleting_data': 'Помилка видалення даних',
      'no_data_to_delete': 'Немає даних для видалення',
      'create_timesheet_todo':
          'Функціонал створення табеля буде реалізовано пізніше',
      'apply': 'Застосувати',
      'shifts_legend': 'Легенда змін',

      // Персональні надбавки
      'employee_bonuses': 'Персональні надбавки',
      'add_bonus': 'Додати надбавку',
      'edit_bonus': 'Редагувати надбавку',
      'bonus_name': 'Назва надбавки',
      'bonus_value': 'Значення надбавки',
      'bonus_description': 'Опис надбавки',
      'bonus_type': 'Тип надбавки',
      'calculation_type': 'Тип нарахування',
      'bonus_start_date': 'Дата початку',
      'bonus_end_date': 'Дата закінчення',
      'indefinite': 'Безстроково',
      'no_bonuses': 'Немає надбавок',
      'bonus_period': 'Період',

      // Типи надбавок
      'bonus_type_hazardous': 'Шкідливі умови',
      'bonus_type_experience': 'Стаж роботи',
      'bonus_type_qualification': 'Кваліфікація',
      'bonus_type_overtime': 'Понаднормова робота',
      'bonus_type_personal': 'Персональна надбавка',
      'bonus_type_achievement': 'Досягнення',

      // Типи нарахування
      'calculation_type_percentage': 'Відсоток від ставки',
      'calculation_type_hourly': 'Фіксована сума на годину',
      'calculation_type_daily': 'Фіксована сума на день',
      'calculation_type_monthly': 'Фіксована сума на місяць',

      // Форматування значень
      'value_percentage': '%',
      'value_hourly': '/год',
      'value_daily': '/день',
      'value_monthly': '/міс',

      // Нагадування
      'reminders': 'Нагадування',
      'reminder': 'Нагадування',
      'reminder_details': 'Деталі нагадування',
      'no_reminders': 'Немає нагадувань',
      'snooze': 'Відкласти',
      'dismiss': 'Закрити',
      'missing_timesheet_reminder':
          'Нагадування про відсутність даних в табелі',
      'missing_timesheet_title': 'Відсутні дані в табелі',
      'missing_timesheet_message':
          'Ви не внесли дані в табель за {date}. Будь ласка, внесіть дані якнайшвидше.',
      'reminder_status_active': 'Активне',
      'reminder_status_shown': 'Показане',
      'reminder_status_snoozed': 'Відкладене',
      'reminder_status_dismissed': 'Закрите',
      'snooze_for': 'Відкласти на',
      'snooze_for_1_hour': '1 годину',
      'snooze_for_3_hours': '3 години',
      'snooze_for_tomorrow': 'Завтра',
      'scheduled_for': 'Заплановано на',
      'refresh': 'Оновити',
    },
    'en': {
      'app_name': 'Timesheet',
      'login': 'Login',
      'register': 'Register',
      'email': 'Email',
      'password': 'Password',
      'confirm_password': 'Confirm Password',
      'first_name': 'First Name',
      'last_name': 'Last Name',
      'position': 'Position',
      'hourly_rate': 'Hourly Rate',
      'admin': 'Administrator',
      'employee': 'Employee',
      'role': 'Role',
      'close': 'Close',
      'edit': 'Edit',
      'edit_profile': 'Edit Profile',
      'required_field': 'Required field',
      'invalid_hourly_rate': 'Invalid hourly rate',
      'invalid_email': 'Invalid email',
      'country': 'Country',
      'currency': 'Currency',
      'change_day_type': 'Change Day Type',
      'change': 'Change',
      'day_type': 'Day Type',
      'date': 'Date',
      'report_saturday_bonus': 'Saturday Bonus',
      'report_sunday_bonus': 'Sunday Bonus',
      'report_holiday_bonus': 'Holiday Bonus',
      'report_night_shift_bonus': 'Night Shift Bonus',
      'report_hazardous_bonus': 'Hazardous Conditions Bonus',
      'report_overtime_bonus': 'Overtime Bonus',
      'report_experience_bonus': 'Experience Bonus',
      'report_qualification_bonus': 'Qualification Bonus',
      'report_personal_bonuses': 'Personal Bonuses',
      'years': 'years',
      'level': 'Level',
      'bonuses': 'Bonuses',
      'hire_date': 'Hire Date',
      'not_specified': 'Not Specified',
      'experience_years': 'Experience (years)',
      'qualification_level': 'Qualification Level (1-5)',
      'invalid_experience': 'Invalid experience',
      'invalid_qualification': 'Level must be between 1 and 5',
      'export_pdf': 'Export to PDF',
      'print': 'Print',
      'preview': 'Preview',
      'timesheet_report': 'Timesheet Report',
      'report_employee': 'Employee',
      'report_position': 'Position',
      'report_period': 'Period',
      'category': 'Category',
      'value': 'Value',
      'employee_signature': 'Employee Signature',
      'manager_signature': 'Manager Signature',
      'report_date': 'Report Date',
      'export_success': 'Report successfully exported',
      'timesheet': 'Timesheet',
      'timesheets': 'Timesheets',
      'work_day': 'Work Day',
      'work_days': 'Work Days',
      'hours': 'Hours',
      'hours_short': 'h',
      'report_date_label': 'Date',
      'report_day_type': 'Day Type',
      'shift_type': 'Shift Type',
      'regular': 'Regular',
      'saturday': 'Saturday',
      'sunday': 'Sunday',
      'holiday': 'Holiday',
      'day_shift': 'Day Shift',
      'night_shift': 'Night Shift',
      'bonus_settings': 'Bonus Settings',
      'night_shift_bonus': 'Night Shift Bonus',
      'saturday_bonus': 'Saturday Bonus',
      'sunday_bonus': 'Sunday Bonus',
      'holiday_bonus': 'Holiday Bonus',
      'save': 'Save',
      'cancel': 'Cancel',
      'delete': 'Delete',
      'add': 'Add',
      'submit': 'Submit',
      'approve': 'Approve',
      'reject': 'Reject',
      'draft': 'Draft',
      'submitted': 'Submitted',
      'approved': 'Approved',
      'rejected': 'Rejected',
      'total_hours': 'Total Hours',
      'total_wage': 'Total Wage',
      'comment': 'Comment',
      'settings': 'Settings',
      'language': 'Language',
      'theme': 'Theme',
      'light': 'Light',
      'dark': 'Dark',
      'system': 'System',
      'sync': 'Sync',
      'sync_success': 'Sync Successful',
      'sync_error': 'Sync Error',
      'no_internet': 'No Internet Connection',
      'logout': 'Logout',
      'profile': 'Profile',
      'change_password': 'Change Password',
      'reset_password': 'Reset Password',
      'reset_password_info':
          'Enter your email address and we will send you instructions to reset your password.',
      'back': 'Back',
      'current_password': 'Current Password',
      'new_password': 'New Password',
      'password_reset_sent':
          'Password reset instructions have been sent to your email',
      'verify_reset_code': 'Verify Code',
      'verify_reset_code_info':
          'Enter the verification code and your new password',
      'reset_code': 'Verification Code',
      'your_reset_code': 'Your Verification Code',
      'submit_new_password': 'Change Password',
      'password_reset_success': 'Password successfully changed',
      'back_to_login': 'Back to Login',
      'code_sent_to_email': 'Verification code has been sent to your email',
      'error': 'Error',
      'success': 'Success',
      'password_mismatch': 'Passwords do not match',
      'password_too_short': 'Password must be at least 6 characters',
      'invalid_hours': 'Invalid hours',
      'export': 'Export',
      'import': 'Import',
      'pdf': 'PDF',
      'excel': 'Excel',
      'reports': 'Reports',
      'report': 'Report',
      'period': 'Period',
      'from': 'From',
      'to': 'To',
      'employees': 'Employees',
      'holidays': 'Holidays',
      'add_holiday': 'Add Holiday',
      'holiday_name': 'Holiday Name',
      'recurring': 'Recurring',
      'ukraine': 'Ukraine',
      'usa': 'USA',
      'czech_republic': 'Czech Republic',
      'slovakia': 'Slovakia',
      'poland': 'Poland',

      // Absences
      'absences': 'Absences',
      'absence': 'Absence',
      'absence_type': 'Absence Type',
      'absence_details': 'Absence Details',
      'new_absence': 'New Absence',
      'start_date': 'Start Date',
      'end_date': 'End Date',
      'status': 'Status',
      'document': 'Document',
      'document_hint': 'Sick leave number, order number, etc.',
      'days': 'days',
      'no_absences': 'No absences',
      'add_absence': 'Add Absence',
      'filter': 'Filter',
      'all': 'All',
      'vacation': 'Annual Leave',
      'sick_leave': 'Sick Leave',
      'day_off': 'Day Off',
      'unpaid_leave': 'Unpaid Leave',
      'study_leave': 'Study Leave',
      'maternity_leave': 'Maternity Leave',
      'childcare_leave': 'Childcare Leave',
      'other': 'Other',
      'planned_status': 'Pending Approval',
      'approved_status': 'Approved',
      'rejected_status': 'Rejected',
      'cancelled': 'Cancelled',
      'approval_comment': 'Approval/Rejection Comment',
      'processed_by': 'Processed by',
      'processed_at': 'Processed at',
      'cancel_request': 'Cancel Request',
      'absence_created': 'Absence created',
      'absence_updated': 'Absence updated',
      'absence_approved': 'Absence approved',
      'absence_rejected': 'Absence rejected',
      'absence_cancelled': 'Absence cancelled',
      'end_date_before_start_date': 'End date cannot be before start date',
      'confirmation': 'Confirmation',
      'delete_work_day_confirm': 'Do you really want to delete this work day?',
      'work_day_deleted': 'Work day deleted',
      'error_deleting_data': 'Error deleting data',
      'no_data_to_delete': 'No data to delete',
      'create_timesheet_todo':
          'Timesheet creation functionality will be implemented later',
      'shifts_legend': 'Shifts Legend',

      // Employee Bonuses
      'employee_bonuses': 'Employee Bonuses',
      'add_bonus': 'Add Bonus',
      'edit_bonus': 'Edit Bonus',
      'bonus_name': 'Bonus Name',
      'bonus_value': 'Bonus Value',

      // Reminders
      'reminders': 'Reminders',
      'reminder': 'Reminder',
      'reminder_details': 'Reminder Details',
      'no_reminders': 'No reminders',
      'snooze': 'Snooze',
      'dismiss': 'Dismiss',
      'missing_timesheet_reminder': 'Missing Timesheet Data Reminder',
      'missing_timesheet_title': 'Missing Timesheet Data',
      'missing_timesheet_message':
          'You have not entered timesheet data for {date}. Please enter the data as soon as possible.',
      'reminder_status_active': 'Active',
      'reminder_status_shown': 'Shown',
      'reminder_status_snoozed': 'Snoozed',
      'reminder_status_dismissed': 'Dismissed',
      'snooze_for': 'Snooze for',
      'snooze_for_1_hour': '1 hour',
      'snooze_for_3_hours': '3 hours',
      'snooze_for_tomorrow': 'Tomorrow',
      'scheduled_for': 'Scheduled for',
      'refresh': 'Refresh',
      'bonus_description': 'Bonus Description',
      'bonus_type': 'Bonus Type',
      'calculation_type': 'Calculation Type',
      'bonus_start_date': 'Start Date',
      'bonus_end_date': 'End Date',
      'indefinite': 'Indefinite',
      'no_bonuses': 'No Bonuses',
      'bonus_period': 'Period',

      // Bonus Types
      'bonus_type_hazardous': 'Hazardous Conditions',
      'bonus_type_experience': 'Experience',
      'bonus_type_qualification': 'Qualification',
      'bonus_type_overtime': 'Overtime',
      'bonus_type_personal': 'Personal Bonus',
      'bonus_type_achievement': 'Achievement',

      // Calculation Types
      'calculation_type_percentage': 'Percentage of Rate',
      'calculation_type_hourly': 'Fixed Amount per Hour',
      'calculation_type_daily': 'Fixed Amount per Day',
      'calculation_type_monthly': 'Fixed Amount per Month',

      // Value Formatting
      'value_percentage': '%',
      'value_hourly': '/hr',
      'value_daily': '/day',
      'value_monthly': '/month',
    },
    'cs': {
      'app_name': 'Docházka',
      'login': 'Přihlášení',
      'register': 'Registrace',
      'email': 'Email',
      'password': 'Heslo',
      'confirm_password': 'Potvrzení hesla',
      'first_name': 'Jméno',
      'last_name': 'Příjmení',
      'position': 'Pozice',
      'hourly_rate': 'Hodinová sazba',
      'admin': 'Administrátor',
      'employee': 'Zaměstnanec',
      'role': 'Role',
      'close': 'Zavřít',
      'edit': 'Upravit',
      'edit_profile': 'Upravit profil',
      'required_field': 'Povinné pole',
      'invalid_hourly_rate': 'Neplatná hodinová sazba',
      'invalid_email': 'Neplatný email',
      'country': 'Země',
      'currency': 'Měna',
      'change_day_type': 'Změnit typ dne',
      'change': 'Změnit',
      'day_type': 'Typ dne',
      'date': 'Datum',
      'report_saturday_bonus': 'Příplatek za sobotu',
      'report_sunday_bonus': 'Příplatek za neděli',
      'report_holiday_bonus': 'Příplatek za svátek',
      'report_night_shift_bonus': 'Příplatek za noční směnu',
      'report_hazardous_bonus': 'Příplatek za škodlivé podmínky',
      'report_overtime_bonus': 'Příplatek za přesčas',
      'report_experience_bonus': 'Příplatek za praxi',
      'report_qualification_bonus': 'Příplatek za kvalifikaci',
      'report_personal_bonuses': 'Osobní příplatky',
      'years': 'let',
      'level': 'Úroveň',
      'bonuses': 'Příplatky',
      'hire_date': 'Datum nástupu',
      'not_specified': 'Není specifikováno',
      'experience_years': 'Praxe (roky)',
      'qualification_level': 'Úroveň kvalifikace (1-5)',
      'invalid_experience': 'Neplatná praxe',
      'invalid_qualification': 'Úroveň musí být mezi 1 a 5',
      'export_pdf': 'Export do PDF',
      'print': 'Tisk',
      'preview': 'Náhled',
      'timesheet_report': 'Výkaz práce',
      'report_employee': 'Zaměstnanec',
      'report_position': 'Pozice',
      'report_period': 'Období',
      'category': 'Kategorie',
      'value': 'Hodnota',
      'employee_signature': 'Podpis zaměstnance',
      'manager_signature': 'Podpis vedoucího',
      'report_date': 'Datum výkazu',
      'export_success': 'Zpráva úspěšně exportována',
      'timesheet': 'Docházka',
      'timesheets': 'Docházky',
      'work_day': 'Pracovní den',
      'work_days': 'Pracovní dny',
      'hours': 'Hodiny',
      'hours_short': 'h',
      'report_date_label': 'Datum',
      'report_day_type': 'Typ dne',
      'shift_type': 'Typ směny',
      'regular': 'Běžný',
      'saturday': 'Sobota',
      'sunday': 'Neděle',
      'holiday': 'Svátek',
      'day_shift': 'Denní směna',
      'night_shift': 'Noční směna',
      'bonus_settings': 'Nastavení příplatků',
      'night_shift_bonus': 'Příplatek za noční směnu',
      'saturday_bonus': 'Příplatek za práci v sobotu',
      'sunday_bonus': 'Příplatek za práci v neděli',
      'holiday_bonus': 'Příplatek za práci ve svátek',
      'save': 'Uložit',
      'cancel': 'Zrušit',
      'delete': 'Smazat',
      'add': 'Přidat',
      'submit': 'Odeslat',
      'approve': 'Schválit',
      'reject': 'Odmítnout',
      'draft': 'Koncept',
      'submitted': 'Odesláno',
      'approved': 'Schváleno',
      'rejected': 'Odmítnuto',
      'total_hours': 'Celkem hodin',
      'total_wage': 'Celková mzda',
      'comment': 'Komentář',
      'settings': 'Nastavení',
      'language': 'Jazyk',
      'theme': 'Téma',
      'light': 'Světlé',
      'dark': 'Tmavé',
      'system': 'Systémové',
      'sync': 'Synchronizace',
      'sync_success': 'Synchronizace úspěšná',
      'sync_error': 'Chyba synchronizace',
      'no_internet': 'Žádné připojení k internetu',
      'logout': 'Odhlásit se',
      'profile': 'Profil',
      'change_password': 'Změnit heslo',
      'reset_password': 'Obnovit heslo',
      'current_password': 'Aktuální heslo',
      'new_password': 'Nové heslo',
      'password_reset_sent':
          'Pokyny k obnovení hesla byly odeslány na váš e-mail',
      'verify_reset_code': 'Ověřit kód',
      'verify_reset_code_info': 'Zadejte ověřovací kód a nové heslo',
      'reset_code': 'Ověřovací kód',
      'your_reset_code': 'Váš ověřovací kód',
      'submit_new_password': 'Změnit heslo',
      'password_reset_success': 'Heslo bylo úspěšně změněno',
      'back_to_login': 'Zpět na přihlášení',
      'code_sent_to_email': 'Ověřovací kód byl odeslán na váš e-mail',
      'error': 'Chyba',
      'success': 'Úspěch',
      'password_mismatch': 'Hesla se neshodují',
      'password_too_short': 'Heslo musí mít alespoň 6 znaků',
      'invalid_hours': 'Neplatný počet hodin',
      'export': 'Export',
      'import': 'Import',
      'pdf': 'PDF',
      'excel': 'Excel',
      'reports': 'Reporty',
      'report': 'Report',
      'period': 'Období',
      'from': 'Od',
      'to': 'Do',
      'employees': 'Zaměstnanci',
      'holidays': 'Svátky',
      'add_holiday': 'Přidat svátek',
      'holiday_name': 'Název svátku',
      'recurring': 'Opakující se',
      'ukraine': 'Ukrajina',
      'usa': 'USA',
      'czech_republic': 'Česká republika',
      'slovakia': 'Slovensko',
      'poland': 'Polsko',

      // Absences
      'absences': 'Absence',
      'absence': 'Absence',
      'absence_type': 'Typ absence',
      'absence_details': 'Detaily absence',
      'new_absence': 'Nová absence',
      'start_date': 'Datum začátku',
      'end_date': 'Datum konce',
      'status': 'Stav',
      'document': 'Dokument',
      'document_hint': 'Číslo neschopenky, číslo příkazu atd.',
      'days': 'dní',
      'no_absences': 'Žádné absence',
      'add_absence': 'Přidat absenci',
      'filter': 'Filtr',
      'all': 'Vše',
      'vacation': 'Dovolená',
      'sick_leave': 'Nemocenská',
      'day_off': 'Volno',
      'unpaid_leave': 'Neplacené volno',
      'study_leave': 'Studijní volno',
      'maternity_leave': 'Mateřská dovolená',
      'childcare_leave': 'Rodičovská dovolená',
      'other': 'Jiné',
      'planned_status': 'Čeká na schválení',
      'approved_status': 'Schváleno',
      'rejected_status': 'Zamítnuto',
      'cancelled': 'Zrušeno',
      'approval_comment': 'Komentář ke schválení/zamítnutí',
      'processed_by': 'Zpracoval',
      'processed_at': 'Zpracováno dne',
      'cancel_request': 'Zrušit žádost',
      'absence_created': 'Absence vytvořena',
      'absence_updated': 'Absence aktualizována',
      'absence_approved': 'Absence schválena',
      'absence_rejected': 'Absence zamítnuta',
      'absence_cancelled': 'Absence zrušena',
      'end_date_before_start_date': 'Datum konce nemůže být před datem začátku',
      'apply': 'Použít',
      'confirmation': 'Potvrzení',
      'delete_work_day_confirm': 'Opravdu chcete smazat tento pracovní den?',
      'work_day_deleted': 'Pracovní den smazán',
      'error_deleting_data': 'Chyba při mazání dat',
      'no_data_to_delete': 'Žádná data ke smazání',
      'create_timesheet_todo':
          'Funkce vytvoření výkazu práce bude implementována později',

      // Osobní příplatky
      'employee_bonuses': 'Osobní příplatky',
      'add_bonus': 'Přidat příplatek',
      'edit_bonus': 'Upravit příplatek',
      'bonus_name': 'Název příplatku',
      'bonus_value': 'Hodnota příplatku',
      'bonus_description': 'Popis příplatku',
      'bonus_type': 'Typ příplatku',
      'calculation_type': 'Typ výpočtu',
      'bonus_start_date': 'Datum začátku',
      'bonus_end_date': 'Datum konce',
      'indefinite': 'Neomezeně',
      'no_bonuses': 'Žádné příplatky',
      'bonus_period': 'Období',

      // Typy příplatků
      'bonus_type_hazardous': 'Škodlivé podmínky',
      'bonus_type_experience': 'Praxe',
      'bonus_type_qualification': 'Kvalifikace',
      'bonus_type_overtime': 'Přesčas',
      'bonus_type_personal': 'Osobní příplatek',
      'bonus_type_achievement': 'Úspěch',

      // Typy výpočtu
      'calculation_type_percentage': 'Procento ze sazby',
      'calculation_type_hourly': 'Pevná částka za hodinu',
      'calculation_type_daily': 'Pevná částka za den',
      'calculation_type_monthly': 'Pevná částka za měsíc',

      // Formátování hodnot
      'value_percentage': '%',
      'value_hourly': '/hod',
      'value_daily': '/den',
      'value_monthly': '/měs',

      // Připomenutí
      'reminders': 'Připomenutí',
      'reminder': 'Připomenutí',
      'reminder_details': 'Detaily připomenutí',
      'no_reminders': 'Žádná připomenutí',
      'snooze': 'Odložit',
      'dismiss': 'Zavřít',
      'missing_timesheet_reminder': 'Připomenutí chybějících údajů v docházce',
      'missing_timesheet_title': 'Chybějící údaje v docházce',
      'missing_timesheet_message':
          'Nezadali jste údaje do docházky za {date}. Prosím, zadejte údaje co nejdříve.',
      'reminder_status_active': 'Aktivní',
      'reminder_status_shown': 'Zobrazeno',
      'reminder_status_snoozed': 'Odloženo',
      'reminder_status_dismissed': 'Zavřeno',
      'snooze_for': 'Odložit o',
      'snooze_for_1_hour': '1 hodinu',
      'snooze_for_3_hours': '3 hodiny',
      'snooze_for_tomorrow': 'Do zítřka',
      'scheduled_for': 'Naplánováno na',
      'refresh': 'Obnovit',
    },
    'sk': {
      'app_name': 'Dochádzka',
      'login': 'Prihlásenie',
      'register': 'Registrácia',
      'email': 'Email',
      'password': 'Heslo',
      'confirm_password': 'Potvrdenie hesla',
      'first_name': 'Meno',
      'last_name': 'Priezvisko',
      'position': 'Pozícia',
      'hourly_rate': 'Hodinová sadzba',
      'admin': 'Administrátor',
      'employee': 'Zamestnanec',
      'role': 'Rola',
      'close': 'Zatvoriť',
      'edit': 'Upraviť',
      'edit_profile': 'Upraviť profil',
      'required_field': 'Povinné pole',
      'invalid_hourly_rate': 'Neplatná hodinová sadzba',
      'invalid_email': 'Neplatný email',
      'country': 'Krajina',
      'currency': 'Mena',
      'change_day_type': 'Zmeniť typ dňa',
      'change': 'Zmeniť',
      'day_type': 'Typ dňa',
      'date': 'Dátum',
      'report_saturday_bonus': 'Príplatok za sobotu',
      'report_sunday_bonus': 'Príplatok za nedeľu',
      'report_holiday_bonus': 'Príplatok za sviatok',
      'report_night_shift_bonus': 'Príplatok za nočnú zmenu',
      'report_hazardous_bonus': 'Príplatok za škodlivé podmienky',
      'report_overtime_bonus': 'Príplatok za nadčas',
      'report_experience_bonus': 'Príplatok za prax',
      'report_qualification_bonus': 'Príplatok za kvalifikáciu',
      'report_personal_bonuses': 'Osobné príplatky',
      'years': 'rokov',
      'level': 'Úroveň',
      'bonuses': 'Príplatky',
      'hire_date': 'Dátum nástupu',
      'not_specified': 'Nie je špecifikované',
      'experience_years': 'Prax (roky)',
      'qualification_level': 'Úroveň kvalifikácie (1-5)',
      'invalid_experience': 'Neplatná prax',
      'invalid_qualification': 'Úroveň musí byť medzi 1 a 5',
      'export_pdf': 'Export do PDF',
      'print': 'Tlač',
      'preview': 'Náhľad',
      'timesheet_report': 'Výkaz práce',
      'report_employee': 'Zamestnanec',
      'report_position': 'Pozícia',
      'report_period': 'Obdobie',
      'category': 'Kategória',
      'value': 'Hodnota',
      'employee_signature': 'Podpis zamestnanca',
      'manager_signature': 'Podpis vedúceho',
      'report_date': 'Dátum výkazu',
      'export_success': 'Správa úspešne exportovaná',
      'timesheet': 'Dochádzka',
      'timesheets': 'Dochádzky',
      'work_day': 'Pracovný deň',
      'work_days': 'Pracovné dni',
      'hours': 'Hodiny',
      'hours_short': 'h',
      'report_date_label': 'Dátum',
      'report_day_type': 'Typ dňa',
      'shift_type': 'Typ zmeny',
      'regular': 'Bežný',
      'saturday': 'Sobota',
      'sunday': 'Nedeľa',
      'holiday': 'Sviatok',
      'day_shift': 'Denná zmena',
      'night_shift': 'Nočná zmena',
      'bonus_settings': 'Nastavenia príplatkov',
      'night_shift_bonus': 'Príplatok za nočnú zmenu',
      'saturday_bonus': 'Príplatok za prácu v sobotu',
      'sunday_bonus': 'Príplatok za prácu v nedeľu',
      'holiday_bonus': 'Príplatok za prácu vo sviatok',
      'save': 'Uložiť',
      'cancel': 'Zrušiť',
      'delete': 'Vymazať',
      'add': 'Pridať',
      'submit': 'Odoslať',
      'approve': 'Schváliť',
      'reject': 'Odmietnuť',
      'draft': 'Koncept',
      'submitted': 'Odoslané',
      'approved': 'Schválené',
      'rejected': 'Odmietnuté',
      'total_hours': 'Celkom hodín',
      'total_wage': 'Celková mzda',
      'comment': 'Komentár',
      'settings': 'Nastavenia',
      'language': 'Jazyk',
      'theme': 'Téma',
      'light': 'Svetlá',
      'dark': 'Tmavá',
      'system': 'Systémová',
      'sync': 'Synchronizácia',
      'sync_success': 'Synchronizácia úspešná',
      'sync_error': 'Chyba synchronizácie',
      'no_internet': 'Žiadne pripojenie k internetu',
      'logout': 'Odhlásiť sa',
      'profile': 'Profil',
      'change_password': 'Zmeniť heslo',
      'reset_password': 'Obnoviť heslo',
      'current_password': 'Aktuálne heslo',
      'new_password': 'Nové heslo',
      'password_reset_sent':
          'Pokyny na obnovenie hesla boli odoslané na váš e-mail',
      'verify_reset_code': 'Overiť kód',
      'verify_reset_code_info': 'Zadajte overovací kód a nové heslo',
      'reset_code': 'Overovací kód',
      'your_reset_code': 'Váš overovací kód',
      'submit_new_password': 'Zmeniť heslo',
      'password_reset_success': 'Heslo bolo úspešne zmenené',
      'back_to_login': 'Späť na prihlásenie',
      'code_sent_to_email': 'Overovací kód bol odoslaný na váš e-mail',
      'error': 'Chyba',
      'success': 'Úspech',
      'password_mismatch': 'Heslá sa nezhodujú',
      'password_too_short': 'Heslo musí mať aspoň 6 znakov',
      'invalid_hours': 'Neplatný počet hodín',
      'export': 'Export',
      'import': 'Import',
      'pdf': 'PDF',
      'excel': 'Excel',
      'reports': 'Reporty',
      'report': 'Report',
      'period': 'Obdobie',
      'from': 'Od',
      'to': 'Do',
      'employees': 'Zamestnanci',
      'holidays': 'Sviatky',
      'add_holiday': 'Pridať sviatok',
      'holiday_name': 'Názov sviatku',
      'recurring': 'Opakujúci sa',
      'ukraine': 'Ukrajina',
      'usa': 'USA',
      'czech_republic': 'Česká republika',
      'slovakia': 'Slovensko',
      'poland': 'Poľsko',

      // Absences
      'absences': 'Absencie',
      'confirmation': 'Potvrdenie',
      'delete_work_day_confirm': 'Naozaj chcete vymazať tento pracovný deň?',
      'work_day_deleted': 'Pracovný deň vymazaný',
      'error_deleting_data': 'Chyba pri mazaní dát',
      'no_data_to_delete': 'Žiadne dáta na vymazanie',
      'create_timesheet_todo':
          'Funkcia vytvorenia výkazu práce bude implementovaná neskôr',

      // Osobné príplatky
      'employee_bonuses': 'Osobné príplatky',
      'add_bonus': 'Pridať príplatok',
      'edit_bonus': 'Upraviť príplatok',
      'bonus_name': 'Názov príplatku',
      'bonus_value': 'Hodnota príplatku',
      'bonus_description': 'Popis príplatku',
      'bonus_type': 'Typ príplatku',
      'calculation_type': 'Typ výpočtu',
      'bonus_start_date': 'Dátum začiatku',
      'bonus_end_date': 'Dátum konca',
      'indefinite': 'Neobmedzene',
      'no_bonuses': 'Žiadne príplatky',
      'bonus_period': 'Obdobie',

      // Typy príplatkov
      'bonus_type_hazardous': 'Škodlivé podmienky',
      'bonus_type_experience': 'Prax',
      'bonus_type_qualification': 'Kvalifikácia',
      'bonus_type_overtime': 'Nadčas',
      'bonus_type_personal': 'Osobný príplatok',
      'bonus_type_achievement': 'Úspech',

      // Typy výpočtu
      'calculation_type_percentage': 'Percento zo sadzby',
      'calculation_type_hourly': 'Pevná suma za hodinu',
      'calculation_type_daily': 'Pevná suma za deň',
      'calculation_type_monthly': 'Pevná suma za mesiac',

      // Formátovanie hodnôt
      'value_percentage': '%',
      'value_hourly': '/hod',
      'value_daily': '/deň',
      'value_monthly': '/mes',

      // Pripomienky
      'reminders': 'Pripomienky',
      'reminder': 'Pripomienka',
      'reminder_details': 'Detaily pripomienky',
      'no_reminders': 'Žiadne pripomienky',
      'snooze': 'Odložiť',
      'dismiss': 'Zatvoriť',
      'missing_timesheet_reminder':
          'Pripomienka chýbajúcich údajov v dochádzke',
      'missing_timesheet_title': 'Chýbajúce údaje v dochádzke',
      'missing_timesheet_message':
          'Nezadali ste údaje do dochádzky za {date}. Prosím, zadajte údaje čo najskôr.',
      'reminder_status_active': 'Aktívna',
      'reminder_status_shown': 'Zobrazená',
      'reminder_status_snoozed': 'Odložená',
      'reminder_status_dismissed': 'Zatvorená',
      'snooze_for': 'Odložiť o',
      'snooze_for_1_hour': '1 hodinu',
      'snooze_for_3_hours': '3 hodiny',
      'snooze_for_tomorrow': 'Do zajtra',
      'scheduled_for': 'Naplánované na',
      'refresh': 'Obnoviť',
    },
    'pl': {
      'app_name': 'Ewidencja czasu pracy',
      'login': 'Logowanie',
      'register': 'Rejestracja',
      'email': 'Email',
      'password': 'Hasło',
      'confirm_password': 'Potwierdzenie hasła',
      'first_name': 'Imię',
      'last_name': 'Nazwisko',
      'position': 'Stanowisko',
      'hourly_rate': 'Stawka godzinowa',
      'admin': 'Administrator',
      'employee': 'Pracownik',
      'role': 'Rola',
      'close': 'Zamknij',
      'edit': 'Edytuj',
      'edit_profile': 'Edytuj profil',
      'required_field': 'Pole wymagane',
      'invalid_hourly_rate': 'Nieprawidłowa stawka godzinowa',
      'invalid_email': 'Nieprawidłowy email',
      'country': 'Kraj',
      'currency': 'Waluta',
      'change_day_type': 'Zmień typ dnia',
      'change': 'Zmień',
      'day_type': 'Typ dnia',
      'date': 'Data',
      'report_saturday_bonus': 'Dodatek za sobotę',
      'report_sunday_bonus': 'Dodatek za niedzielę',
      'report_holiday_bonus': 'Dodatek za święto',
      'report_night_shift_bonus': 'Dodatek za zmianę nocną',
      'report_hazardous_bonus': 'Dodatek za warunki szkodliwe',
      'report_overtime_bonus': 'Dodatek za nadgodziny',
      'report_experience_bonus': 'Dodatek za doświadczenie',
      'report_qualification_bonus': 'Dodatek za kwalifikacje',
      'report_personal_bonuses': 'Dodatki osobiste',
      'years': 'lat',
      'level': 'Poziom',
      'bonuses': 'Dodatki',
      'export_pdf': 'Eksport do PDF',
      'print': 'Drukuj',
      'preview': 'Podgląd',
      'timesheet_report': 'Raport czasu pracy',
      'report_employee': 'Pracownik',
      'report_position': 'Stanowisko',
      'report_period': 'Okres',
      'category': 'Kategoria',
      'value': 'Wartość',
      'employee_signature': 'Podpis pracownika',
      'manager_signature': 'Podpis kierownika',
      'report_date': 'Data raportu',
      'export_success': 'Raport pomyślnie wyeksportowany',
      'timesheet': 'Ewidencja',
      'timesheets': 'Ewidencje',
      'work_day': 'Dzień pracy',
      'work_days': 'Dni pracy',
      'hours': 'Godziny',
      'hours_short': 'g',
      'report_date_label': 'Data',
      'report_day_type': 'Typ dnia',
      'shift_type': 'Typ zmiany',
      'regular': 'Zwykły',
      'saturday': 'Sobota',
      'sunday': 'Niedziela',
      'holiday': 'Święto',
      'day_shift': 'Zmiana dzienna',
      'night_shift': 'Zmiana nocna',
      'bonus_settings': 'Ustawienia dodatków',
      'night_shift_bonus': 'Dodatek za zmianę nocną',
      'saturday_bonus': 'Dodatek za pracę w sobotę',
      'sunday_bonus': 'Dodatek za pracę w niedzielę',
      'holiday_bonus': 'Dodatek za pracę w święto',
      'save': 'Zapisz',
      'cancel': 'Anuluj',
      'delete': 'Usuń',
      'add': 'Dodaj',
      'submit': 'Wyślij',
      'approve': 'Zatwierdź',
      'reject': 'Odrzuć',
      'draft': 'Szkic',
      'submitted': 'Wysłane',
      'approved': 'Zatwierdzone',
      'rejected': 'Odrzucone',
      'total_hours': 'Łączna liczba godzin',
      'total_wage': 'Łączna płaca',
      'comment': 'Komentarz',
      'settings': 'Ustawienia',
      'language': 'Język',
      'theme': 'Motyw',
      'light': 'Jasny',
      'dark': 'Ciemny',
      'system': 'Systemowy',
      'sync': 'Synchronizacja',
      'sync_success': 'Synchronizacja zakończona pomyślnie',
      'sync_error': 'Błąd synchronizacji',
      'no_internet': 'Brak połączenia z internetem',
      'logout': 'Wyloguj',
      'profile': 'Profil',
      'change_password': 'Zmień hasło',
      'reset_password': 'Zresetuj hasło',
      'current_password': 'Aktualne hasło',
      'new_password': 'Nowe hasło',
      'password_reset_sent':
          'Instrukcje resetowania hasła zostały wysłane na Twój adres e-mail',
      'verify_reset_code': 'Weryfikuj kod',
      'verify_reset_code_info': 'Wprowadź kod weryfikacyjny i nowe hasło',
      'reset_code': 'Kod weryfikacyjny',
      'your_reset_code': 'Twój kod weryfikacyjny',
      'submit_new_password': 'Zmień hasło',
      'password_reset_success': 'Hasło zostało pomyślnie zmienione',
      'back_to_login': 'Powrót do logowania',
      'code_sent_to_email':
          'Kod weryfikacyjny został wysłany na Twój adres e-mail',
      'error': 'Błąd',
      'success': 'Sukces',
      'password_mismatch': 'Hasła nie pasują do siebie',
      'password_too_short': 'Hasło musi mieć co najmniej 6 znaków',
      'invalid_hours': 'Nieprawidłowa liczba godzin',
      'export': 'Eksport',
      'import': 'Import',
      'pdf': 'PDF',
      'excel': 'Excel',
      'reports': 'Raporty',
      'report': 'Raport',
      'period': 'Okres',
      'from': 'Od',
      'to': 'Do',
      'employees': 'Pracownicy',
      'holidays': 'Święta',
      'add_holiday': 'Dodaj święto',
      'holiday_name': 'Nazwa święta',
      'recurring': 'Powtarzające się',
      'ukraine': 'Ukraina',
      'usa': 'USA',
      'czech_republic': 'Republika Czeska',
      'slovakia': 'Słowacja',
      'poland': 'Polska',

      // Absences
      'absences': 'Nieobecności',
      'confirmation': 'Potwierdzenie',
      'delete_work_day_confirm': 'Czy na pewno chcesz usunąć ten dzień pracy?',
      'work_day_deleted': 'Dzień pracy usunięty',
      'error_deleting_data': 'Błąd podczas usuwania danych',
      'no_data_to_delete': 'Brak danych do usunięcia',
      'create_timesheet_todo':
          'Funkcja tworzenia karty pracy zostanie zaimplementowana później',

      // Dodatki osobiste
      'employee_bonuses': 'Dodatki osobiste',
      'add_bonus': 'Dodaj dodatek',
      'edit_bonus': 'Edytuj dodatek',
      'bonus_name': 'Nazwa dodatku',
      'bonus_value': 'Wartość dodatku',
      'bonus_description': 'Opis dodatku',
      'bonus_type': 'Typ dodatku',
      'calculation_type': 'Typ obliczenia',
      'bonus_start_date': 'Data rozpoczęcia',
      'bonus_end_date': 'Data zakończenia',
      'indefinite': 'Bezterminowo',
      'no_bonuses': 'Brak dodatków',
      'bonus_period': 'Okres',

      // Typy dodatków
      'bonus_type_hazardous': 'Warunki szkodliwe',
      'bonus_type_experience': 'Doświadczenie',
      'bonus_type_qualification': 'Kwalifikacje',
      'bonus_type_overtime': 'Nadgodziny',
      'bonus_type_personal': 'Dodatek osobisty',
      'bonus_type_achievement': 'Osiągnięcie',

      // Przypomnienia
      'reminders': 'Przypomnienia',
      'reminder': 'Przypomnienie',
      'reminder_details': 'Szczegóły przypomnienia',
      'no_reminders': 'Brak przypomnień',
      'snooze': 'Drzemka',
      'dismiss': 'Zamknij',
      'missing_timesheet_reminder':
          'Przypomnienie o brakujących danych w ewidencji',
      'missing_timesheet_title': 'Brakujące dane w ewidencji',
      'missing_timesheet_message':
          'Nie wprowadzono danych do ewidencji za {date}. Proszę wprowadzić dane jak najszybciej.',
      'reminder_status_active': 'Aktywne',
      'reminder_status_shown': 'Wyświetlone',
      'reminder_status_snoozed': 'Odłożone',
      'reminder_status_dismissed': 'Zamknięte',
      'snooze_for': 'Odłóż o',
      'snooze_for_1_hour': '1 godzinę',
      'snooze_for_3_hours': '3 godziny',
      'snooze_for_tomorrow': 'Do jutra',
      'scheduled_for': 'Zaplanowane na',
      'refresh': 'Odśwież',

      // Typy obliczeń
      'calculation_type_percentage': 'Procent od stawki',
      'calculation_type_hourly': 'Stała kwota za godzinę',
      'calculation_type_daily': 'Stała kwota za dzień',
      'calculation_type_monthly': 'Stała kwota za miesiąc',

      // Formatowanie wartości
      'value_percentage': '%',
      'value_hourly': '/godz',
      'value_daily': '/dzień',
      'value_monthly': '/mies',
    },
  };

  /// Отримання перекладу
  String translate(String key) {
    // Спочатку перевіряємо в основних перекладах
    final baseTranslation = _localizedValues[locale.languageCode]?[key];
    if (baseTranslation != null) {
      return baseTranslation;
    }

    // Якщо не знайдено, перевіряємо в додаткових перекладах
    if (locale.languageCode == 'uk') {
      final syncReminderTranslation = ukSyncAndReminderTranslations[key];
      if (syncReminderTranslation != null) {
        return syncReminderTranslation;
      }
    } else if (locale.languageCode == 'en') {
      final syncReminderTranslation = enSyncAndReminderTranslations[key];
      if (syncReminderTranslation != null) {
        return syncReminderTranslation;
      }
    }

    // Якщо не знайдено ніде, повертаємо ключ
    return key;
  }

  /// Форматування дати
  String formatDate(DateTime date, {String format = 'dd.MM.yyyy'}) {
    return DateFormat(format, locale.languageCode).format(date);
  }

  /// Форматування дати та часу
  String formatDateTime(
    DateTime dateTime, {
    String format = 'dd.MM.yyyy HH:mm',
  }) {
    return DateFormat(format, locale.languageCode).format(dateTime);
  }

  /// Форматування грошової суми
  String formatCurrency(
    double amount, {
    String? symbol,
    int? decimalDigits,
    String? currencyCode,
  }) {
    return NumberFormat.currency(
      locale: locale.languageCode,
      symbol: symbol,
      decimalDigits: decimalDigits ?? 2,
      name: currencyCode,
    ).format(amount);
  }

  /// Форматування відсотків
  String formatPercent(double percent) {
    return NumberFormat.percentPattern(locale.languageCode).format(percent);
  }
}

/// Делегат для локалізації
class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLanguage.values.any(
      (language) => language.code == locale.languageCode,
    );
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
