# Восстановление проекта Tabel App
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Восстановление проекта Tabel App" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Проверяем Flutter
try {
    $null = flutter --version 2>&1
    Write-Host "✅ Flutter найден!" -ForegroundColor Green
} catch {
    Write-Host "❌ Flutter не найден!" -ForegroundColor Red
    Write-Host "Сначала установите Flutter и запустите .\setup_flutter.ps1" -ForegroundColor Yellow
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

# Проверяем наличие проекта
if (-not (Test-Path "tabel_app\pubspec.yaml")) {
    Write-Host "❌ Проект tabel_app не найден!" -ForegroundColor Red
    Write-Host "Убедитесь, что вы находитесь в правильной папке." -ForegroundColor Yellow
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

Write-Host "✅ Проект найден!" -ForegroundColor Green
Write-Host ""

# Переходим в папку проекта
Set-Location tabel_app

# Очищаем кеш
Write-Host "Очищаем кеш..." -ForegroundColor Yellow
try {
    flutter clean
    Write-Host "✅ Кеш очищен" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Ошибка при очистке кеша" -ForegroundColor Yellow
}
Write-Host ""

# Удаляем старые файлы сборки
Write-Host "Удаляем старые файлы сборки..." -ForegroundColor Yellow
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
    Write-Host "✅ Папка build удалена" -ForegroundColor Green
}
if (Test-Path ".dart_tool") {
    Remove-Item -Recurse -Force ".dart_tool"
    Write-Host "✅ Папка .dart_tool удалена" -ForegroundColor Green
}
Write-Host ""

# Получаем зависимости
Write-Host "Получаем зависимости..." -ForegroundColor Yellow
try {
    flutter pub get
    Write-Host "✅ Зависимости получены" -ForegroundColor Green
} catch {
    Write-Host "❌ Ошибка при получении зависимостей" -ForegroundColor Red
}
Write-Host ""

# Включаем поддержку Windows
Write-Host "Включаем поддержку Windows desktop..." -ForegroundColor Yellow
try {
    flutter config --enable-windows-desktop
    Write-Host "✅ Поддержка Windows включена" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Ошибка при включении поддержки Windows" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Проект восстановлен!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Теперь можно запускать:" -ForegroundColor Cyan
Write-Host "flutter run -d windows" -ForegroundColor White
Write-Host ""

# Возвращаемся в родительскую папку
Set-Location ..

Read-Host "Нажмите Enter для выхода"
