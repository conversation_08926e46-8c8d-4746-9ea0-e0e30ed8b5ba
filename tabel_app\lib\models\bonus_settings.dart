import 'package:equatable/equatable.dart';

/// Модель налаштувань надбавок
class BonusSettings extends Equatable {
  /// Відсоток надбавки за нічну зміну (наприклад, 0.2 = 20%)
  final double nightShiftBonus;

  /// Відсоток надбавки за роботу в суботу
  final double saturdayBonus;

  /// Відсоток надбавки за роботу в неділю
  final double sundayBonus;

  /// Відсоток надбавки за роботу в святковий день
  final double holidayBonus;

  /// Відсоток надбавки за шкідливі умови праці
  final double hazardousBonus;

  /// Відсоток надбавки за понаднормову роботу
  final double overtimeBonus;

  /// Відсоток надбавки за стаж роботи (за кожен рік)
  final double experienceBonus;

  /// Відсоток надбавки за кваліфікацію (за кожен рівень)
  final double qualificationBonus;

  /// Конструктор
  const BonusSettings({
    this.nightShiftBonus = 0.2, // 20% за замовчуванням
    this.saturdayBonus = 0.5, // 50% за замовчуванням
    this.sundayBonus = 1.0, // 100% за замовчуванням
    this.holidayBonus = 1.0, // 100% за замовчуванням
    this.hazardousBonus = 0.25, // 25% за замовчуванням
    this.overtimeBonus = 0.5, // 50% за замовчуванням
    this.experienceBonus = 0.03, // 3% за кожен рік стажу
    this.qualificationBonus = 0.05, // 5% за кожен рівень кваліфікації
  });

  /// Створення копії об'єкта з можливістю зміни окремих полів
  BonusSettings copyWith({
    double? nightShiftBonus,
    double? saturdayBonus,
    double? sundayBonus,
    double? holidayBonus,
    double? hazardousBonus,
    double? overtimeBonus,
    double? experienceBonus,
    double? qualificationBonus,
  }) {
    return BonusSettings(
      nightShiftBonus: nightShiftBonus ?? this.nightShiftBonus,
      saturdayBonus: saturdayBonus ?? this.saturdayBonus,
      sundayBonus: sundayBonus ?? this.sundayBonus,
      holidayBonus: holidayBonus ?? this.holidayBonus,
      hazardousBonus: hazardousBonus ?? this.hazardousBonus,
      overtimeBonus: overtimeBonus ?? this.overtimeBonus,
      experienceBonus: experienceBonus ?? this.experienceBonus,
      qualificationBonus: qualificationBonus ?? this.qualificationBonus,
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'nightShiftBonus': nightShiftBonus,
      'saturdayBonus': saturdayBonus,
      'sundayBonus': sundayBonus,
      'holidayBonus': holidayBonus,
      'hazardousBonus': hazardousBonus,
      'overtimeBonus': overtimeBonus,
      'experienceBonus': experienceBonus,
      'qualificationBonus': qualificationBonus,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory BonusSettings.fromMap(Map<String, dynamic> map) {
    return BonusSettings(
      nightShiftBonus: map['nightShiftBonus'] ?? 0.2,
      saturdayBonus: map['saturdayBonus'] ?? 0.5,
      sundayBonus: map['sundayBonus'] ?? 1.0,
      holidayBonus: map['holidayBonus'] ?? 1.0,
      hazardousBonus: map['hazardousBonus'] ?? 0.25,
      overtimeBonus: map['overtimeBonus'] ?? 0.5,
      experienceBonus: map['experienceBonus'] ?? 0.03,
      qualificationBonus: map['qualificationBonus'] ?? 0.05,
    );
  }

  @override
  List<Object?> get props => [
    nightShiftBonus,
    saturdayBonus,
    sundayBonus,
    holidayBonus,
    hazardousBonus,
    overtimeBonus,
    experienceBonus,
    qualificationBonus,
  ];
}
