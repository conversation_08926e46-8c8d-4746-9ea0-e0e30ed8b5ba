import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';

/// Перевірка, чи є день вихідним (субота або неділя)
bool isWeekend(DateTime date) {
  // 6 - субота, 7 - неділя
  return date.weekday >= 6;
}

/// Перевірка, чи є день святковим
Future<bool> isHoliday(DateTime date) async {
  try {
    // Тут має бути логіка перевірки святкових днів
    // Для простоти зараз повертаємо false
    return false;
  } catch (e) {
    debugPrint('Error checking if day is holiday: $e');
    return false;
  }
}

/// Утиліти для роботи з датами
class AppDateUtils {
  /// Перевірка, чи є дата сьогоднішньою
  static bool isToday(DateTime date) {
    final DateTime now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// Перевірка, чи є дата вчорашньою
  static bool isYesterday(DateTime date) {
    final DateTime yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// Перевірка, чи є дата завтрашньою
  static bool isTomorrow(DateTime date) {
    final DateTime tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year &&
        date.month == tomorrow.month &&
        date.day == tomorrow.day;
  }

  /// Перевірка, чи є дата в цьому тижні
  static bool isThisWeek(DateTime date) {
    final DateTime now = DateTime.now();
    final DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final DateTime endOfWeek = startOfWeek.add(const Duration(days: 6));
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
        date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// Перевірка, чи є дата в цьому місяці
  static bool isThisMonth(DateTime date) {
    final DateTime now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  /// Перевірка, чи є дата в цьому році
  static bool isThisYear(DateTime date) {
    final DateTime now = DateTime.now();
    return date.year == now.year;
  }

  /// Отримання першого дня місяця
  static DateTime getFirstDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Отримання останнього дня місяця
  static DateTime getLastDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0);
  }

  /// Отримання першого дня тижня
  static DateTime getFirstDayOfWeek(DateTime date, {int startOfWeek = 1}) {
    // startOfWeek: 1 - понеділок, 7 - неділя
    int weekday = date.weekday;
    return date.subtract(Duration(days: (weekday - startOfWeek) % 7));
  }

  /// Отримання останнього дня тижня
  static DateTime getLastDayOfWeek(DateTime date, {int startOfWeek = 1}) {
    // startOfWeek: 1 - понеділок, 7 - неділя
    DateTime firstDay = getFirstDayOfWeek(date, startOfWeek: startOfWeek);
    return firstDay.add(const Duration(days: 6));
  }

  /// Отримання першого дня року
  static DateTime getFirstDayOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  /// Отримання останнього дня року
  static DateTime getLastDayOfYear(DateTime date) {
    return DateTime(date.year, 12, 31);
  }

  /// Отримання кількості днів у місяці
  static int getDaysInMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0).day;
  }

  /// Отримання кількості робочих днів у місяці (без субот та неділь)
  static int getWorkDaysInMonth(DateTime date) {
    final DateTime firstDay = getFirstDayOfMonth(date);
    final DateTime lastDay = getLastDayOfMonth(date);
    int workDays = 0;

    for (int i = 0; i < lastDay.day; i++) {
      final DateTime currentDay = firstDay.add(Duration(days: i));
      if (currentDay.weekday != 6 && currentDay.weekday != 7) {
        workDays++;
      }
    }

    return workDays;
  }

  /// Форматування дати
  static String formatDate(
    DateTime date, {
    String format = 'dd.MM.yyyy',
    String? locale,
  }) {
    return DateFormat(format, locale).format(date);
  }

  /// Форматування часу
  static String formatTime(
    DateTime time, {
    String format = 'HH:mm',
    String? locale,
  }) {
    return DateFormat(format, locale).format(time);
  }

  /// Форматування дати та часу
  static String formatDateTime(
    DateTime dateTime, {
    String format = 'dd.MM.yyyy HH:mm',
    String? locale,
  }) {
    return DateFormat(format, locale).format(dateTime);
  }

  /// Парсинг дати з рядка
  static DateTime? parseDate(
    String date, {
    String format = 'dd.MM.yyyy',
    String? locale,
  }) {
    try {
      return DateFormat(format, locale).parse(date);
    } catch (e) {
      return null;
    }
  }

  /// Парсинг часу з рядка
  static DateTime? parseTime(
    String time, {
    String format = 'HH:mm',
    String? locale,
  }) {
    try {
      return DateFormat(format, locale).parse(time);
    } catch (e) {
      return null;
    }
  }

  /// Парсинг дати та часу з рядка
  static DateTime? parseDateTime(
    String dateTime, {
    String format = 'dd.MM.yyyy HH:mm',
    String? locale,
  }) {
    try {
      return DateFormat(format, locale).parse(dateTime);
    } catch (e) {
      return null;
    }
  }

  /// Отримання різниці між датами у днях
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return (to.difference(from).inHours / 24).round();
  }

  /// Отримання різниці між датами у місяцях
  static int monthsBetween(DateTime from, DateTime to) {
    return (to.year - from.year) * 12 + to.month - from.month;
  }

  /// Отримання різниці між датами у роках
  static int yearsBetween(DateTime from, DateTime to) {
    return to.year - from.year;
  }

  /// Отримання списку дат між двома датами
  static List<DateTime> getDaysInRange(DateTime from, DateTime to) {
    final List<DateTime> days = [];
    for (int i = 0; i <= daysBetween(from, to); i++) {
      days.add(from.add(Duration(days: i)));
    }
    return days;
  }

  /// Отримання списку робочих днів між двома датами (без субот та неділь)
  static List<DateTime> getWorkDaysInRange(DateTime from, DateTime to) {
    final List<DateTime> days = [];
    for (int i = 0; i <= daysBetween(from, to); i++) {
      final DateTime currentDay = from.add(Duration(days: i));
      if (currentDay.weekday != 6 && currentDay.weekday != 7) {
        days.add(currentDay);
      }
    }
    return days;
  }

  /// Отримання списку вихідних днів між двома датами (субот та неділь)
  static List<DateTime> getWeekendDaysInRange(DateTime from, DateTime to) {
    final List<DateTime> days = [];
    for (int i = 0; i <= daysBetween(from, to); i++) {
      final DateTime currentDay = from.add(Duration(days: i));
      if (currentDay.weekday == 6 || currentDay.weekday == 7) {
        days.add(currentDay);
      }
    }
    return days;
  }
}
