import 'package:equatable/equatable.dart';

/// Модель валюти
class Currency extends Equatable {
  /// Код валюти
  final String code;

  /// Символ валюти
  final String symbol;

  /// Назва валюти
  final String name;

  /// Кількість десяткових знаків
  final int decimalDigits;

  /// Конструктор
  const Currency({
    required this.code,
    required this.symbol,
    required this.name,
    this.decimalDigits = 2,
  });

  @override
  List<Object?> get props => [code, symbol, name, decimalDigits];
}

/// Список доступних валют
class Currencies {
  /// Українська гривня
  static const Currency uah = Currency(
    code: 'UAH',
    symbol: 'грн',
    name: 'Українська гривня',
  );

  /// Долар США
  static const Currency usd = Currency(
    code: 'USD',
    symbol: '\$',
    name: 'Долар США',
  );

  /// Чеська крона
  static const Currency czk = Currency(
    code: 'CZK',
    symbol: 'Kč',
    name: 'Чеська крона',
  );

  /// Словацька євро
  static const Currency eur = Currency(code: 'EUR', symbol: '€', name: 'Євро');

  /// Польський злотий
  static const Currency pln = Currency(
    code: 'PLN',
    symbol: 'zł',
    name: 'Польський злотий',
  );

  /// Отримання валюти за кодом країни
  static Currency getCurrencyByCountryCode(String countryCode) {
    switch (countryCode) {
      case 'UA':
        return uah;
      case 'US':
        return usd;
      case 'CZ':
        return czk;
      case 'SK':
        return eur;
      case 'PL':
        return pln;
      default:
        return usd;
    }
  }
}
