name: Android Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '11'
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.10.0'
          channel: 'stable'
      
      - name: Get dependencies
        run: flutter pub get
      
      - name: Create keystore.properties
        run: |
          echo "storeFile=debug.keystore" > android/keystore.properties
          echo "storePassword=android" >> android/keystore.properties
          echo "keyAlias=androiddebugkey" >> android/keystore.properties
          echo "keyPassword=android" >> android/keystore.properties
      
      - name: Use debug keystore
        run: |
          echo "${{ secrets.DEBUG_KEYSTORE }}" | base64 --decode > android/app/debug.keystore
      
      - name: Build APK
        run: flutter build apk --debug
      
      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: app-debug
          path: build/app/outputs/flutter-apk/app-debug.apk
