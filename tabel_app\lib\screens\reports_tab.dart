import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';

/// Вкладка звітів
class ReportsTab extends StatefulWidget {
  const ReportsTab({super.key});

  @override
  State<ReportsTab> createState() => _ReportsTabState();
}

class _ReportsTabState extends State<ReportsTab> {
  // Контролери для вибору періоду
  late DateTime _startDate;
  late DateTime _endDate;

  // Сервіси
  late DatabaseService _databaseService;
  late CalculationService _calculationService;

  // Дані
  List<Timesheet> _timesheets = [];
  Map<String, dynamic>? _reportData;
  bool _isLoading = true;
  String? _errorMessage;
  String _currencySymbol = 'грн'; // За замовчуванням гривня

  // Сервіси
  PdfService? _pdfService;
  late AppCountry _country;

  @override
  void initState() {
    super.initState();

    // Ініціалізація дат (поточний місяць)
    final now = DateTime.now();
    _startDate = DateTime(now.year, now.month, 1);
    _endDate = DateTime(now.year, now.month + 1, 0);

    // Ініціалізація сервісів
    _databaseService = Provider.of<DatabaseService>(context, listen: false);
    _calculationService = Provider.of<CalculationService>(
      context,
      listen: false,
    );

    // Завантаження даних
    _loadData();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Отримання поточного працівника
      final appState = Provider.of<AppState>(context, listen: false);
      final employee = appState.currentEmployee;
      if (employee == null) {
        setState(() {
          _errorMessage = 'Працівника не знайдено';
        });
        return;
      }

      // Отримання валюти відповідно до країни
      setState(() {
        _currencySymbol = appState.country.getCurrencySymbol();
        _country = appState.country;
      });

      // Отримання табелів за період
      final timesheets = await _databaseService.getTimesheetsByEmployee(
        employee.id,
      );

      // Фільтрація табелів за періодом
      final filteredTimesheets =
          timesheets.where((timesheet) {
            return timesheet.startDate.isAfter(
                  _startDate.subtract(const Duration(days: 1)),
                ) &&
                timesheet.endDate.isBefore(
                  _endDate.add(const Duration(days: 1)),
                );
          }).toList();

      // Завжди отримуємо робочі дні за період для звіту
      // Отримання робочих днів за період
      final workDays = await _databaseService.getWorkDaysByEmployeeAndPeriod(
        employee.id,
        _startDate,
        _endDate,
      );

      if (workDays.isNotEmpty) {
        // Створення тимчасового табеля
        final tempTimesheet = Timesheet(
          employeeId: employee.id,
          startDate: _startDate,
          endDate: _endDate,
          workDays: workDays,
          status: TimesheetStatus.draft,
        );

        // Додаємо тимчасовий табель до списку
        filteredTimesheets
            .clear(); // Очищаємо список, щоб використовувати найсвіжіші дані
        filteredTimesheets.add(tempTimesheet);
      }

      // Розрахунок даних для звіту
      Map<String, dynamic>? reportData;
      if (filteredTimesheets.isNotEmpty) {
        // Беремо перший табель для звіту
        final timesheet = filteredTimesheets.first;
        reportData = await _calculationService.calculateTimesheetWage(
          timesheet,
        );
      }

      setState(() {
        _timesheets = filteredTimesheets;
        _reportData = reportData;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка завантаження даних: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Вибір початкової дати
  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;

        // Якщо кінцева дата раніше за початкову, оновлюємо її
        if (_endDate.isBefore(_startDate)) {
          _endDate = _startDate.add(const Duration(days: 30));
        }
      });

      // Завантаження даних для нового періоду
      _loadData();
    }
  }

  /// Вибір кінцевої дати
  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });

      // Завантаження даних для нового періоду
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Заголовок
                    Text(
                      localizations.translate('reports'),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Вибір періоду
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('period'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Вибір дат
                            Row(
                              children: [
                                // Початкова дата
                                Expanded(
                                  child: InkWell(
                                    onTap: _selectStartDate,
                                    child: InputDecorator(
                                      decoration: InputDecoration(
                                        labelText: localizations.translate(
                                          'from',
                                        ),
                                        border: const OutlineInputBorder(),
                                      ),
                                      child: Text(
                                        DateFormat(
                                          'dd.MM.yyyy',
                                        ).format(_startDate),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),

                                // Кінцева дата
                                Expanded(
                                  child: InkWell(
                                    onTap: _selectEndDate,
                                    child: InputDecorator(
                                      decoration: InputDecoration(
                                        labelText: localizations.translate(
                                          'to',
                                        ),
                                        border: const OutlineInputBorder(),
                                      ),
                                      child: Text(
                                        DateFormat(
                                          'dd.MM.yyyy',
                                        ).format(_endDate),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Повідомлення про помилку
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),

                    // Дані звіту
                    if (_reportData != null)
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                localizations.translate('report'),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Загальна кількість годин
                              _buildReportRow(
                                localizations.translate('total_hours'),
                                '${_reportData!['totalHours']}',
                              ),

                              // Звичайні дні
                              _buildReportRow(
                                localizations.translate('regular'),
                                '${_reportData!['regularHours']} ${localizations.translate('hours')} - ${_reportData!['regularHoursWage'].toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              // Суботи
                              _buildReportRow(
                                localizations.translate('saturday'),
                                '${_reportData!['saturdayHours']} ${localizations.translate('hours')} - ${_reportData!['saturdayHoursWage'].toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              // Неділі
                              _buildReportRow(
                                localizations.translate('sunday'),
                                '${_reportData!['sundayHours']} ${localizations.translate('hours')} - ${_reportData!['sundayHoursWage'].toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              // Святкові дні
                              _buildReportRow(
                                localizations.translate('holiday'),
                                '${_reportData!['holidayHours']} ${localizations.translate('hours')} - ${_reportData!['holidayHoursWage'].toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              // Денні зміни
                              _buildReportRow(
                                localizations.translate('day_shift'),
                                '${_reportData!['dayShiftHours']} ${localizations.translate('hours')}',
                              ),

                              // Нічні зміни
                              _buildReportRow(
                                localizations.translate('night_shift'),
                                '${_reportData!['nightShiftHours']} ${localizations.translate('hours')} - ${_reportData!['nightShiftHoursWage'].toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              // Заголовок розділу надбавок
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: 8,
                                  bottom: 4,
                                ),
                                child: Text(
                                  localizations.translate('bonuses'),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),

                              // Базові надбавки
                              _buildReportRow(
                                localizations.translate(
                                  'report_saturday_bonus',
                                ),
                                '${(_reportData!['saturdayBonus'] * 100).toStringAsFixed(0)}% - ${(_reportData!['saturdayHoursWage'] * _reportData!['saturdayBonus']).toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              _buildReportRow(
                                localizations.translate('report_sunday_bonus'),
                                '${(_reportData!['sundayBonus'] * 100).toStringAsFixed(0)}% - ${(_reportData!['sundayHoursWage'] * _reportData!['sundayBonus']).toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              _buildReportRow(
                                localizations.translate('report_holiday_bonus'),
                                '${(_reportData!['holidayBonus'] * 100).toStringAsFixed(0)}% - ${(_reportData!['holidayHoursWage'] * _reportData!['holidayBonus']).toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              _buildReportRow(
                                localizations.translate(
                                  'report_night_shift_bonus',
                                ),
                                '${(_reportData!['nightShiftBonus'] * 100).toStringAsFixed(0)}% - ${(_reportData!['nightShiftHoursWage'] * _reportData!['nightShiftBonus']).toStringAsFixed(2)} ${_currencySymbol}',
                              ),

                              // Додаткові надбавки
                              if (_reportData!['hazardousHoursWage'] > 0)
                                _buildReportRow(
                                  localizations.translate(
                                    'report_hazardous_bonus',
                                  ),
                                  '${(_reportData!['hazardousBonus'] * 100).toStringAsFixed(0)}% - ${_reportData!['hazardousHoursWage'].toStringAsFixed(2)} ${_currencySymbol}',
                                ),

                              if (_reportData!['overtimeHoursWage'] > 0)
                                _buildReportRow(
                                  localizations.translate(
                                    'report_overtime_bonus',
                                  ),
                                  '${(_reportData!['overtimeBonus'] * 100).toStringAsFixed(0)}% - ${_reportData!['overtimeHoursWage'].toStringAsFixed(2)} ${_currencySymbol}',
                                ),

                              // Надбавки за стаж та кваліфікацію
                              if (_reportData!['experienceBonusWage'] > 0)
                                _buildReportRow(
                                  localizations.translate(
                                    'report_experience_bonus',
                                  ),
                                  '${_reportData!['employeeExperienceYears']} років × ${(_reportData!['experienceBonus'] * 100).toStringAsFixed(0)}% - ${_reportData!['experienceBonusWage'].toStringAsFixed(2)} ${_currencySymbol}',
                                ),

                              if (_reportData!['qualificationBonusWage'] > 0)
                                _buildReportRow(
                                  localizations.translate(
                                    'report_qualification_bonus',
                                  ),
                                  'Рівень ${_reportData!['employeeQualificationLevel']} × ${(_reportData!['qualificationBonus'] * 100).toStringAsFixed(0)}% - ${_reportData!['qualificationBonusWage'].toStringAsFixed(2)} ${_currencySymbol}',
                                ),

                              // Персональні надбавки
                              if (_reportData!['personalBonusesWage'] > 0)
                                _buildReportRow(
                                  localizations.translate(
                                    'report_personal_bonuses',
                                  ),
                                  '${_reportData!['personalBonusesWage'].toStringAsFixed(2)} ${_currencySymbol}',
                                ),

                              const Divider(),

                              // Загальна сума
                              _buildReportRow(
                                localizations.translate('total_wage'),
                                '${_reportData!['totalWage'].toStringAsFixed(2)} ${_currencySymbol}',
                                isBold: true,
                              ),

                              const SizedBox(height: 16),

                              // Кнопки експорту
                              Wrap(
                                spacing: 16,
                                runSpacing: 16,
                                alignment: WrapAlignment.center,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: () => _exportPdf(localizations),
                                    icon: const Icon(Icons.picture_as_pdf),
                                    label: Text(
                                      localizations.translate('export_pdf'),
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    onPressed:
                                        () => _previewReport(localizations),
                                    icon: const Icon(Icons.preview),
                                    label: Text(
                                      localizations.translate('preview'),
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    onPressed:
                                        () => _printReport(localizations),
                                    icon: const Icon(Icons.print),
                                    label: Text(
                                      localizations.translate('print'),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      )
                    else
                      // Повідомлення про відсутність даних
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Center(
                            child: Text(
                              'Немає даних для відображення за вибраний період',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
    );
  }

  /// Створення рядка звіту
  Widget _buildReportRow(String label, String value, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// Експорт звіту в PDF
  Future<void> _exportPdf(AppLocalizations localizations) async {
    if (_reportData == null) return;

    // Перевірка та ініціалізація PdfService
    _pdfService ??= Provider.of<PdfService>(context, listen: false);

    try {
      setState(() {
        _isLoading = true;
      });

      // Отримання поточного працівника
      final appState = Provider.of<AppState>(context, listen: false);
      final employee = appState.currentEmployee;
      if (employee == null) {
        setState(() {
          _errorMessage = 'Працівника не знайдено';
          _isLoading = false;
        });
        return;
      }

      // Визначення періоду
      final DateTime startDate =
          _timesheets.isNotEmpty
              ? _timesheets
                  .map((t) => t.startDate)
                  .reduce((a, b) => a.isBefore(b) ? a : b)
              : DateTime.now();

      final DateTime endDate =
          _timesheets.isNotEmpty
              ? _timesheets
                  .map((t) => t.endDate)
                  .reduce((a, b) => a.isAfter(b) ? a : b)
              : DateTime.now();

      // Генерація PDF
      final File pdfFile = await _pdfService!.generateTimesheetReport(
        reportData: _reportData!,
        employee: employee,
        startDate: startDate,
        endDate: endDate,
        country: _country,
        localizations: localizations,
      );

      // Відкриття PDF
      await _pdfService!.openPdf(pdfFile);

      // Показ повідомлення про успішний експорт
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.translate('export_success')),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка експорту: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Попередній перегляд звіту
  Future<void> _previewReport(AppLocalizations localizations) async {
    if (_reportData == null) return;

    // Перевірка та ініціалізація PdfService
    _pdfService ??= Provider.of<PdfService>(context, listen: false);

    try {
      setState(() {
        _isLoading = true;
      });

      // Отримання поточного працівника
      final appState = Provider.of<AppState>(context, listen: false);
      final employee = appState.currentEmployee;
      if (employee == null) {
        setState(() {
          _errorMessage = 'Працівника не знайдено';
          _isLoading = false;
        });
        return;
      }

      // Визначення періоду
      final DateTime startDate =
          _timesheets.isNotEmpty
              ? _timesheets
                  .map((t) => t.startDate)
                  .reduce((a, b) => a.isBefore(b) ? a : b)
              : DateTime.now();

      final DateTime endDate =
          _timesheets.isNotEmpty
              ? _timesheets
                  .map((t) => t.endDate)
                  .reduce((a, b) => a.isAfter(b) ? a : b)
              : DateTime.now();

      // Генерація PDF
      final File pdfFile = await _pdfService!.generateTimesheetReport(
        reportData: _reportData!,
        employee: employee,
        startDate: startDate,
        endDate: endDate,
        country: _country,
        localizations: localizations,
      );

      // Попередній перегляд PDF
      await _pdfService!.previewPdf(pdfFile);
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка перегляду: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Друк звіту
  Future<void> _printReport(AppLocalizations localizations) async {
    if (_reportData == null) return;

    // Перевірка та ініціалізація PdfService
    _pdfService ??= Provider.of<PdfService>(context, listen: false);

    try {
      setState(() {
        _isLoading = true;
      });

      // Отримання поточного працівника
      final appState = Provider.of<AppState>(context, listen: false);
      final employee = appState.currentEmployee;
      if (employee == null) {
        setState(() {
          _errorMessage = 'Працівника не знайдено';
          _isLoading = false;
        });
        return;
      }

      // Визначення періоду
      final DateTime startDate =
          _timesheets.isNotEmpty
              ? _timesheets
                  .map((t) => t.startDate)
                  .reduce((a, b) => a.isBefore(b) ? a : b)
              : DateTime.now();

      final DateTime endDate =
          _timesheets.isNotEmpty
              ? _timesheets
                  .map((t) => t.endDate)
                  .reduce((a, b) => a.isAfter(b) ? a : b)
              : DateTime.now();

      // Генерація PDF
      final File pdfFile = await _pdfService!.generateTimesheetReport(
        reportData: _reportData!,
        employee: employee,
        startDate: startDate,
        endDate: endDate,
        country: _country,
        localizations: localizations,
      );

      // Друк PDF
      await _pdfService!.printPdf(pdfFile);
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка друку: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
