import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';
import 'shifts/shifts.dart';

/// Вкладка табеля
class TimesheetTab extends StatefulWidget {
  const TimesheetTab({super.key});

  @override
  State<TimesheetTab> createState() => _TimesheetTabState();
}

class _TimesheetTabState extends State<TimesheetTab> {
  // Контролери для календаря
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;

  // Контролер для введення годин
  final TextEditingController _hoursController = TextEditingController();

  // FocusNode для поля введення годин
  final FocusNode _hoursFocusNode = FocusNode();

  // Тип зміни
  ShiftType _selectedShiftType = ShiftType.day;

  // Коментар
  final TextEditingController _commentController = TextEditingController();

  // Сервіси
  late DatabaseService _databaseService;
  late CalendarService _calendarService;
  late ShiftScheduleService _shiftScheduleService;

  // Дані
  Map<DateTime, List<WorkDay>> _workDays = {};
  DayType _selectedDayType = DayType.regular;
  bool _isLoading = true;
  String? _errorMessage;
  String _countryCode = 'UA'; // Код країни за замовчуванням

  // Дані графіку змін
  ShiftSchedule? _employeeShiftSchedule;
  Map<String, Shift> _shiftsMap = {};
  Map<DateTime, Shift?> _shiftsForDays = {};

  @override
  void initState() {
    super.initState();
    _calendarFormat = CalendarFormat.month;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();

    // Ініціалізація сервісів
    _databaseService = Provider.of<DatabaseService>(context, listen: false);
    _calendarService = Provider.of<CalendarService>(context, listen: false);
    _shiftScheduleService = ShiftScheduleService();

    // Завантаження даних
    _loadData();
  }

  @override
  void dispose() {
    _hoursController.dispose();
    _commentController.dispose();
    _hoursFocusNode.dispose();
    super.dispose();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Отримання поточного працівника
      final employee =
          Provider.of<AppState>(context, listen: false).currentEmployee;
      if (employee == null) {
        setState(() {
          _errorMessage = 'Працівника не знайдено';
        });
        return;
      }

      // Отримання першого та останнього дня місяця
      final firstDay = AppDateUtils.getFirstDayOfMonth(_focusedDay);
      final lastDay = AppDateUtils.getLastDayOfMonth(_focusedDay);

      // Отримання робочих днів за період
      final workDays = await _databaseService.getWorkDaysByEmployeeAndPeriod(
        employee.id,
        firstDay,
        lastDay,
      );

      // Групування робочих днів за датою
      final Map<DateTime, List<WorkDay>> workDaysMap = {};
      for (final workDay in workDays) {
        final date = DateTime(
          workDay.date.year,
          workDay.date.month,
          workDay.date.day,
        );

        if (workDaysMap[date] == null) {
          workDaysMap[date] = [];
        }

        workDaysMap[date]!.add(workDay);
      }

      // Визначення типу дня для вибраної дати
      String countryCode;

      // Перевірка, чи компонент все ще змонтований
      if (!mounted) return;

      // Отримуємо код країни з AppState
      final appState = Provider.of<AppState>(context, listen: false);
      countryCode = appState.country.code;

      // Зберігаємо код країни для використання в інших методах
      _countryCode = countryCode;

      // Завантаження графіку змін працівника
      _employeeShiftSchedule = await _shiftScheduleService
          .getEmployeeShiftSchedule(employee.id);

      // Якщо є графік змін, завантажуємо зміни та генеруємо зміни для днів
      if (_employeeShiftSchedule != null) {
        // Завантаження всіх змін
        final shifts = await _shiftScheduleService.getAllShifts();
        _shiftsMap = {for (var shift in shifts) shift.id: shift};

        // Генерація змін для днів поточного місяця
        _generateShiftsForDays(firstDay, lastDay);
      }

      final selectedDayType = await _calendarService.getDayType(
        _selectedDay,
        countryCode,
      );

      setState(() {
        _workDays = workDaysMap;
        _selectedDayType = selectedDayType;

        // Заповнення полів для вибраного дня
        _fillFieldsForSelectedDay();
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка завантаження даних: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Заповнення полів для вибраного дня
  void _fillFieldsForSelectedDay() {
    final date = DateTime(
      _selectedDay.year,
      _selectedDay.month,
      _selectedDay.day,
    );

    if (_workDays[date] != null && _workDays[date]!.isNotEmpty) {
      // Якщо є робочий день, заповнюємо поля його даними
      final workDay = _workDays[date]!.first;
      _hoursController.text = workDay.hoursWorked.toString();
      _selectedShiftType = workDay.shiftType;
      _selectedDayType = workDay.dayType; // Додаємо оновлення типу дня
      _commentController.text = workDay.comment ?? '';
    } else if (_shiftsForDays.containsKey(date) &&
        _shiftsForDays[date] != null) {
      // Якщо є зміна з графіку змін, але немає робочого дня
      final shift = _shiftsForDays[date]!;

      // Автоматично заповнюємо поля на основі зміни
      _hoursController.text = shift.durationInHours.toString();
      _selectedShiftType = shift.isNightShift ? ShiftType.night : ShiftType.day;
      _commentController.clear();
    } else {
      // Якщо немає ні робочого дня, ні зміни
      _hoursController.clear();
      _selectedShiftType = ShiftType.day;
      _commentController.clear();
      // Тип дня вже встановлено в _loadData
    }
  }

  /// Збереження робочого дня
  Future<void> _saveWorkDay() async {
    // Валідація введених годин
    if (_hoursController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Введіть кількість годин';
      });
      return;
    }

    final hours = double.tryParse(_hoursController.text);
    if (hours == null || hours <= 0 || hours > 24) {
      setState(() {
        _errorMessage = 'Некоректна кількість годин';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Отримання поточного працівника
      final employee =
          Provider.of<AppState>(context, listen: false).currentEmployee;
      if (employee == null) {
        setState(() {
          _errorMessage = 'Працівника не знайдено';
        });
        return;
      }

      // Створення або оновлення робочого дня
      final date = DateTime(
        _selectedDay.year,
        _selectedDay.month,
        _selectedDay.day,
      );

      WorkDay? existingWorkDay;
      if (_workDays[date] != null && _workDays[date]!.isNotEmpty) {
        existingWorkDay = _workDays[date]!.first;
      }

      // Отримання інформації про зміну з графіку змін
      String? shiftId;
      if (_shiftsForDays.containsKey(date) && _shiftsForDays[date] != null) {
        shiftId = _shiftsForDays[date]!.id;
      }

      // Якщо вже є робочий день з ідентифікатором зміни, зберігаємо його
      if (existingWorkDay != null && existingWorkDay.shiftId != null) {
        shiftId = existingWorkDay.shiftId;
      }

      if (existingWorkDay != null) {
        // Оновлення існуючого робочого дня
        final updatedWorkDay = existingWorkDay.copyWith(
          hoursWorked: hours,
          dayType: _selectedDayType, // Додаємо оновлення типу дня
          shiftType: _selectedShiftType,
          comment:
              _commentController.text.isEmpty ? null : _commentController.text,
          shiftId: shiftId, // Додаємо ідентифікатор зміни
        );

        await _databaseService.updateWorkDay(updatedWorkDay);
      } else {
        // Створення нового робочого дня
        final newWorkDay = WorkDay(
          employeeId: employee.id,
          date: date,
          hoursWorked: hours,
          dayType: _selectedDayType,
          shiftType: _selectedShiftType,
          comment:
              _commentController.text.isEmpty ? null : _commentController.text,
          shiftId: shiftId, // Додаємо ідентифікатор зміни
        );

        await _databaseService.insertWorkDay(newWorkDay);
      }

      // Оновлення даних
      await _loadData();

      // Показ повідомлення про успішне збереження
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Робочий день збережено'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка збереження даних: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Видалення робочого дня
  Future<void> _deleteWorkDay() async {
    final localizations = AppLocalizations.of(context);
    final date = DateTime(
      _selectedDay.year,
      _selectedDay.month,
      _selectedDay.day,
    );

    if (_workDays[date] == null || _workDays[date]!.isEmpty) {
      setState(() {
        _errorMessage = localizations.translate('no_data_to_delete');
      });
      return;
    }

    // Підтвердження видалення
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(localizations.translate('confirmation')),
            content: Text(localizations.translate('delete_work_day_confirm')),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(dialogContext, false),
                child: Text(localizations.translate('cancel')),
              ),
              TextButton(
                onPressed: () => Navigator.pop(dialogContext, true),
                child: Text(localizations.translate('delete')),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Видалення робочого дня
      final workDay = _workDays[date]!.first;
      await _databaseService.deleteWorkDay(workDay.id);

      // Оновлення даних
      await _loadData();

      // Показ повідомлення про успішне видалення
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.translate('work_day_deleted')),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _errorMessage =
            '${localizations.translate('error_deleting_data')}: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Отримання кольору для дня календаря
  Color _getDayColor(DateTime day) {
    // Перетворення дати до формату без часу
    final date = DateTime(day.year, day.month, day.day);

    // Перевірка, чи є дані для цього дня
    if (_workDays[date] != null && _workDays[date]!.isNotEmpty) {
      final workDay = _workDays[date]!.first;

      // Колір залежно від типу дня
      switch (workDay.dayType) {
        case DayType.regular:
          return Colors.blue.withAlpha(76);
        case DayType.saturday:
          return Colors.orange.withAlpha(76);
        case DayType.sunday:
          return Colors.red.withAlpha(76);
        case DayType.holiday:
          return Colors.purple.withAlpha(76);
      }
    }

    // Перевірка на наявність зміни з графіку змін
    if (_shiftsForDays.containsKey(date) && _shiftsForDays[date] != null) {
      final shift = _shiftsForDays[date]!;
      // Використовуємо колір зміни з меншою непрозорістю
      return shift.color.withAlpha(50);
    }

    // Перевірка на вихідні дні з графіку змін
    if (_employeeShiftSchedule != null &&
        _employeeShiftSchedule!.isDayOff(date)) {
      return Colors.grey.withAlpha(25);
    }

    // Перевірка на святкові дні за допомогою синхронного методу
    String countryCode = _countryCode; // Використовуємо збережений код країни
    bool isHoliday = _isHoliday(day, countryCode);
    if (isHoliday) {
      return Colors.purple.withAlpha(25);
    }

    // Колір для вихідних днів
    if (day.weekday == DateTime.saturday) {
      return Colors.orange.withAlpha(25);
    } else if (day.weekday == DateTime.sunday) {
      return Colors.red.withAlpha(25);
    }

    return Colors.transparent;
  }

  /// Генерація змін для днів вказаного періоду
  void _generateShiftsForDays(DateTime startDate, DateTime endDate) {
    if (_employeeShiftSchedule == null) return;

    // Очищення попередніх даних
    _shiftsForDays.clear();

    // Для кожного дня в періоді
    for (
      DateTime date = startDate;
      date.isBefore(endDate.add(const Duration(days: 1)));
      date = date.add(const Duration(days: 1))
    ) {
      // Перетворення дати до формату без часу
      final normalizedDate = DateTime(date.year, date.month, date.day);

      // Перевірка, чи є день вихідним
      if (_employeeShiftSchedule!.isDayOff(normalizedDate)) {
        _shiftsForDays[normalizedDate] = null;
        continue;
      }

      // Отримання ID зміни для вказаної дати
      final shiftId = _employeeShiftSchedule!.getShiftIdForDate(normalizedDate);
      if (shiftId == null) {
        _shiftsForDays[normalizedDate] = null;
        continue;
      }

      // Отримання зміни
      final shift = _shiftsMap[shiftId];
      _shiftsForDays[normalizedDate] = shift;
    }
  }

  /// Перевірка, чи є день святковим (синхронний метод)
  bool _isHoliday(DateTime date, String countryCode) {
    // Перевірка для України
    if (countryCode == 'UA') {
      // Новий рік
      if (date.month == 1 && date.day == 1) return true;
      // Різдво Христове
      if (date.month == 1 && date.day == 7) return true;
      // Міжнародний жіночий день
      if (date.month == 3 && date.day == 8) return true;
      // День праці
      if (date.month == 5 && date.day == 1) return true;
      // День перемоги
      if (date.month == 5 && date.day == 9) return true;
      // День Конституції
      if (date.month == 6 && date.day == 28) return true;
      // День Незалежності
      if (date.month == 8 && date.day == 24) return true;
      // День захисників і захисниць
      if (date.month == 10 && date.day == 14) return true;
      // Різдво Христове (за григоріанським календарем)
      if (date.month == 12 && date.day == 25) return true;
    }
    // Перевірка для Чехії
    else if (countryCode == 'CZ') {
      // Новий рік
      if (date.month == 1 && date.day == 1) return true;
      // День праці
      if (date.month == 5 && date.day == 1) return true;
      // День перемоги
      if (date.month == 5 && date.day == 8) return true;
      // День слов'янських вірозвістів
      if (date.month == 7 && date.day == 5) return true;
      // День спалення Яна Гуса
      if (date.month == 7 && date.day == 6) return true;
      // День чеської державності
      if (date.month == 9 && date.day == 28) return true;
      // День утворення Чехословаччини
      if (date.month == 10 && date.day == 28) return true;
      // День боротьби за свободу
      if (date.month == 11 && date.day == 17) return true;
      // Різдвяні свята
      if (date.month == 12 &&
          (date.day == 24 || date.day == 25 || date.day == 26)) {
        return true;
      }
    }
    // Перевірка для Словаччини
    else if (countryCode == 'SK') {
      // Новий рік
      if (date.month == 1 && date.day == 1) return true;
      // Три королі
      if (date.month == 1 && date.day == 6) return true;
      // День праці
      if (date.month == 5 && date.day == 1) return true;
      // День перемоги
      if (date.month == 5 && date.day == 8) return true;
      // День святих Кирила і Мефодія
      if (date.month == 7 && date.day == 5) return true;
      // День Словацького національного повстання
      if (date.month == 8 && date.day == 29) return true;
      // День Конституції
      if (date.month == 9 && date.day == 1) return true;
      // День Богородиці
      if (date.month == 9 && date.day == 15) return true;
      // День всіх святих
      if (date.month == 11 && date.day == 1) return true;
      // День боротьби за свободу
      if (date.month == 11 && date.day == 17) return true;
      // Різдвяні свята
      if (date.month == 12 &&
          (date.day == 24 || date.day == 25 || date.day == 26)) {
        return true;
      }
    }
    // Перевірка для Польщі
    else if (countryCode == 'PL') {
      // Новий рік
      if (date.month == 1 && date.day == 1) return true;
      // Три королі
      if (date.month == 1 && date.day == 6) return true;
      // День праці
      if (date.month == 5 && date.day == 1) return true;
      // День Конституції
      if (date.month == 5 && date.day == 3) return true;
      // Вознесіння Богородиці
      if (date.month == 8 && date.day == 15) return true;
      // День всіх святих
      if (date.month == 11 && date.day == 1) return true;
      // День незалежності
      if (date.month == 11 && date.day == 11) return true;
      // Різдвяні свята
      if (date.month == 12 &&
          (date.day == 24 || date.day == 25 || date.day == 26)) {
        return true;
      }
    }

    return false;
  }

  /// Отримання маркерів для дня календаря
  List<Widget> _getDayMarkers(DateTime day) {
    // Перетворення дати до формату без часу
    final date = DateTime(day.year, day.month, day.day);
    final localizations = AppLocalizations.of(context);
    final List<Widget> markers = [];

    // Перевірка на наявність зміни з графіку змін
    Shift? shift;
    if (_shiftsForDays.containsKey(date) && _shiftsForDays[date] != null) {
      shift = _shiftsForDays[date]!;
    }

    // Перевірка на наявність робочого дня
    if (_workDays[date] != null && _workDays[date]!.isNotEmpty) {
      final workDay = _workDays[date]!.first;

      // Отримання зміни з робочого дня, якщо вона є
      if (workDay.shiftId != null && _shiftsMap.containsKey(workDay.shiftId)) {
        shift = _shiftsMap[workDay.shiftId];
      }

      // Маркер з кількістю годин
      markers.add(
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
          decoration: BoxDecoration(
            color:
                shift?.color ??
                (workDay.shiftType == ShiftType.day
                    ? Colors.yellow
                    : Colors.indigo),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${workDay.hoursWorked}${localizations.translate('hours_short')}',
            style: TextStyle(
              fontSize: 8,
              fontWeight: FontWeight.bold,
              color:
                  (shift?.color ??
                                  (workDay.shiftType == ShiftType.day
                                      ? Colors.yellow
                                      : Colors.indigo))
                              .computeLuminance() >
                          0.5
                      ? Colors.black
                      : Colors.white,
            ),
          ),
        ),
      );
    } else if (shift != null) {
      // Якщо є зміна з графіку змін, але немає робочого дня
      // Маркер зміни з скороченою назвою
      String shiftLabel = '';

      // Створюємо скорочену назву зміни (перші 1-2 символи)
      if (shift.name.isNotEmpty) {
        // Якщо назва містить цифру, використовуємо її
        final digitMatch = RegExp(r'\d+').firstMatch(shift.name);
        if (digitMatch != null) {
          shiftLabel = digitMatch.group(0)!;
        } else {
          // Інакше використовуємо перші символи
          shiftLabel =
              shift.name.length > 2
                  ? shift.name.substring(0, 2).toUpperCase()
                  : shift.name.toUpperCase();
        }
      } else {
        // Якщо назви немає, використовуємо старий спосіб
        shiftLabel = shift.isNightShift ? 'Н' : 'Д';
      }

      markers.add(
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
          decoration: BoxDecoration(
            color: shift.color,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            shiftLabel,
            style: TextStyle(
              fontSize: 7,
              fontWeight: FontWeight.bold,
              color:
                  shift.color.computeLuminance() > 0.5
                      ? Colors.black
                      : Colors.white,
            ),
          ),
        ),
      );
    }

    return markers;
  }

  /// Створення легенди кольорів змін
  Widget _buildShiftLegend() {
    // Отримуємо унікальні зміни з _shiftsMap
    final shifts = _shiftsMap.values.toList();

    // Якщо немає змін або є тільки одна зміна, не показуємо легенду
    if (shifts.isEmpty || shifts.length <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).translate('shifts_legend'),
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children:
                shifts.map((shift) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: shift.color,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.grey.shade300,
                            width: 1,
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(shift.name, style: const TextStyle(fontSize: 12)),
                      const SizedBox(width: 12),
                    ],
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(minHeight: 600),
                  child: Column(
                    children: [
                      // Календар
                      TableCalendar(
                        firstDay: DateTime.utc(2020, 1, 1),
                        lastDay: DateTime.utc(2030, 12, 31),
                        focusedDay: _focusedDay,
                        calendarFormat: _calendarFormat,
                        locale:
                            localizations
                                .locale
                                .languageCode, // Додаємо локалізацію календаря
                        headerStyle: HeaderStyle(
                          formatButtonVisible: false,
                          titleCentered: true,
                          titleTextFormatter:
                              (date, locale) => localizations.formatDate(
                                date,
                                format: 'MMMM yyyy',
                              ),
                        ),
                        selectedDayPredicate: (day) {
                          return isSameDay(_selectedDay, day);
                        },
                        onDaySelected: (selectedDay, focusedDay) async {
                          setState(() {
                            _selectedDay = selectedDay;
                            _focusedDay = focusedDay;
                          });

                          // Визначення типу дня
                          final appState = Provider.of<AppState>(
                            context,
                            listen: false,
                          );
                          final countryCode = appState.country.code;

                          // Перевірка, чи компонент все ще змонтований
                          if (!mounted) return;

                          final dayType = await _calendarService.getDayType(
                            selectedDay,
                            countryCode,
                          );

                          setState(() {
                            _selectedDayType = dayType;
                            _fillFieldsForSelectedDay();
                          });

                          // Встановлюємо фокус на поле введення годин
                          // Додаємо невелику затримку, щоб дозволити інтерфейсу оновитися
                          Future.delayed(const Duration(milliseconds: 100), () {
                            if (mounted) {
                              _hoursFocusNode.requestFocus();
                            }
                          });
                        },
                        onFormatChanged: (format) {
                          setState(() {
                            _calendarFormat = format;
                          });
                        },
                        onPageChanged: (focusedDay) {
                          setState(() {
                            _focusedDay = focusedDay;
                          });

                          // Завантаження даних для нового місяця
                          _loadData();
                        },
                        calendarBuilders: CalendarBuilders(
                          // Стилізація дня
                          defaultBuilder: (context, day, focusedDay) {
                            return Container(
                              margin: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: _getDayColor(day),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '${day.day}',
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: _getDayMarkers(day),
                                  ),
                                ],
                              ),
                            );
                          },
                          // Стилізація вибраного дня
                          selectedBuilder: (context, day, focusedDay) {
                            return Container(
                              margin: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '${day.day}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children:
                                        _getDayMarkers(day).map((marker) {
                                          if (marker is Container) {
                                            // Змінюємо кольори для вибраного дня
                                            final decoration =
                                                marker.decoration
                                                    as BoxDecoration?;
                                            return Container(
                                              padding: marker.padding,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    decoration?.borderRadius
                                                        as BorderRadius?,
                                                border: Border.all(
                                                  color: Colors.blue,
                                                  width: 1,
                                                ),
                                              ),
                                              child:
                                                  (marker.child is Text)
                                                      ? Text(
                                                        ((marker.child as Text)
                                                                .data ??
                                                            ''),
                                                        style: const TextStyle(
                                                          fontSize: 8,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color: Colors.blue,
                                                        ),
                                                      )
                                                      : marker.child,
                                            );
                                          }
                                          return marker;
                                        }).toList(),
                                  ),
                                ],
                              ),
                            );
                          },
                          // Стилізація сьогоднішнього дня
                          todayBuilder: (context, day, focusedDay) {
                            return Container(
                              margin: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: _getDayColor(day),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.blue,
                                  width: 2,
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '${day.day}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children:
                                        _getDayMarkers(day).map((marker) {
                                          if (marker is Container) {
                                            // Змінюємо кольори для поточного дня
                                            final decoration =
                                                marker.decoration
                                                    as BoxDecoration?;
                                            return Container(
                                              padding: marker.padding,
                                              decoration: BoxDecoration(
                                                color: decoration?.color,
                                                borderRadius:
                                                    decoration?.borderRadius
                                                        as BorderRadius?,
                                                border: Border.all(
                                                  color: Colors.blue,
                                                  width: 1,
                                                ),
                                              ),
                                              child: marker.child,
                                            );
                                          }
                                          return marker;
                                        }).toList(),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),

                      // Легенда кольорів змін
                      _buildShiftLegend(),

                      // Інформація про вибраний день
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Дата
                            Text(
                              '${localizations.translate('date')}: ${localizations.formatDate(_selectedDay)}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Тип дня
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    '${localizations.translate('day_type')}: ${_getDayTypeName(_selectedDayType, localizations)}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: _getDayTypeColor(_selectedDayType),
                                    ),
                                  ),
                                ),
                                // Кнопка для зміни типу дня
                                TextButton.icon(
                                  onPressed: _changeDayType,
                                  icon: const Icon(Icons.edit),
                                  label: Text(
                                    localizations.translate('change'),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Форма для введення годин
                            Row(
                              children: [
                                // Поле для введення годин
                                Expanded(
                                  child: TextField(
                                    controller: _hoursController,
                                    focusNode: _hoursFocusNode,
                                    keyboardType: TextInputType.number,
                                    // Автоматичне виділення тексту при фокусі
                                    onTap: () {
                                      _hoursController
                                          .selection = TextSelection(
                                        baseOffset: 0,
                                        extentOffset:
                                            _hoursController.text.length,
                                      );
                                    },
                                    decoration: InputDecoration(
                                      labelText: localizations.translate(
                                        'hours',
                                      ),
                                      border: const OutlineInputBorder(),
                                      // Кнопка для очищення поля
                                      suffixIcon: IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: () {
                                          _hoursController.clear();
                                          _hoursFocusNode.requestFocus();
                                        },
                                      ),
                                    ),
                                    onChanged: (value) {
                                      // Автоматичне збереження при зміні кількості годин
                                      if (value.isNotEmpty) {
                                        // Додаємо затримку, щоб не зберігати при кожному натисканні клавіші
                                        Future.delayed(
                                          const Duration(milliseconds: 1000),
                                          () {
                                            if (mounted) {
                                              _saveWorkDay();
                                            }
                                          },
                                        );
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 16),

                                // Вибір типу зміни
                                DropdownButton<ShiftType>(
                                  value: _selectedShiftType,
                                  items:
                                      ShiftType.values.map((type) {
                                        return DropdownMenuItem<ShiftType>(
                                          value: type,
                                          child: Text(
                                            _getShiftTypeName(
                                              type,
                                              localizations,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      setState(() {
                                        _selectedShiftType = value;
                                      });
                                      // Автоматичне збереження при зміні типу зміни
                                      if (_hoursController.text.isNotEmpty) {
                                        _saveWorkDay();
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Поле для коментаря
                            TextField(
                              controller: _commentController,
                              maxLines: 2,
                              decoration: InputDecoration(
                                labelText: localizations.translate('comment'),
                                border: const OutlineInputBorder(),
                              ),
                              onChanged: (value) {
                                // Автоматичне збереження при зміні коментаря
                                if (_hoursController.text.isNotEmpty) {
                                  // Додаємо затримку, щоб не зберігати при кожному натисканні клавіші
                                  Future.delayed(
                                    const Duration(milliseconds: 1500),
                                    () {
                                      if (mounted) {
                                        _saveWorkDay();
                                      }
                                    },
                                  );
                                }
                              },
                            ),
                            const SizedBox(height: 16),

                            // Повідомлення про помилку
                            if (_errorMessage != null)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: Text(
                                  _errorMessage!,
                                  style: const TextStyle(color: Colors.red),
                                ),
                              ),

                            // Кнопки
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                // Кнопка збереження
                                ElevatedButton.icon(
                                  onPressed: _saveWorkDay,
                                  icon: const Icon(Icons.save),
                                  label: Text(localizations.translate('save')),
                                ),

                                // Кнопка видалення
                                ElevatedButton.icon(
                                  onPressed:
                                      _workDays[DateTime(
                                                    _selectedDay.year,
                                                    _selectedDay.month,
                                                    _selectedDay.day,
                                                  )] !=
                                                  null &&
                                              _workDays[DateTime(
                                                    _selectedDay.year,
                                                    _selectedDay.month,
                                                    _selectedDay.day,
                                                  )]!
                                                  .isNotEmpty
                                          ? _deleteWorkDay
                                          : null,
                                  icon: const Icon(Icons.delete),
                                  label: Text(
                                    localizations.translate('delete'),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

      // Кнопка для створення табеля
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // TODO: Реалізувати створення табеля
          showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: Text(localizations.translate('timesheet_report')),
                  content: Text(
                    localizations.translate('create_timesheet_todo'),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(localizations.translate('close')),
                    ),
                  ],
                ),
          );
        },
        icon: const Icon(Icons.add),
        label: Text(localizations.translate('timesheet_report')),
      ),
    );
  }

  /// Отримання назви типу дня
  String _getDayTypeName(DayType type, AppLocalizations localizations) {
    switch (type) {
      case DayType.regular:
        return localizations.translate('regular');
      case DayType.saturday:
        return localizations.translate('saturday');
      case DayType.sunday:
        return localizations.translate('sunday');
      case DayType.holiday:
        return localizations.translate('holiday');
    }
  }

  /// Отримання кольору типу дня
  Color _getDayTypeColor(DayType type) {
    switch (type) {
      case DayType.regular:
        return Colors.blue;
      case DayType.saturday:
        return Colors.orange;
      case DayType.sunday:
        return Colors.red;
      case DayType.holiday:
        return Colors.purple;
    }
  }

  /// Отримання назви типу зміни
  String _getShiftTypeName(ShiftType type, AppLocalizations localizations) {
    switch (type) {
      case ShiftType.day:
        return localizations.translate('day_shift');
      case ShiftType.night:
        return localizations.translate('night_shift');
    }
  }

  /// Зміна типу дня
  void _changeDayType() {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations.translate('change_day_type')),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  DayType.values.map((type) {
                    return RadioListTile<DayType>(
                      title: Text(
                        _getDayTypeName(type, localizations),
                        style: TextStyle(color: _getDayTypeColor(type)),
                      ),
                      value: type,
                      groupValue: _selectedDayType,
                      onChanged: (value) {
                        Navigator.pop(context);
                        if (value != null) {
                          setState(() {
                            _selectedDayType = value;
                          });
                        }
                      },
                    );
                  }).toList(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(localizations.translate('cancel')),
              ),
            ],
          ),
    );
  }
}
