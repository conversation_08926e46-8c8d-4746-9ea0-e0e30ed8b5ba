#!/bin/bash

# Скрипт для збірки та підписання iOS-додатку

# Перевірка наявності необхідних інструментів
if ! command -v flutter &> /dev/null; then
    echo "Flutter не знайдено. Будь ласка, встановіть Flutter та додайте його до PATH."
    exit 1
fi

if ! command -v xcodebuild &> /dev/null; then
    echo "Xcode не знайдено. Будь ласка, встановіть Xcode."
    exit 1
fi

# Параметри
APP_NAME="Tabel App"
WORKSPACE="Runner.xcworkspace"
SCHEME="Runner"
CONFIGURATION="Release"
EXPORT_OPTIONS_PLIST="ExportOptions.plist"
ARCHIVE_PATH="build/Runner.xcarchive"
EXPORT_PATH="build/ios/ipa"

# Перехід до директорії iOS
cd "$(dirname "$0")" || exit 1

# Очищення попередніх збірок
echo "Очищення попередніх збірок..."
rm -rf build
mkdir -p "$EXPORT_PATH"

# Оновлення залежностей Flutter
echo "Оновлення залежностей Flutter..."
cd .. && flutter pub get && cd ios || exit 1

# Оновлення CocoaPods
echo "Оновлення CocoaPods..."
pod install

# Збірка архіву
echo "Збірка архіву..."
xcodebuild clean archive \
    -workspace "$WORKSPACE" \
    -scheme "$SCHEME" \
    -configuration "$CONFIGURATION" \
    -archivePath "$ARCHIVE_PATH" \
    -allowProvisioningUpdates \
    -destination "generic/platform=iOS" \
    CODE_SIGN_STYLE="Automatic"

# Перевірка успішності збірки архіву
if [ $? -ne 0 ]; then
    echo "Помилка при збірці архіву."
    exit 1
fi

# Експорт IPA
echo "Експорт IPA..."
xcodebuild -exportArchive \
    -archivePath "$ARCHIVE_PATH" \
    -exportOptionsPlist "$EXPORT_OPTIONS_PLIST" \
    -exportPath "$EXPORT_PATH" \
    -allowProvisioningUpdates

# Перевірка успішності експорту
if [ $? -ne 0 ]; then
    echo "Помилка при експорті IPA."
    exit 1
fi

echo "Збірка успішно завершена!"
echo "IPA файл знаходиться в: $EXPORT_PATH"

# Відкриття директорії з IPA файлом
open "$EXPORT_PATH"
