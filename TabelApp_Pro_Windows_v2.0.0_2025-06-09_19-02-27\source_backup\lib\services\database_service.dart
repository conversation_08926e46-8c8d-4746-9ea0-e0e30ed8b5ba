import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/models.dart';

/// Сервіс для роботи з локальною базою даних
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static Database? _database;

  /// Фабричний конструктор
  factory DatabaseService() => _instance;

  /// Приватний конструктор
  DatabaseService._internal();

  /// Отримання екземпляра бази даних
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Ініціалізація бази даних
  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'timesheet.db');

    try {
      return await openDatabase(
        path,
        version: 6, // Збільшуємо версію для оновлення бази даних
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
        onOpen: _onDatabaseOpen,
      );
    } catch (e) {
      print('Error opening database: $e');
      // Якщо база даних пошкоджена, видаляємо її та створюємо заново
      await _recreateDatabase(path);
      return await openDatabase(
        path,
        version: 6,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
        onOpen: _onDatabaseOpen,
      );
    }
  }

  /// Перевірка бази даних при відкритті
  Future<void> _onDatabaseOpen(Database db) async {
    try {
      // Перевіряємо наявність всіх необхідних таблиць
      await _verifyDatabaseStructure(db);
    } catch (e) {
      print('Database structure verification failed: $e');
      // Якщо структура неправильна, пересоздаємо базу даних
      await db.close();
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, 'timesheet.db');
      await _recreateDatabase(path);
    }
  }

  /// Перевірка структури бази даних
  Future<void> _verifyDatabaseStructure(Database db) async {
    final List<String> requiredTables = [
      'employees',
      'workDays',
      'timesheets',
      'bonusSettings',
      'employeeBonuses',
      'holidays',
      'absences',
      'reminders',
      'shifts',
      'shiftSchedules',
      'employeeShiftSchedules'
    ];

    for (String tableName in requiredTables) {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName]
      );

      if (result.isEmpty) {
        throw Exception('Missing table: $tableName');
      }
    }

    // Перевіряємо наявність поля shiftId в таблиці workDays
    final workDaysInfo = await db.rawQuery("PRAGMA table_info(workDays)");
    final hasShiftId = workDaysInfo.any((column) => column['name'] == 'shiftId');

    if (!hasShiftId) {
      await db.execute('ALTER TABLE workDays ADD COLUMN shiftId TEXT');
    }
  }

  /// Пересоздання бази даних
  Future<void> _recreateDatabase(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      print('Error deleting database file: $e');
    }
  }

  /// Діагностика та відновлення бази даних
  Future<Map<String, dynamic>> diagnoseDatabaseHealth() async {
    final Map<String, dynamic> diagnosis = {
      'isHealthy': false,
      'missingTables': <String>[],
      'errors': <String>[],
      'recommendations': <String>[],
    };

    try {
      final db = await database;

      // Перевіряємо наявність всіх таблиць
      final List<String> requiredTables = [
        'employees',
        'workDays',
        'timesheets',
        'bonusSettings',
        'employeeBonuses',
        'holidays',
        'absences',
        'reminders',
        'shifts',
        'shiftSchedules',
        'employeeShiftSchedules'
      ];

      final List<String> missingTables = [];

      for (String tableName in requiredTables) {
        final result = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [tableName]
        );

        if (result.isEmpty) {
          missingTables.add(tableName);
        }
      }

      diagnosis['missingTables'] = missingTables;

      // Перевіряємо цілісність даних
      final employeeCount = await db.rawQuery('SELECT COUNT(*) as count FROM employees');
      final workDayCount = await db.rawQuery('SELECT COUNT(*) as count FROM workDays');

      diagnosis['employeeCount'] = employeeCount.first['count'];
      diagnosis['workDayCount'] = workDayCount.first['count'];

      // Перевіряємо наявність поля shiftId
      final workDaysInfo = await db.rawQuery("PRAGMA table_info(workDays)");
      final hasShiftId = workDaysInfo.any((column) => column['name'] == 'shiftId');

      if (!hasShiftId) {
        diagnosis['errors'].add('Missing shiftId column in workDays table');
        diagnosis['recommendations'].add('Add shiftId column to workDays table');
      }

      if (missingTables.isEmpty && diagnosis['errors'].isEmpty) {
        diagnosis['isHealthy'] = true;
      } else {
        if (missingTables.isNotEmpty) {
          diagnosis['recommendations'].add('Recreate missing tables: ${missingTables.join(', ')}');
        }
      }

    } catch (e) {
      diagnosis['errors'].add('Database access error: ${e.toString()}');
      diagnosis['recommendations'].add('Consider recreating the database');
    }

    return diagnosis;
  }

  /// Відновлення бази даних
  Future<bool> repairDatabase() async {
    try {
      // Закриваємо поточне з'єднання
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // Пересоздаємо базу даних
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, 'timesheet.db');
      await _recreateDatabase(path);

      // Ініціалізуємо заново
      _database = await _initDatabase();

      return true;
    } catch (e) {
      print('Error repairing database: $e');
      return false;
    }
  }

  /// Оновлення бази даних при зміні версії
  Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    if (oldVersion < 2) {
      // Додавання нових полів до таблиці employees
      await db.execute('''
        ALTER TABLE employees ADD COLUMN hireDate INTEGER;
      ''');

      await db.execute('''
        ALTER TABLE employees ADD COLUMN experienceYears INTEGER;
      ''');

      await db.execute('''
        ALTER TABLE employees ADD COLUMN qualificationLevel INTEGER;
      ''');

      // Оновлення таблиці bonusSettings
      await db.execute('''
        ALTER TABLE bonusSettings ADD COLUMN hazardousBonus REAL NOT NULL DEFAULT 0.25;
      ''');

      await db.execute('''
        ALTER TABLE bonusSettings ADD COLUMN overtimeBonus REAL NOT NULL DEFAULT 0.5;
      ''');

      await db.execute('''
        ALTER TABLE bonusSettings ADD COLUMN experienceBonus REAL NOT NULL DEFAULT 0.03;
      ''');

      await db.execute('''
        ALTER TABLE bonusSettings ADD COLUMN qualificationBonus REAL NOT NULL DEFAULT 0.05;
      ''');

      // Створення таблиці персональних надбавок
      await db.execute('''
        CREATE TABLE employeeBonuses (
          id TEXT PRIMARY KEY,
          employeeId TEXT NOT NULL,
          type INTEGER NOT NULL,
          value REAL NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          startDate INTEGER NOT NULL,
          endDate INTEGER,
          FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE
        )
      ''');
    }

    if (oldVersion < 3) {
      // Додавання поля calculationType до таблиці employeeBonuses
      try {
        await db.execute('''
          ALTER TABLE employeeBonuses ADD COLUMN calculationType INTEGER NOT NULL DEFAULT 0
        ''');
      } catch (e) {
        // Ігноруємо помилку, якщо поле вже існує
        print('Error adding calculationType column: ${e.toString()}');
      }
    }

    if (oldVersion < 4) {
      // Створення таблиці відсутностей (відпустки, лікарняні, відгули)
      await db.execute('''
        CREATE TABLE absences (
          id TEXT PRIMARY KEY,
          employeeId TEXT NOT NULL,
          type INTEGER NOT NULL,
          startDate INTEGER NOT NULL,
          endDate INTEGER NOT NULL,
          status INTEGER NOT NULL,
          comment TEXT,
          document TEXT,
          approvedById TEXT,
          approvedAt INTEGER,
          approvalComment TEXT,
          createdAt INTEGER NOT NULL,
          updatedAt INTEGER NOT NULL,
          FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE,
          FOREIGN KEY (approvedById) REFERENCES employees (id) ON DELETE SET NULL
        )
      ''');
    }

    if (oldVersion < 6) {
      // Створення таблиці змін
      await db.execute('''
        CREATE TABLE shifts (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          startTimeHour INTEGER NOT NULL,
          startTimeMinute INTEGER NOT NULL,
          endTimeHour INTEGER NOT NULL,
          endTimeMinute INTEGER NOT NULL,
          colorValue INTEGER NOT NULL,
          isNightShift INTEGER NOT NULL
        )
      ''');

      // Створення таблиці графіків змін
      await db.execute('''
        CREATE TABLE shiftSchedules (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          shiftOrder TEXT NOT NULL,
          currentShiftIndex INTEGER NOT NULL,
          startDate INTEGER NOT NULL,
          dayOff1 INTEGER NOT NULL,
          dayOff2 INTEGER,
          hasFloatingDaysOff INTEGER NOT NULL,
          workDaysCount INTEGER NOT NULL DEFAULT 5,
          daysOffCount INTEGER NOT NULL DEFAULT 2
        )
      ''');

      // Створення таблиці для зв'язку працівників з графіками змін
      await db.execute('''
        CREATE TABLE employeeShiftSchedules (
          id TEXT PRIMARY KEY,
          employeeId TEXT NOT NULL,
          shiftScheduleId TEXT NOT NULL,
          FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE,
          FOREIGN KEY (shiftScheduleId) REFERENCES shiftSchedules (id) ON DELETE CASCADE
        )
      ''');

      // Додавання поля shiftId до таблиці workDays
      try {
        await db.execute('''
          ALTER TABLE workDays ADD COLUMN shiftId TEXT
        ''');
      } catch (e) {
        // Ігноруємо помилку, якщо поле вже існує
        print('Error adding shiftId column: ${e.toString()}');
      }
    }
  }

  /// Створення таблиць бази даних
  Future<void> _createDatabase(Database db, int version) async {
    // Таблиця працівників
    await db.execute('''
      CREATE TABLE employees (
        id TEXT PRIMARY KEY,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        hourlyRate REAL NOT NULL,
        position TEXT NOT NULL,
        email TEXT NOT NULL,
        isAdmin INTEGER NOT NULL,
        hireDate INTEGER,
        experienceYears INTEGER,
        qualificationLevel INTEGER
      )
    ''');

    // Таблиця робочих днів
    await db.execute('''
      CREATE TABLE workDays (
        id TEXT PRIMARY KEY,
        employeeId TEXT NOT NULL,
        date INTEGER NOT NULL,
        hoursWorked REAL NOT NULL,
        dayType INTEGER NOT NULL,
        shiftType INTEGER NOT NULL,
        comment TEXT,
        shiftId TEXT,
        FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE
      )
    ''');

    // Таблиця табелів
    await db.execute('''
      CREATE TABLE timesheets (
        id TEXT PRIMARY KEY,
        employeeId TEXT NOT NULL,
        startDate INTEGER NOT NULL,
        endDate INTEGER NOT NULL,
        status INTEGER NOT NULL,
        comment TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE
      )
    ''');

    // Таблиця налаштувань надбавок
    await db.execute('''
      CREATE TABLE bonusSettings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nightShiftBonus REAL NOT NULL,
        saturdayBonus REAL NOT NULL,
        sundayBonus REAL NOT NULL,
        holidayBonus REAL NOT NULL,
        hazardousBonus REAL NOT NULL,
        overtimeBonus REAL NOT NULL,
        experienceBonus REAL NOT NULL,
        qualificationBonus REAL NOT NULL
      )
    ''');

    // Таблиця персональних надбавок працівників
    await db.execute('''
      CREATE TABLE employeeBonuses (
        id TEXT PRIMARY KEY,
        employeeId TEXT NOT NULL,
        type INTEGER NOT NULL,
        value REAL NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        startDate INTEGER NOT NULL,
        endDate INTEGER,
        calculationType INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE
      )
    ''');

    // Таблиця святкових днів
    await db.execute('''
      CREATE TABLE holidays (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        date INTEGER NOT NULL,
        country TEXT NOT NULL,
        isRecurring INTEGER NOT NULL
      )
    ''');

    // Таблиця відсутностей (відпустки, лікарняні, відгули)
    await db.execute('''
      CREATE TABLE absences (
        id TEXT PRIMARY KEY,
        employeeId TEXT NOT NULL,
        type INTEGER NOT NULL,
        startDate INTEGER NOT NULL,
        endDate INTEGER NOT NULL,
        status INTEGER NOT NULL,
        comment TEXT,
        document TEXT,
        approvedById TEXT,
        approvedAt INTEGER,
        approvalComment TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE,
        FOREIGN KEY (approvedById) REFERENCES employees (id) ON DELETE SET NULL
      )
    ''');

    // Таблиця нагадувань
    await db.execute('''
      CREATE TABLE reminders (
        id TEXT PRIMARY KEY,
        employeeId TEXT NOT NULL,
        type INTEGER NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        scheduledFor INTEGER NOT NULL,
        relatedDate INTEGER NOT NULL,
        status INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE
      )
    ''');

    // Таблиця змін
    await db.execute('''
      CREATE TABLE shifts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        startTimeHour INTEGER NOT NULL,
        startTimeMinute INTEGER NOT NULL,
        endTimeHour INTEGER NOT NULL,
        endTimeMinute INTEGER NOT NULL,
        colorValue INTEGER NOT NULL,
        isNightShift INTEGER NOT NULL
      )
    ''');

    // Таблиця графіків змін
    await db.execute('''
      CREATE TABLE shiftSchedules (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        shiftOrder TEXT NOT NULL,
        currentShiftIndex INTEGER NOT NULL,
        startDate INTEGER NOT NULL,
        dayOff1 INTEGER NOT NULL,
        dayOff2 INTEGER,
        hasFloatingDaysOff INTEGER NOT NULL,
        workDaysCount INTEGER NOT NULL DEFAULT 5,
        daysOffCount INTEGER NOT NULL DEFAULT 2
      )
    ''');

    // Таблиця для зв'язку працівників з графіками змін
    await db.execute('''
      CREATE TABLE employeeShiftSchedules (
        id TEXT PRIMARY KEY,
        employeeId TEXT NOT NULL,
        shiftScheduleId TEXT NOT NULL,
        FOREIGN KEY (employeeId) REFERENCES employees (id) ON DELETE CASCADE,
        FOREIGN KEY (shiftScheduleId) REFERENCES shiftSchedules (id) ON DELETE CASCADE
      )
    ''');

    // Вставка значень за замовчуванням для налаштувань надбавок
    await db.insert('bonusSettings', const BonusSettings().toMap());
  }

  // CRUD операції для працівників

  /// Додавання нового працівника
  Future<void> insertEmployee(Employee employee) async {
    final db = await database;
    await db.insert(
      'employees',
      employee.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення інформації про працівника
  Future<void> updateEmployee(Employee employee) async {
    final db = await database;
    await db.update(
      'employees',
      employee.toMap(),
      where: 'id = ?',
      whereArgs: [employee.id],
    );
  }

  /// Видалення працівника
  Future<void> deleteEmployee(String id) async {
    final db = await database;
    await db.delete('employees', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання працівника за ідентифікатором
  Future<Employee?> getEmployee(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employees',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return Employee.fromMap(maps.first);
  }

  /// Отримання працівника за email
  Future<Employee?> getEmployeeByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employees',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isEmpty) return null;
    return Employee.fromMap(maps.first);
  }

  /// Отримання всіх працівників
  Future<List<Employee>> getAllEmployees() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('employees');
    return List.generate(maps.length, (i) => Employee.fromMap(maps[i]));
  }

  // CRUD операції для робочих днів

  /// Додавання нового робочого дня
  Future<void> insertWorkDay(WorkDay workDay) async {
    final db = await database;
    await db.insert(
      'workDays',
      workDay.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення інформації про робочий день
  Future<void> updateWorkDay(WorkDay workDay) async {
    final db = await database;
    await db.update(
      'workDays',
      workDay.toMap(),
      where: 'id = ?',
      whereArgs: [workDay.id],
    );
  }

  /// Видалення робочого дня
  Future<void> deleteWorkDay(String id) async {
    final db = await database;
    await db.delete('workDays', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання робочого дня за ідентифікатором
  Future<WorkDay?> getWorkDay(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workDays',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return WorkDay.fromMap(maps.first);
  }

  /// Отримання робочих днів за ідентифікатором працівника та періодом
  Future<List<WorkDay>> getWorkDaysByEmployeeAndPeriod(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workDays',
      where: 'employeeId = ? AND date >= ? AND date <= ?',
      whereArgs: [
        employeeId,
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
    );
    return List.generate(maps.length, (i) => WorkDay.fromMap(maps[i]));
  }

  // CRUD операції для табелів

  /// Додавання нового табеля
  Future<void> insertTimesheet(Timesheet timesheet) async {
    final db = await database;
    await db.transaction((txn) async {
      // Вставка табеля
      await txn.insert(
        'timesheets',
        timesheet.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Вставка робочих днів
      for (var workDay in timesheet.workDays) {
        await txn.insert(
          'workDays',
          workDay.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  /// Оновлення інформації про табель
  Future<void> updateTimesheet(Timesheet timesheet) async {
    final db = await database;
    await db.transaction((txn) async {
      // Оновлення табеля
      await txn.update(
        'timesheets',
        timesheet.toMap(),
        where: 'id = ?',
        whereArgs: [timesheet.id],
      );

      // Видалення старих робочих днів
      await txn.delete(
        'workDays',
        where: 'employeeId = ? AND date >= ? AND date <= ?',
        whereArgs: [
          timesheet.employeeId,
          timesheet.startDate.millisecondsSinceEpoch,
          timesheet.endDate.millisecondsSinceEpoch,
        ],
      );

      // Вставка нових робочих днів
      for (var workDay in timesheet.workDays) {
        await txn.insert(
          'workDays',
          workDay.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  /// Видалення табеля
  Future<void> deleteTimesheet(String id) async {
    final db = await database;
    await db.transaction((txn) async {
      // Отримання табеля
      final List<Map<String, dynamic>> maps = await txn.query(
        'timesheets',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isEmpty) return;

      final Map<String, dynamic> timesheetMap = maps.first;
      final String employeeId = timesheetMap['employeeId'];
      final DateTime startDate = DateTime.fromMillisecondsSinceEpoch(
        timesheetMap['startDate'],
      );
      final DateTime endDate = DateTime.fromMillisecondsSinceEpoch(
        timesheetMap['endDate'],
      );

      // Видалення робочих днів
      await txn.delete(
        'workDays',
        where: 'employeeId = ? AND date >= ? AND date <= ?',
        whereArgs: [
          employeeId,
          startDate.millisecondsSinceEpoch,
          endDate.millisecondsSinceEpoch,
        ],
      );

      // Видалення табеля
      await txn.delete('timesheets', where: 'id = ?', whereArgs: [id]);
    });
  }

  /// Отримання табеля за ідентифікатором
  Future<Timesheet?> getTimesheet(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'timesheets',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final Map<String, dynamic> timesheetMap = maps.first;
    final String employeeId = timesheetMap['employeeId'];
    final DateTime startDate = DateTime.fromMillisecondsSinceEpoch(
      timesheetMap['startDate'],
    );
    final DateTime endDate = DateTime.fromMillisecondsSinceEpoch(
      timesheetMap['endDate'],
    );

    // Отримання робочих днів
    final List<WorkDay> workDays = await getWorkDaysByEmployeeAndPeriod(
      employeeId,
      startDate,
      endDate,
    );

    return Timesheet.fromMap(timesheetMap, workDays);
  }

  /// Отримання табелів за ідентифікатором працівника
  Future<List<Timesheet>> getTimesheetsByEmployee(String employeeId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'timesheets',
      where: 'employeeId = ?',
      whereArgs: [employeeId],
    );

    List<Timesheet> timesheets = [];
    for (var map in maps) {
      final DateTime startDate = DateTime.fromMillisecondsSinceEpoch(
        map['startDate'],
      );
      final DateTime endDate = DateTime.fromMillisecondsSinceEpoch(
        map['endDate'],
      );

      // Отримання робочих днів
      final List<WorkDay> workDays = await getWorkDaysByEmployeeAndPeriod(
        employeeId,
        startDate,
        endDate,
      );

      timesheets.add(Timesheet.fromMap(map, workDays));
    }

    return timesheets;
  }

  // CRUD операції для налаштувань надбавок

  /// Отримання налаштувань надбавок
  Future<BonusSettings> getBonusSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('bonusSettings');

    if (maps.isEmpty) {
      // Якщо налаштування відсутні, створюємо значення за замовчуванням
      final BonusSettings defaultSettings = const BonusSettings();
      await db.insert('bonusSettings', defaultSettings.toMap());
      return defaultSettings;
    }

    return BonusSettings.fromMap(maps.first);
  }

  /// Оновлення налаштувань надбавок
  Future<void> updateBonusSettings(BonusSettings settings) async {
    final db = await database;
    await db.update(
      'bonusSettings',
      settings.toMap(),
      where: 'id = ?',
      whereArgs: [1], // Завжди використовуємо id=1 для налаштувань
    );
  }

  // CRUD операції для персональних надбавок

  /// Додавання нової персональної надбавки
  Future<void> insertEmployeeBonus(EmployeeBonus bonus) async {
    final db = await database;
    await db.insert(
      'employeeBonuses',
      bonus.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення персональної надбавки
  Future<void> updateEmployeeBonus(EmployeeBonus bonus) async {
    final db = await database;
    await db.update(
      'employeeBonuses',
      bonus.toMap(),
      where: 'id = ?',
      whereArgs: [bonus.id],
    );
  }

  /// Видалення персональної надбавки
  Future<void> deleteEmployeeBonus(String id) async {
    final db = await database;
    await db.delete('employeeBonuses', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання персональної надбавки за ідентифікатором
  Future<EmployeeBonus?> getEmployeeBonus(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employeeBonuses',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return EmployeeBonus.fromMap(maps.first);
  }

  /// Отримання всіх персональних надбавок працівника
  Future<List<EmployeeBonus>> getEmployeeBonusesByEmployee(
    String employeeId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employeeBonuses',
      where: 'employeeId = ?',
      whereArgs: [employeeId],
    );

    return List.generate(maps.length, (i) => EmployeeBonus.fromMap(maps[i]));
  }

  /// Отримання активних персональних надбавок працівника на вказану дату
  Future<List<EmployeeBonus>> getActiveEmployeeBonuses(
    String employeeId,
    DateTime date,
  ) async {
    final List<EmployeeBonus> allBonuses = await getEmployeeBonusesByEmployee(
      employeeId,
    );
    return allBonuses.where((bonus) => bonus.isActiveOn(date)).toList();
  }

  // CRUD операції для святкових днів

  /// Додавання нового святкового дня
  Future<void> insertHoliday(Holiday holiday) async {
    final db = await database;
    await db.insert(
      'holidays',
      holiday.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення інформації про святковий день
  Future<void> updateHoliday(Holiday holiday) async {
    final db = await database;
    await db.update(
      'holidays',
      holiday.toMap(),
      where: 'id = ?',
      whereArgs: [holiday.id],
    );
  }

  /// Видалення святкового дня
  Future<void> deleteHoliday(String id) async {
    final db = await database;
    await db.delete('holidays', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання святкового дня за ідентифікатором
  Future<Holiday?> getHoliday(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'holidays',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return Holiday.fromMap(maps.first);
  }

  /// Отримання святкових днів за країною
  Future<List<Holiday>> getHolidaysByCountry(String country) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'holidays',
      where: 'country = ?',
      whereArgs: [country],
    );
    return List.generate(maps.length, (i) => Holiday.fromMap(maps[i]));
  }

  /// Перевірка, чи є дата святковим днем
  Future<bool> isHoliday(DateTime date, String country) async {
    final db = await database;

    // Перевірка на щорічні свята (без урахування року)
    final List<Map<String, dynamic>> recurringMaps = await db.rawQuery(
      '''
      SELECT * FROM holidays
      WHERE country = ?
      AND isRecurring = 1
      AND strftime('%m-%d', date / 1000, 'unixepoch') = strftime('%m-%d', ? / 1000, 'unixepoch')
    ''',
      [country, date.millisecondsSinceEpoch],
    );

    if (recurringMaps.isNotEmpty) return true;

    // Перевірка на одноразові свята (з урахуванням року)
    final List<Map<String, dynamic>> nonRecurringMaps = await db.rawQuery(
      '''
      SELECT * FROM holidays
      WHERE country = ?
      AND isRecurring = 0
      AND strftime('%Y-%m-%d', date / 1000, 'unixepoch') = strftime('%Y-%m-%d', ? / 1000, 'unixepoch')
    ''',
      [country, date.millisecondsSinceEpoch],
    );

    return nonRecurringMaps.isNotEmpty;
  }

  // CRUD операції для відсутностей (відпустки, лікарняні, відгули)

  /// Додавання нової відсутності
  Future<void> insertAbsence(Absence absence) async {
    final db = await database;
    await db.insert(
      'absences',
      absence.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення інформації про відсутність
  Future<void> updateAbsence(Absence absence) async {
    final db = await database;
    await db.update(
      'absences',
      absence.toMap(),
      where: 'id = ?',
      whereArgs: [absence.id],
    );
  }

  /// Видалення відсутності
  Future<void> deleteAbsence(String id) async {
    final db = await database;
    await db.delete('absences', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання відсутності за ідентифікатором
  Future<Absence?> getAbsence(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'absences',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return Absence.fromMap(maps.first);
  }

  /// Отримання всіх відсутностей працівника
  Future<List<Absence>> getAbsencesByEmployee(String employeeId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'absences',
      where: 'employeeId = ?',
      whereArgs: [employeeId],
      orderBy: 'startDate DESC',
    );

    return List.generate(maps.length, (i) => Absence.fromMap(maps[i]));
  }

  /// Отримання відсутностей працівника за період
  Future<List<Absence>> getAbsencesByEmployeeAndPeriod(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'absences',
      where: 'employeeId = ? AND (startDate <= ? AND endDate >= ?)',
      whereArgs: [
        employeeId,
        endDate.millisecondsSinceEpoch,
        startDate.millisecondsSinceEpoch,
      ],
      orderBy: 'startDate ASC',
    );

    return List.generate(maps.length, (i) => Absence.fromMap(maps[i]));
  }

  /// Отримання відсутностей працівника за типом та статусом
  Future<List<Absence>> getAbsencesByEmployeeAndTypeAndStatus(
    String employeeId,
    AbsenceType type,
    AbsenceStatus status,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'absences',
      where: 'employeeId = ? AND type = ? AND status = ?',
      whereArgs: [employeeId, type.index, status.index],
      orderBy: 'startDate DESC',
    );

    return List.generate(maps.length, (i) => Absence.fromMap(maps[i]));
  }

  /// Отримання всіх відсутностей за статусом
  Future<List<Absence>> getAbsencesByStatus(AbsenceStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'absences',
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'startDate DESC',
    );

    return List.generate(maps.length, (i) => Absence.fromMap(maps[i]));
  }

  /// Перевірка, чи є у працівника активна відсутність на вказану дату
  Future<bool> hasActiveAbsence(String employeeId, DateTime date) async {
    final db = await database;
    final normalizedDate = DateTime(date.year, date.month, date.day);
    final List<Map<String, dynamic>> maps = await db.query(
      'absences',
      where:
          'employeeId = ? AND status = ? AND startDate <= ? AND endDate >= ?',
      whereArgs: [
        employeeId,
        AbsenceStatus.approved.index,
        normalizedDate.millisecondsSinceEpoch + 86399999, // Кінець дня
        normalizedDate.millisecondsSinceEpoch, // Початок дня
      ],
    );

    return maps.isNotEmpty;
  }

  /// Отримання активної відсутності працівника на вказану дату
  Future<Absence?> getActiveAbsence(String employeeId, DateTime date) async {
    final db = await database;
    final normalizedDate = DateTime(date.year, date.month, date.day);
    final List<Map<String, dynamic>> maps = await db.query(
      'absences',
      where:
          'employeeId = ? AND status = ? AND startDate <= ? AND endDate >= ?',
      whereArgs: [
        employeeId,
        AbsenceStatus.approved.index,
        normalizedDate.millisecondsSinceEpoch + 86399999, // Кінець дня
        normalizedDate.millisecondsSinceEpoch, // Початок дня
      ],
    );

    if (maps.isEmpty) return null;
    return Absence.fromMap(maps.first);
  }

  // CRUD операції для нагадувань

  /// Додавання нового нагадування
  Future<void> insertReminder(Reminder reminder) async {
    final db = await database;
    await db.insert(
      'reminders',
      reminder.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Оновлення нагадування
  Future<void> updateReminder(Reminder reminder) async {
    final db = await database;
    await db.update(
      'reminders',
      reminder.toMap(),
      where: 'id = ?',
      whereArgs: [reminder.id],
    );
  }

  /// Видалення нагадування
  Future<void> deleteReminder(String id) async {
    final db = await database;
    await db.delete('reminders', where: 'id = ?', whereArgs: [id]);
  }

  /// Отримання нагадування за ідентифікатором
  Future<Reminder?> getReminder(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reminders',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return Reminder.fromMap(maps.first);
  }

  /// Отримання всіх нагадувань для працівника
  Future<List<Reminder>> getRemindersByEmployee(String employeeId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reminders',
      where: 'employeeId = ?',
      whereArgs: [employeeId],
    );

    return List.generate(maps.length, (i) => Reminder.fromMap(maps[i]));
  }

  /// Отримання нагадувань для працівника за статусом
  Future<List<Reminder>> getRemindersByEmployeeAndStatus(
    String employeeId,
    ReminderStatus status,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reminders',
      where: 'employeeId = ? AND status = ?',
      whereArgs: [employeeId, status.index],
    );

    return List.generate(maps.length, (i) => Reminder.fromMap(maps[i]));
  }

  /// Отримання нагадувань для працівника за датою, до якої відноситься нагадування
  Future<List<Reminder>> getRemindersByEmployeeAndRelatedDate(
    String employeeId,
    DateTime relatedDate,
  ) async {
    final db = await database;
    final normalizedDate = DateTime(
      relatedDate.year,
      relatedDate.month,
      relatedDate.day,
    );

    final List<Map<String, dynamic>> maps = await db.query(
      'reminders',
      where: 'employeeId = ? AND relatedDate = ?',
      whereArgs: [employeeId, normalizedDate.millisecondsSinceEpoch],
    );

    return List.generate(maps.length, (i) => Reminder.fromMap(maps[i]));
  }

  /// Отримання робочих днів за ідентифікатором працівника та датою
  Future<List<WorkDay>> getWorkDaysByEmployeeAndDate(
    String employeeId,
    DateTime date,
  ) async {
    final db = await database;
    final normalizedDate = DateTime(date.year, date.month, date.day);

    final List<Map<String, dynamic>> maps = await db.query(
      'workDays',
      where: 'employeeId = ? AND date = ?',
      whereArgs: [employeeId, normalizedDate.millisecondsSinceEpoch],
    );

    return List.generate(maps.length, (i) => WorkDay.fromMap(maps[i]));
  }
}
