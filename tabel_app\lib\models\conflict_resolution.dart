import 'syncable_model.dart';

/// Стратегії вирішення конфліктів
enum ConflictResolutionStrategy {
  /// Використовувати локальну версію
  useLocal,

  /// Використовувати віддалену версію
  useRemote,

  /// Використовувати найновішу версію
  useNewest,

  /// Використовувати найстарішу версію
  useOldest,

  /// Ручне вирішення конфлікту
  manual,
}

/// Виняток, що виникає при конфлікті синхронізації
class ConflictException implements Exception {
  final SyncableModel localModel;
  final SyncableModel remoteModel;

  ConflictException(this.localModel, this.remoteModel);

  @override
  String toString() {
    return 'ConflictException: Conflict detected between local and remote models with id ${localModel.id}';
  }
}

/// Клас для вирішення конфліктів синхронізації
class ConflictResolver {
  /// Вирішення конфлікту між локальною та віддаленою моделями
  static T resolveConflict<T extends SyncableModel>({
    required T localModel,
    required T remoteModel,
    required ConflictResolutionStrategy strategy,
  }) {
    if (!localModel.hasConflictWith(remoteModel)) {
      throw ArgumentError('Models do not have a conflict');
    }

    switch (strategy) {
      case ConflictResolutionStrategy.useLocal:
        return localModel;
      case ConflictResolutionStrategy.useRemote:
        return remoteModel;
      case ConflictResolutionStrategy.useNewest:
        return localModel.updatedAt.isAfter(remoteModel.updatedAt)
            ? localModel
            : remoteModel;
      case ConflictResolutionStrategy.useOldest:
        return localModel.updatedAt.isBefore(remoteModel.updatedAt)
            ? localModel
            : remoteModel;
      case ConflictResolutionStrategy.manual:
        // Для ручного вирішення повертаємо виняток
        throw ConflictException(localModel, remoteModel);
    }
  }
}
