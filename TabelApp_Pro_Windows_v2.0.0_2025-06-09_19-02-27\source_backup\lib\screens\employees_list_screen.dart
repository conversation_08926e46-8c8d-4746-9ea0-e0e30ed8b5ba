import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';
import 'employee_detail_screen.dart';
import 'employee_edit_screen.dart';

/// Екран списку всіх працівників
class EmployeesListScreen extends StatefulWidget {
  const EmployeesListScreen({super.key});

  @override
  State<EmployeesListScreen> createState() => _EmployeesListScreenState();
}

class _EmployeesListScreenState extends State<EmployeesListScreen> {
  List<Employee> _employees = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'name'; // name, position, hourlyRate, hireDate
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  /// Завантаження списку працівників
  Future<void> _loadEmployees() async {
    setState(() => _isLoading = true);
    
    try {
      final databaseService = Provider.of<DatabaseService>(context, listen: false);
      final employees = await databaseService.getAllEmployees();
      
      setState(() {
        _employees = employees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Помилка завантаження: $e')),
        );
      }
    }
  }

  /// Фільтрація та сортування працівників
  List<Employee> get _filteredEmployees {
    var filtered = _employees.where((employee) {
      final query = _searchQuery.toLowerCase();
      return employee.fullName.toLowerCase().contains(query) ||
             employee.position.toLowerCase().contains(query) ||
             employee.email.toLowerCase().contains(query);
    }).toList();

    // Сортування
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.fullName.compareTo(b.fullName);
          break;
        case 'position':
          comparison = a.position.compareTo(b.position);
          break;
        case 'hourlyRate':
          comparison = a.hourlyRate.compareTo(b.hourlyRate);
          break;
        case 'hireDate':
          final aDate = a.hireDate ?? DateTime(1900);
          final bDate = b.hireDate ?? DateTime(1900);
          comparison = aDate.compareTo(bDate);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  /// Видалення працівника
  Future<void> _deleteEmployee(Employee employee) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Підтвердження'),
        content: Text('Ви впевнені, що хочете видалити працівника ${employee.fullName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Скасувати'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Видалити'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final databaseService = Provider.of<DatabaseService>(context, listen: false);
        await databaseService.deleteEmployee(employee.id);
        await _loadEmployees();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Працівника ${employee.fullName} видалено')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Помилка видалення: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final appState = Provider.of<AppState>(context);
    final currentEmployee = appState.currentEmployee;
    final isAdmin = currentEmployee?.isAdmin ?? false;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('employees')),
        actions: [
          // Пошук
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: _EmployeeSearchDelegate(_employees),
              );
            },
          ),
          // Сортування
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                if (_sortBy == value) {
                  _sortAscending = !_sortAscending;
                } else {
                  _sortBy = value;
                  _sortAscending = true;
                }
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'name', child: Text('За ім\'ям')),
              const PopupMenuItem(value: 'position', child: Text('За посадою')),
              const PopupMenuItem(value: 'hourlyRate', child: Text('За ставкою')),
              const PopupMenuItem(value: 'hireDate', child: Text('За датою прийому')),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Панель пошуку
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Пошук працівників...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() => _searchQuery = value);
                    },
                  ),
                ),
                // Статистика
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem('Всього', _employees.length.toString()),
                      _buildStatItem('Адміністраторів', 
                        _employees.where((e) => e.isAdmin).length.toString()),
                      _buildStatItem('Активних', _filteredEmployees.length.toString()),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                // Список працівників
                Expanded(
                  child: _filteredEmployees.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.people_outline,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _searchQuery.isEmpty 
                                    ? 'Немає працівників'
                                    : 'Працівників не знайдено',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          itemCount: _filteredEmployees.length,
                          itemBuilder: (context, index) {
                            final employee = _filteredEmployees[index];
                            return _buildEmployeeCard(employee, isAdmin);
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: isAdmin
          ? FloatingActionButton(
              onPressed: () async {
                final result = await Navigator.of(context).push<bool>(
                  MaterialPageRoute(
                    builder: (context) => const EmployeeEditScreen(),
                  ),
                );
                if (result == true) {
                  await _loadEmployees();
                }
              },
              child: const Icon(Icons.add),
              tooltip: 'Додати працівника',
            )
          : null,
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildEmployeeCard(Employee employee, bool isAdmin) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: employee.isAdmin ? Colors.orange : Colors.blue,
          child: Text(
            employee.firstName.isNotEmpty ? employee.firstName[0].toUpperCase() : '',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          employee.fullName,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(employee.position),
            Text(
              '${employee.hourlyRate.toStringAsFixed(2)} грн/год',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (employee.isAdmin)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Адміністратор',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        trailing: isAdmin
            ? PopupMenuButton<String>(
                onSelected: (value) async {
                  switch (value) {
                    case 'view':
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => EmployeeDetailScreen(employee: employee),
                        ),
                      );
                      break;
                    case 'edit':
                      final result = await Navigator.of(context).push<bool>(
                        MaterialPageRoute(
                          builder: (context) => EmployeeEditScreen(employee: employee),
                        ),
                      );
                      if (result == true) {
                        await _loadEmployees();
                      }
                      break;
                    case 'delete':
                      await _deleteEmployee(employee);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'view', child: Text('Переглянути')),
                  const PopupMenuItem(value: 'edit', child: Text('Редагувати')),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Text('Видалити', style: TextStyle(color: Colors.red)),
                  ),
                ],
              )
            : IconButton(
                icon: const Icon(Icons.visibility),
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => EmployeeDetailScreen(employee: employee),
                    ),
                  );
                },
              ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => EmployeeDetailScreen(employee: employee),
            ),
          );
        },
      ),
    );
  }
}

/// Делегат для пошуку працівників
class _EmployeeSearchDelegate extends SearchDelegate<Employee?> {
  final List<Employee> employees;

  _EmployeeSearchDelegate(this.employees);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return buildSuggestions(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final filteredEmployees = employees.where((employee) {
      final queryLower = query.toLowerCase();
      return employee.fullName.toLowerCase().contains(queryLower) ||
             employee.position.toLowerCase().contains(queryLower) ||
             employee.email.toLowerCase().contains(queryLower);
    }).toList();

    return ListView.builder(
      itemCount: filteredEmployees.length,
      itemBuilder: (context, index) {
        final employee = filteredEmployees[index];
        return ListTile(
          leading: CircleAvatar(
            child: Text(employee.firstName[0].toUpperCase()),
          ),
          title: Text(employee.fullName),
          subtitle: Text(employee.position),
          onTap: () {
            close(context, employee);
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => EmployeeDetailScreen(employee: employee),
              ),
            );
          },
        );
      },
    );
  }
}
