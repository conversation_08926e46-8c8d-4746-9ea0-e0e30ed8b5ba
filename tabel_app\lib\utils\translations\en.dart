/// English translations for new features
final Map<String, String> enSyncAndReminderTranslations = {
  // Sync Settings
  'sync_settings': 'Sync Settings',
  'enable_sync': 'Enable Sync',
  'enable_sync_description': 'Automatically sync data in the background',
  'sync_frequency': 'Sync Frequency',
  'conflict_resolution': 'Conflict Resolution',
  'wifi_only': 'Wi-Fi Only',
  'wifi_only_description': 'Only sync data when connected to Wi-Fi',
  'battery_not_low': 'Battery Not Low',
  'battery_not_low_description': 'Only sync data when battery is not low',
  'sync_status': 'Sync Status',
  'pending_operations': 'Pending Operations',
  'sync_now': 'Sync Now',
  'syncing': 'Syncing...',
  'save_settings': 'Save Settings',
  'settings_saved': 'Settings Saved',
  'error_saving_settings': 'Error Saving Settings',
  'no_internet_connection': 'No Internet Connection',
  'sync_completed': 'Sync Completed',
  'select_sync_frequency': 'Select Sync Frequency',
  'select_conflict_strategy': 'Select Conflict Resolution Strategy',
  'use_local': 'Use Local Version',
  'use_remote': 'Use Remote Version',
  'use_newest': 'Use Newest Version',
  'use_oldest': 'Use Oldest Version',
  'manual_resolution': 'Manual Resolution',
  'use_local_description': 'Always use the version from this device',
  'use_remote_description': 'Always use the version from the server',
  'use_newest_description':
      'Use the version with the most recent modification date',
  'use_oldest_description': 'Use the version with the oldest modification date',
  'manual_resolution_description': 'Ask the user for each conflict',

  // Reminder Settings
  'reminder_settings': 'Reminder Settings',
  'enable_reminders': 'Enable Reminders',
  'enable_reminders_description':
      'Receive reminders for timesheet entries and other events',
  'reminder_frequency': 'Reminder Frequency',
  'reminder_time': 'Reminder Time',
  'reminder_days': 'Reminder Days',
  'reminder_types': 'Reminder Types',
  'in_app_reminders': 'In-App',
  'email_reminders': 'Email',
  'push_reminders': 'Push Notifications',
  'skip_holidays': 'Skip Weekends and Holidays',
  'skip_holidays_description': 'Do not send reminders on weekends and holidays',
  'test_reminder': 'Test Reminder',
  'test_reminder_message':
      'This is a test reminder. If you see this message, reminders are working correctly.',
  'test_reminder_sent': 'Test Reminder Sent',
  'select_reminder_frequency': 'Select Reminder Frequency',
  'select_reminder_time': 'Select Reminder Time',
  'daily': 'Daily',
  'weekdays': 'Weekdays Only',
  'custom': 'Custom Days',
  'daily_description': 'Reminders every day',
  'weekdays_description': 'Reminders only on weekdays (Mon-Fri)',
  'custom_description': 'Reminders on selected days of the week',
  'monday': 'Monday',
  'tuesday': 'Tuesday',
  'wednesday': 'Wednesday',
  'thursday': 'Thursday',
  'friday': 'Friday',
  'saturday': 'Saturday',
  'sunday': 'Sunday',
  'notification_permission_denied':
      'Notification permission denied. Reminders will not work.',

  // User switching
  'switch_user': 'Switch User',
  'recent_users': 'Recent Users',
  'all_users': 'All Users',
  'recent': 'Recent',
  'add_user': 'Add User',
  'no_users_found': 'No users found',
  'logout': 'Logout',
  'retry': 'Retry',
};
