/// Перелік днів тижня
enum DayOfWeek {
  monday(1, 'Понеділок'),
  tuesday(2, 'Вівторок'),
  wednesday(3, 'Середа'),
  thursday(4, 'Четвер'),
  friday(5, 'П\'ятниця'),
  saturday(6, 'Субота'),
  sunday(7, 'Неділя');

  /// Номер дня тижня (1-7)
  final int value;
  
  /// Назва дня тижня
  final String name;
  
  const DayOfWeek(this.value, this.name);
  
  /// Отримання дня тижня за номером
  static DayOfWeek fromValue(int value) {
    return DayOfWeek.values.firstWhere(
      (day) => day.value == value,
      orElse: () => DayOfWeek.monday,
    );
  }
  
  /// Отримання дня тижня з DateTime
  static DayOfWeek fromDateTime(DateTime dateTime) {
    // DateTime.weekday повертає 1 для понеділка, 7 для неділі
    return fromValue(dateTime.weekday);
  }
  
  /// Перевірка, чи є день вихідним (субота або неділя)
  bool get isWeekend {
    return this == DayOfWeek.saturday || this == DayOfWeek.sunday;
  }
}
