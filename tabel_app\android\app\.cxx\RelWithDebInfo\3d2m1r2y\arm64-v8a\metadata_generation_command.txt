                        -HC:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\AndroidSDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\AndroidSDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\AndroidSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\AndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Tabel\tabel_app\build\app\intermediates\cxx\RelWithDebInfo\3d2m1r2y\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Tabel\tabel_app\build\app\intermediates\cxx\RelWithDebInfo\3d2m1r2y\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\Tabel\tabel_app\android\app\.cxx\RelWithDebInfo\3d2m1r2y\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2