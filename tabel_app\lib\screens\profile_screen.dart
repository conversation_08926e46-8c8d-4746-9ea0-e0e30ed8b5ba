import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../utils/utils.dart';
import '../services/services.dart';
import '../main.dart';
import 'employee_bonuses_screen.dart';
import 'login_screen.dart';
import 'shifts/shifts.dart';

/// Екран профілю користувача
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isEditing = false;
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _positionController = TextEditingController();
  final _hourlyRateController = TextEditingController();
  final _experienceYearsController = TextEditingController();
  final _qualificationLevelController = TextEditingController();
  DateTime? _hireDate;
  bool _isAdmin = false;

  @override
  void initState() {
    super.initState();
    _loadEmployeeData();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _positionController.dispose();
    _hourlyRateController.dispose();
    _experienceYearsController.dispose();
    _qualificationLevelController.dispose();
    super.dispose();
  }

  /// Завантаження даних працівника
  void _loadEmployeeData() {
    final appState = Provider.of<AppState>(context, listen: false);
    final employee = appState.currentEmployee;
    if (employee != null) {
      _firstNameController.text = employee.firstName;
      _lastNameController.text = employee.lastName;
      _positionController.text = employee.position;
      _hourlyRateController.text = employee.hourlyRate.toString();
      _experienceYearsController.text =
          employee.experienceYears?.toString() ?? '';
      _qualificationLevelController.text =
          employee.qualificationLevel?.toString() ?? '';
      _hireDate = employee.hireDate;
      _isAdmin = employee.isAdmin;
    }
  }

  /// Збереження змін
  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final employee = appState.currentEmployee;
    if (employee == null) return;

    final updatedEmployee = employee.copyWith(
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      position: _positionController.text.trim(),
      hourlyRate: double.parse(_hourlyRateController.text.trim()),
      experienceYears:
          _experienceYearsController.text.isEmpty
              ? null
              : int.parse(_experienceYearsController.text.trim()),
      qualificationLevel:
          _qualificationLevelController.text.isEmpty
              ? null
              : int.parse(_qualificationLevelController.text.trim()),
      hireDate: _hireDate,
      isAdmin: _isAdmin,
    );

    try {
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );
      await databaseService.updateEmployee(updatedEmployee);
      appState.setCurrentEmployee(updatedEmployee);
      setState(() {
        _isEditing = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).translate('success')),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).translate('error')}: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Вибір дати початку роботи
  Future<void> _selectHireDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _hireDate ?? DateTime.now(),
      firstDate: DateTime(1990),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _hireDate) {
      setState(() {
        _hireDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final appState = Provider.of<AppState>(context);
    final employee = appState.currentEmployee;
    final currency = appState.currency;

    if (employee == null) {
      return Scaffold(
        appBar: AppBar(title: Text(localizations.translate('profile'))),
        body: Center(child: Text(localizations.translate('error'))),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('profile')),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              tooltip: localizations.translate('edit'),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveChanges,
              tooltip: localizations.translate('save'),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child:
            _isEditing
                ? _buildEditForm(localizations)
                : _buildProfileView(localizations, employee, currency),
      ),
    );
  }

  /// Побудова форми редагування профілю
  Widget _buildEditForm(AppLocalizations localizations) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: _firstNameController,
            decoration: InputDecoration(
              labelText: localizations.translate('first_name'),
              border: const OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return localizations.translate('required_field');
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _lastNameController,
            decoration: InputDecoration(
              labelText: localizations.translate('last_name'),
              border: const OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return localizations.translate('required_field');
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _positionController,
            decoration: InputDecoration(
              labelText: localizations.translate('position'),
              border: const OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return localizations.translate('required_field');
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _hourlyRateController,
            decoration: InputDecoration(
              labelText: localizations.translate('hourly_rate'),
              border: const OutlineInputBorder(),
              suffixText: Provider.of<AppState>(context).currency.symbol,
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return localizations.translate('required_field');
              }
              try {
                final rate = double.parse(value);
                if (rate <= 0) {
                  return localizations.translate('invalid_hourly_rate');
                }
              } catch (e) {
                return localizations.translate('invalid_hourly_rate');
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _experienceYearsController,
            decoration: InputDecoration(
              labelText: localizations.translate('experience_years'),
              border: const OutlineInputBorder(),
              suffixText: localizations.translate('years'),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                try {
                  final years = int.parse(value);
                  if (years < 0) {
                    return localizations.translate('invalid_experience');
                  }
                } catch (e) {
                  return localizations.translate('invalid_experience');
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _qualificationLevelController,
            decoration: InputDecoration(
              labelText: localizations.translate('qualification_level'),
              border: const OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                try {
                  final level = int.parse(value);
                  if (level < 1 || level > 5) {
                    return localizations.translate('invalid_qualification');
                  }
                } catch (e) {
                  return localizations.translate('invalid_qualification');
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          ListTile(
            title: Text(localizations.translate('hire_date')),
            subtitle: Text(
              _hireDate != null
                  ? localizations.formatDate(_hireDate!)
                  : localizations.translate('not_specified'),
            ),
            trailing: const Icon(Icons.calendar_today),
            onTap: _selectHireDate,
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: Text(localizations.translate('admin')),
            value: _isAdmin,
            onChanged: (value) {
              setState(() {
                _isAdmin = value;
              });
            },
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _saveChanges,
                child: Text(localizations.translate('save')),
              ),
              OutlinedButton(
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                    _loadEmployeeData();
                  });
                },
                child: Text(localizations.translate('cancel')),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Побудова перегляду профілю
  Widget _buildProfileView(
    AppLocalizations localizations,
    Employee employee,
    Currency currency,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: CircleAvatar(
                    radius: 50,
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      '${employee.firstName[0]}${employee.lastName[0]}',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Center(
                  child: Text(
                    employee.fullName,
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 8),
                Center(
                  child: Text(
                    employee.position,
                    style: Theme.of(context).textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 8),
                Center(
                  child: Text(
                    employee.email,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 8),
                Center(
                  child: Chip(
                    label: Text(
                      employee.isAdmin
                          ? localizations.translate('admin')
                          : localizations.translate('employee'),
                    ),
                    backgroundColor:
                        employee.isAdmin
                            ? Theme.of(
                              context,
                            ).colorScheme.primary.withOpacity(0.2)
                            : Theme.of(
                              context,
                            ).colorScheme.secondary.withOpacity(0.2),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Кнопка для переходу на екран персональних надбавок
        Card(
          elevation: 2,
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => EmployeeBonusesScreen(employee: employee),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.translate('employee_bonuses'),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Icon(Icons.arrow_forward_ios),
                ],
              ),
            ),
          ),
        ),

        // Кнопка для переходу на екран налаштувань графіку змін
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) =>
                          AssignShiftScheduleScreen(employeeId: employee.id),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Налаштування графіку змін',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Icon(Icons.arrow_forward_ios),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  localizations.translate('hourly_rate'),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${employee.hourlyRate.toStringAsFixed(2)} ${currency.symbol}',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Divider(),
                if (employee.hireDate != null) ...[
                  Text(
                    localizations.translate('hire_date'),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    localizations.formatDate(employee.hireDate!),
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const Divider(),
                ],
                if (employee.experienceYears != null) ...[
                  Text(
                    localizations.translate('experience_years'),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${employee.experienceYears} ${localizations.translate('years')}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const Divider(),
                ],
                if (employee.qualificationLevel != null) ...[
                  Text(
                    localizations.translate('qualification_level'),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${employee.qualificationLevel}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('country'),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    DropdownButton<AppCountry>(
                      value: Provider.of<AppState>(context).country,
                      onChanged: (AppCountry? newValue) {
                        if (newValue != null) {
                          Provider.of<AppState>(
                            context,
                            listen: false,
                          ).setCountry(newValue);
                        }
                      },
                      items:
                          AppCountry.values.map<DropdownMenuItem<AppCountry>>((
                            AppCountry value,
                          ) {
                            return DropdownMenuItem<AppCountry>(
                              value: value,
                              child: Text(localizations.translate(value.name)),
                            );
                          }).toList(),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('currency'),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      '${currency.name} (${currency.symbol})',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ],
                ),
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('language'),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    DropdownButton<AppLanguage>(
                      value: Provider.of<AppState>(context).language,
                      onChanged: (AppLanguage? newValue) {
                        if (newValue != null) {
                          Provider.of<AppState>(
                            context,
                            listen: false,
                          ).setLanguage(newValue);
                        }
                      },
                      items:
                          AppLanguage.values.map<DropdownMenuItem<AppLanguage>>(
                            (AppLanguage value) {
                              return DropdownMenuItem<AppLanguage>(
                                value: value,
                                child: Text(value.name),
                              );
                            },
                          ).toList(),
                    ),
                  ],
                ),
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('theme'),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Switch(
                      value: Provider.of<AppState>(context).isDarkMode,
                      onChanged: (bool value) {
                        Provider.of<AppState>(
                          context,
                          listen: false,
                        ).setThemeMode(value);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
        // Кнопка виходу
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _logout,
            icon: const Icon(Icons.exit_to_app),
            label: Text(localizations.translate('logout')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
      ],
    );
  }

  /// Вихід з системи
  Future<void> _logout() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      await authService.signOut();

      if (!mounted) return;

      // Встановлюємо currentEmployee в null
      Provider.of<AppState>(context, listen: false).setCurrentEmployee(null);

      // Перехід на екран входу з видаленням всіх попередніх екранів
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false,
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${AppLocalizations.of(context).translate('error')}: ${e.toString()}',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
