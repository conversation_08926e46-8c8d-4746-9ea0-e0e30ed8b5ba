<!DOCTYPE html>
<html lang="uk">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Додаток для обліку робочого часу та розрахунку зарплати з підтримкою бонусів та надбавок.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Tabel App">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Tabel App - Облік робочого часу</title>
  <link rel="manifest" href="manifest.json">

  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
    }

    .container {
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      text-align: center;
      max-width: 500px;
      width: 90%;
    }

    .logo {
      width: 80px;
      height: 80px;
      margin: 0 auto 20px;
      background: url('favicon.png') center/contain no-repeat;
      border-radius: 16px;
    }

    h1 {
      color: #2c3e50;
      margin-bottom: 10px;
      font-size: 2.2em;
      font-weight: 300;
    }

    .subtitle {
      color: #7f8c8d;
      margin-bottom: 30px;
      font-size: 1.1em;
    }

    .status {
      background: #e8f5e8;
      border: 1px solid #4caf50;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 30px;
    }

    .status-title {
      color: #2e7d32;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .status-text {
      color: #388e3c;
    }

    .features {
      text-align: left;
      margin-bottom: 30px;
    }

    .features h3 {
      color: #2c3e50;
      margin-bottom: 15px;
      text-align: center;
    }

    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      padding: 8px 0;
    }

    .feature-icon {
      width: 20px;
      height: 20px;
      background: #4caf50;
      border-radius: 50%;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
    }

    .btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 15px 30px;
      border-radius: 10px;
      font-size: 1.1em;
      cursor: pointer;
      transition: transform 0.2s, box-shadow 0.2s;
      margin: 5px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .info {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 20px;
      margin-top: 20px;
      font-size: 0.9em;
      color: #6c757d;
    }

    @media (max-width: 600px) {
      .container {
        padding: 30px 20px;
      }

      h1 {
        font-size: 1.8em;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo"></div>
    <h1>Tabel App</h1>
    <p class="subtitle">Додаток для обліку робочого часу та розрахунку зарплати</p>

    <div class="status">
      <div class="status-title">✅ Веб-версія готова до розробки</div>
      <div class="status-text">Проект налаштований для запуску в браузері</div>
    </div>

    <div class="features">
      <h3>Основні можливості:</h3>
      <div class="feature-item">
        <div class="feature-icon">⏰</div>
        <span>Облік робочого часу</span>
      </div>
      <div class="feature-item">
        <div class="feature-icon">💰</div>
        <span>Розрахунок зарплати</span>
      </div>
      <div class="feature-item">
        <div class="feature-icon">📊</div>
        <span>Звіти та аналітика</span>
      </div>
      <div class="feature-item">
        <div class="feature-icon">🎯</div>
        <span>Бонуси та надбавки</span>
      </div>
      <div class="feature-item">
        <div class="feature-icon">📱</div>
        <span>Мобільна версія</span>
      </div>
    </div>

    <button class="btn" onclick="showInstallInfo()">Інструкції з встановлення Flutter</button>
    <button class="btn" onclick="showProjectInfo()">Інформація про проект</button>

    <div class="info">
      <strong>Для запуску повної версії:</strong><br>
      1. Встановіть Flutter SDK<br>
      2. Виконайте команду: <code>flutter run -d chrome</code><br>
      3. Або зберіть веб-версію: <code>flutter build web</code>
    </div>
  </div>

  <script>
    function showInstallInfo() {
      alert(`Для встановлення Flutter:

1. Завантажте Flutter SDK з https://flutter.dev
2. Розпакуйте в папку C:\\flutter
3. Додайте C:\\flutter\\bin до PATH
4. Перезапустіть командний рядок
5. Виконайте: flutter doctor
6. Встановіть необхідні залежності
7. Запустіть: flutter run -d chrome`);
    }

    function showProjectInfo() {
      alert(`Tabel App - Додаток для обліку робочого часу

Версія: 1.0.3+4
Платформи: Windows, Web, Android, iOS
Технології: Flutter, Dart, SQLite

Основні функції:
• Облік робочих годин
• Розрахунок зарплати
• Управління співробітниками
• Звіти та експорт
• Багатомовність (UA, EN, CS, SK, PL)
• Темна/світла тема`);
    }

    // Простий PWA функціонал
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js').then(function(registration) {
          console.log('ServiceWorker registration successful');
        }, function(err) {
          console.log('ServiceWorker registration failed: ', err);
        });
      });
    }
  </script>
</body>
</html>
