# Інструкція з завантаження проекту на GitHub

Ця інструкція допоможе вам завантажити ваш проект на GitHub.

## Передумови

Для завантаження проекту на GitHub вам знадобиться:

1. Обліковий запис на [GitHub](https://github.com/)
2. Встановлений [Git](https://git-scm.com/downloads) на вашому комп'ютері
3. Базові знання Git та GitHub

## Крок 1: Створення репозиторію на GitHub

1. Перейдіть на сайт [GitHub](https://github.com/) і увійдіть у свій обліковий запис
2. Натисніть кнопку "+" у верхньому правому куті та виберіть "New repository"
3. Введіть назву репозиторію (наприклад, "tabel-app")
4. Додайте опис (необов'язково)
5. Виберіть тип репозиторію (публічний або приватний)
6. Не ініціалізуйте репозиторій з README, .gitignore або ліцензією
7. Натисніть "Create repository"

## Крок 2: Ініціалізація Git у вашому проекті

1. Запустіть скрипт `init_git.bat`:
   ```
   init_git.bat
   ```

2. Цей скрипт:
   - Ініціалізує Git-репозиторій у вашому проекті
   - Створює файл `.gitignore` з правилами для ігнорування тимчасових файлів та директорій

## Крок 3: Завантаження проекту на GitHub

1. Запустіть скрипт `push_to_github.bat`:
   ```
   push_to_github.bat
   ```

2. Введіть ваше ім'я користувача GitHub та назву репозиторію
3. Введіть повідомлення для коміту (або залиште порожнім для використання "Initial commit")
4. Скрипт додасть всі файли, створить коміт та завантажить проект на GitHub

## Крок 4: Перевірка репозиторію на GitHub

1. Перейдіть на сторінку вашого репозиторію на GitHub:
   ```
   https://github.com/YOUR_USERNAME/YOUR_REPOSITORY
   ```

2. Переконайтеся, що всі файли успішно завантажені

## Додаткові команди Git

Ось кілька корисних команд Git для подальшої роботи з репозиторієм:

- Перевірка статусу:
  ```
  git status
  ```

- Додавання змін:
  ```
  git add .
  ```

- Створення коміту:
  ```
  git commit -m "Ваше повідомлення"
  ```

- Завантаження змін на GitHub:
  ```
  git push
  ```

- Отримання змін з GitHub:
  ```
  git pull
  ```

## Вирішення проблем

### Помилка автентифікації

Якщо ви отримуєте помилку автентифікації при спробі завантажити проект на GitHub:

1. Переконайтеся, що ви правильно ввели ім'я користувача та назву репозиторію
2. Спробуйте використати GitHub CLI для автентифікації:
   ```
   gh auth login
   ```

3. Або налаштуйте Git для використання токену доступу:
   ```
   git config --global credential.helper store
   ```

### Конфлікти при завантаженні

Якщо ви отримуєте конфлікти при спробі завантажити проект на GitHub:

1. Отримайте останні зміни з GitHub:
   ```
   git pull --rebase origin master
   ```

2. Вирішіть конфлікти вручну
3. Додайте вирішені конфлікти:
   ```
   git add .
   ```

4. Продовжіть rebase:
   ```
   git rebase --continue
   ```

5. Завантажте зміни на GitHub:
   ```
   git push -f origin master
   ```
