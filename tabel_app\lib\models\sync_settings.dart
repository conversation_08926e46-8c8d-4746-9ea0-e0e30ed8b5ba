import 'dart:convert';
import 'conflict_resolution.dart';

/// Частота синхронізації
enum SyncFrequency {
  /// Ручна синхронізація
  manual,

  /// Кожну годину
  hourly,

  /// Кожні дві години
  everyTwoHours,

  /// Чотири рази на день
  fourTimesDaily,

  /// Двічі на день
  twiceDaily,

  /// Щодня
  daily,
}

/// Розширення для SyncFrequency
extension SyncFrequencyExtension on SyncFrequency {
  /// Отримання тривалості в хвилинах
  int get durationInMinutes {
    switch (this) {
      case SyncFrequency.manual:
        return 0;
      case SyncFrequency.hourly:
        return 60;
      case SyncFrequency.everyTwoHours:
        return 120;
      case SyncFrequency.fourTimesDaily:
        return 360;
      case SyncFrequency.twiceDaily:
        return 720;
      case SyncFrequency.daily:
        return 1440;
    }
  }

  /// Отримання назви для відображення
  String get displayName {
    switch (this) {
      case SyncFrequency.manual:
        return 'Вручну';
      case SyncFrequency.hourly:
        return 'Щогодини';
      case SyncFrequency.everyTwoHours:
        return 'Кожні 2 години';
      case SyncFrequency.fourTimesDaily:
        return '4 рази на день';
      case SyncFrequency.twiceDaily:
        return 'Двічі на день';
      case SyncFrequency.daily:
        return 'Щодня';
    }
  }
}

/// Налаштування синхронізації
class SyncSettings {
  /// Чи увімкнена синхронізація
  final bool enabled;

  /// Частота синхронізації
  final SyncFrequency frequency;

  /// Стратегія вирішення конфліктів
  final ConflictResolutionStrategy conflictStrategy;

  /// Чи синхронізувати тільки через Wi-Fi
  final bool wifiOnly;

  /// Чи синхронізувати тільки при достатньому заряді батареї
  final bool batteryNotLow;

  const SyncSettings({
    this.enabled = true,
    this.frequency = SyncFrequency.daily,
    this.conflictStrategy = ConflictResolutionStrategy.useNewest,
    this.wifiOnly = false,
    this.batteryNotLow = true,
  });

  /// Перетворення в JSON
  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'frequency': frequency.toString().split('.').last,
      'conflictStrategy': conflictStrategy.toString().split('.').last,
      'wifiOnly': wifiOnly,
      'batteryNotLow': batteryNotLow,
    };
  }

  /// Створення з JSON
  factory SyncSettings.fromJson(Map<String, dynamic> json) {
    return SyncSettings(
      enabled: json['enabled'] ?? true,
      frequency: SyncFrequency.values.firstWhere(
        (e) => e.toString().split('.').last == json['frequency'],
        orElse: () => SyncFrequency.daily,
      ),
      conflictStrategy: ConflictResolutionStrategy.values.firstWhere(
        (e) => e.toString().split('.').last == json['conflictStrategy'],
        orElse: () => ConflictResolutionStrategy.useNewest,
      ),
      wifiOnly: json['wifiOnly'] ?? false,
      batteryNotLow: json['batteryNotLow'] ?? true,
    );
  }

  /// Серіалізація в рядок
  String toJsonString() => jsonEncode(toJson());

  /// Десеріалізація з рядка
  static SyncSettings fromJsonString(String jsonString) {
    return SyncSettings.fromJson(jsonDecode(jsonString));
  }

  /// Створення копії з оновленими полями
  SyncSettings copyWith({
    bool? enabled,
    SyncFrequency? frequency,
    ConflictResolutionStrategy? conflictStrategy,
    bool? wifiOnly,
    bool? batteryNotLow,
  }) {
    return SyncSettings(
      enabled: enabled ?? this.enabled,
      frequency: frequency ?? this.frequency,
      conflictStrategy: conflictStrategy ?? this.conflictStrategy,
      wifiOnly: wifiOnly ?? this.wifiOnly,
      batteryNotLow: batteryNotLow ?? this.batteryNotLow,
    );
  }
}
