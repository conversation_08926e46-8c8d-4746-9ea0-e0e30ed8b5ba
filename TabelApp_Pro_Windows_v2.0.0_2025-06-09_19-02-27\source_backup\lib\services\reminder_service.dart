import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:tabel_app/models/reminder.dart';
import 'package:tabel_app/models/reminder_settings.dart';
import 'package:tabel_app/services/database_service.dart';
import 'package:tabel_app/services/settings_service.dart';
import 'package:tabel_app/services/notification_service.dart';

import 'package:tabel_app/utils/date_utils.dart' as date_utils;

/// Сервіс для управління нагадуваннями
class ReminderService {
  final DatabaseService _databaseService = DatabaseService();
  final SettingsService _settingsService = SettingsService();
  final NotificationService _notificationService = NotificationService();
  // final EmailService _emailService = EmailService(); // Закоментовано, щоб уникнути попередження

  /// Створення нагадування
  Future<Reminder> createReminder({
    required String employeeId,
    required ReminderCategory category,
    required String title,
    required String message,
    required DateTime scheduledFor,
    required DateTime relatedDate,
  }) async {
    try {
      final reminder = Reminder(
        employeeId: employeeId,
        type: ReminderType.missingTimesheet, // Додано тип нагадування
        category: category,
        title: title,
        message: message,
        scheduledFor: scheduledFor,
        relatedDate: relatedDate,
      );

      await _databaseService.insertReminder(reminder);

      // Планування нагадування
      await _scheduleReminder(reminder);

      return reminder;
    } catch (e) {
      debugPrint('Error creating reminder: $e');
      rethrow;
    }
  }

  /// Оновлення нагадування
  Future<void> updateReminder(Reminder reminder) async {
    try {
      await _databaseService.updateReminder(reminder);

      // Оновлення запланованого нагадування
      await _cancelReminder(reminder.id);

      if (reminder.status == ReminderStatus.active ||
          reminder.status == ReminderStatus.snoozed) {
        await _scheduleReminder(reminder);
      }
    } catch (e) {
      debugPrint('Error updating reminder: $e');
      rethrow;
    }
  }

  /// Видалення нагадування
  Future<void> deleteReminder(String id) async {
    try {
      await _databaseService.deleteReminder(id);

      // Скасування запланованого нагадування
      await _cancelReminder(id);
    } catch (e) {
      debugPrint('Error deleting reminder: $e');
      rethrow;
    }
  }

  /// Отримання всіх нагадувань для працівника
  Future<List<Reminder>> getRemindersForEmployee(String employeeId) async {
    try {
      // Заглушка для отримання нагадувань
      debugPrint('Getting reminders for employee: $employeeId');
      return [];
    } catch (e) {
      debugPrint('Error getting reminders for employee: $e');
      return [];
    }
  }

  /// Отримання активних нагадувань для працівника
  Future<List<Reminder>> getActiveRemindersForEmployee(
    String employeeId,
  ) async {
    try {
      // Заглушка для отримання активних нагадувань
      debugPrint('Getting active reminders for employee: $employeeId');
      return [];
    } catch (e) {
      debugPrint('Error getting active reminders for employee: $e');
      return [];
    }
  }

  /// Відкладення нагадування
  Future<void> snoozeReminder(String id, Duration duration) async {
    try {
      // Заглушка для отримання нагадування за ідентифікатором
      debugPrint('Getting reminder by id: $id');
      final reminder = null;

      if (reminder == null) {
        throw Exception('Reminder not found');
      }

      final now = DateTime.now();
      final newScheduledFor = now.add(duration);

      final updatedReminder = reminder.copyWith(
        scheduledFor: newScheduledFor,
        status: ReminderStatus.snoozed,
        updatedAt: now,
      );

      await updateReminder(updatedReminder);
    } catch (e) {
      debugPrint('Error snoozing reminder: $e');
      rethrow;
    }
  }

  /// Закриття нагадування
  Future<void> dismissReminder(String id) async {
    try {
      // Заглушка для отримання нагадування за ідентифікатором
      debugPrint('Getting reminder by id: $id');
      final reminder = null;

      if (reminder == null) {
        throw Exception('Reminder not found');
      }

      final updatedReminder = reminder.copyWith(
        status: ReminderStatus.dismissed,
        updatedAt: DateTime.now(),
      );

      await updateReminder(updatedReminder);
    } catch (e) {
      debugPrint('Error dismissing reminder: $e');
      rethrow;
    }
  }

  /// Планування нагадування
  Future<void> _scheduleReminder(Reminder reminder) async {
    try {
      // Отримання налаштувань нагадувань
      final settings = await _settingsService.getReminderSettings();

      // Якщо нагадування вимкнені, не плануємо
      if (!settings.enabled) {
        return;
      }

      // Перевірка, чи не є день вихідним
      if (settings.skipHolidays &&
          date_utils.isWeekend(reminder.scheduledFor)) {
        debugPrint('Skipping reminder for weekend: ${reminder.scheduledFor}');
        return;
      }

      // Перевірка, чи не є день святковим
      if (settings.skipHolidays) {
        final isHolidayDay = await date_utils.isHoliday(reminder.scheduledFor);
        if (isHolidayDay) {
          debugPrint('Skipping reminder for holiday: ${reminder.scheduledFor}');
          return;
        }
      }

      // Планування нагадування через різні канали
      for (final type in settings.reminderTypes) {
        await _scheduleReminderByType(reminder, type);
      }
    } catch (e) {
      debugPrint('Error scheduling reminder: $e');
    }
  }

  /// Планування нагадування за типом
  Future<void> _scheduleReminderByType(
    Reminder reminder,
    NotificationType notificationType,
  ) async {
    try {
      switch (notificationType) {
        case NotificationType.inApp:
          await _scheduleInAppReminder(reminder);
          break;
        case NotificationType.email:
          await _scheduleEmailReminder(reminder);
          break;
        case NotificationType.push:
          await _schedulePushReminder(reminder);
          break;
      }
    } catch (e) {
      debugPrint('Error scheduling reminder by type: $e');
    }
  }

  /// Планування внутрішнього нагадування
  Future<void> _scheduleInAppReminder(Reminder reminder) async {
    try {
      // Заглушка для планування нагадування
      debugPrint('Scheduling in-app reminder: ${reminder.title}');
    } catch (e) {
      debugPrint('Error scheduling in-app reminder: $e');
    }
  }

  /// Планування нагадування електронною поштою
  Future<void> _scheduleEmailReminder(Reminder reminder) async {
    try {
      // Заглушка для отримання електронної пошти працівника
      debugPrint(
        'Scheduling email reminder for employee: ${reminder.employeeId}',
      );

      // Планування відправки електронної пошти
      // Примітка: для справжньої реалізації потрібен сервер для відправки електронної пошти
      // Тут ми просто логуємо, що нагадування заплановано
      debugPrint(
        'Email reminder scheduled for employee ${reminder.employeeId} at ${reminder.scheduledFor}',
      );
    } catch (e) {
      debugPrint('Error scheduling email reminder: $e');
    }
  }

  /// Планування push-нагадування
  Future<void> _schedulePushReminder(Reminder reminder) async {
    try {
      // Для справжньої реалізації потрібен Firebase Cloud Messaging або інший сервіс
      // Тут ми використовуємо локальні повідомлення як заміну
      await _scheduleInAppReminder(reminder);
    } catch (e) {
      debugPrint('Error scheduling push reminder: $e');
    }
  }

  /// Скасування нагадування
  Future<void> _cancelReminder(String id) async {
    try {
      // Заглушка для скасування нагадування
      debugPrint('Canceling reminder: $id');
    } catch (e) {
      debugPrint('Error canceling reminder: $e');
    }
  }

  /// Планування нагадувань на основі налаштувань
  Future<void> scheduleReminders(ReminderSettings settings) async {
    try {
      // Скасування всіх запланованих нагадувань
      await _notificationService.cancelAllNotifications();

      // Якщо нагадування вимкнені, не плануємо
      if (!settings.enabled) {
        return;
      }

      // Отримання всіх активних нагадувань
      // Отримання всіх активних нагадувань
      // Тут має бути код для отримання активних нагадувань
      final reminders = <Reminder>[];

      // Планування нагадувань
      for (final reminder in reminders) {
        await _scheduleReminder(reminder);
      }
    } catch (e) {
      debugPrint('Error scheduling reminders: $e');
    }
  }

  /// Створення нагадування про відсутність даних у табелі
  Future<void> createMissingTimesheetReminder({
    required String employeeId,
    required DateTime date,
  }) async {
    try {
      // Перевірка, чи не є день вихідним
      if (date_utils.isWeekend(date)) {
        debugPrint('Skipping missing timesheet reminder for weekend: $date');
        return;
      }

      // Перевірка, чи не є день святковим
      final isHolidayDay = await date_utils.isHoliday(date);
      if (isHolidayDay) {
        debugPrint('Skipping missing timesheet reminder for holiday: $date');
        return;
      }

      // Отримання налаштувань нагадувань
      final settings = await _settingsService.getReminderSettings();

      // Якщо нагадування вимкнені, не створюємо
      if (!settings.enabled) {
        return;
      }

      // Створення часу нагадування
      final now = DateTime.now();
      final scheduledFor = DateTime(
        now.year,
        now.month,
        now.day,
        settings.reminderTime.hour,
        settings.reminderTime.minute,
      );

      // Якщо час нагадування вже минув, плануємо на наступний день
      final finalScheduledFor =
          scheduledFor.isBefore(now)
              ? scheduledFor.add(const Duration(days: 1))
              : scheduledFor;

      // Перевірка, чи відповідає день тижня налаштуванням
      final weekday = finalScheduledFor.weekday;

      if (settings.frequency == ReminderFrequency.weekdays &&
          (weekday > 5 || weekday < 1)) {
        // Якщо це вихідний, а нагадування налаштовані тільки для робочих днів
        return;
      }

      if (settings.frequency == ReminderFrequency.custom &&
          !settings.reminderDays.contains(weekday)) {
        // Якщо це день, не вибраний у налаштуваннях
        return;
      }

      // Створення нагадування
      await createReminder(
        employeeId: employeeId,
        category: ReminderCategory.timesheet,
        title: 'Нагадування про заповнення табеля',
        message:
            'Не забудьте заповнити табель за ${date.day}.${date.month}.${date.year}',
        scheduledFor: finalScheduledFor,
        relatedDate: date,
      );
    } catch (e) {
      debugPrint('Error creating missing timesheet reminder: $e');
    }
  }

  /// Перевірка та створення нагадувань про відсутність даних у табелі
  Future<void> checkAndCreateMissingTimesheetReminders(
    String employeeId,
  ) async {
    try {
      // Отримання поточної дати
      final now = DateTime.now();
      final yesterday = DateTime(now.year, now.month, now.day - 1);

      // Перевірка, чи є дані за вчора
      // Перевірка, чи є дані за вчора
      // Тут має бути код для перевірки наявності даних
      final hasData = false; // Заглушка

      // Якщо даних немає, створюємо нагадування
      if (!hasData) {
        await createMissingTimesheetReminder(
          employeeId: employeeId,
          date: yesterday,
        );
      }
    } catch (e) {
      debugPrint('Error checking and creating missing timesheet reminders: $e');
    }
  }
}
