import '../models/models.dart';
import 'database_service.dart';

/// Сервіс для розрахунків
class CalculationService {
  final DatabaseService _databaseService = DatabaseService();

  /// Розрахунок заробітної плати за робочий день
  Future<double> calculateDayWage(WorkDay workDay, Employee employee) async {
    // Отримання налаштувань надбавок
    final BonusSettings bonusSettings =
        await _databaseService.getBonusSettings();

    // Отримання активних персональних надбавок працівника
    final List<EmployeeBonus> activeBonuses = await _databaseService
        .getActiveEmployeeBonuses(employee.id, workDay.date);

    // Базова оплата за день
    double baseWage = workDay.hoursWorked * employee.hourlyRate;

    // Надбавка за тип дня
    double dayTypeBonus = 0.0;
    switch (workDay.dayType) {
      case DayType.regular:
        dayTypeBonus = 0.0;
        break;
      case DayType.saturday:
        dayTypeBonus = baseWage * bonusSettings.saturdayBonus;
        break;
      case DayType.sunday:
        dayTypeBonus = baseWage * bonusSettings.sundayBonus;
        break;
      case DayType.holiday:
        dayTypeBonus = baseWage * bonusSettings.holidayBonus;
        break;
    }

    // Надбавка за тип зміни
    double shiftTypeBonus = 0.0;
    if (workDay.shiftType == ShiftType.night) {
      shiftTypeBonus = baseWage * bonusSettings.nightShiftBonus;
    }

    // Надбавка за стаж роботи
    double experienceBonus = 0.0;
    if (employee.experienceYears != null && employee.experienceYears! > 0) {
      experienceBonus =
          baseWage *
          (employee.experienceYears! * bonusSettings.experienceBonus);
    }

    // Надбавка за кваліфікацію
    double qualificationBonus = 0.0;
    if (employee.qualificationLevel != null &&
        employee.qualificationLevel! > 0) {
      qualificationBonus =
          baseWage *
          (employee.qualificationLevel! * bonusSettings.qualificationBonus);
    }

    // Персональні надбавки
    double personalBonuses = 0.0;
    for (var bonus in activeBonuses) {
      switch (bonus.calculationType) {
        case BonusCalculationType.percentage:
          personalBonuses += baseWage * bonus.value;
          break;
        case BonusCalculationType.hourly:
          personalBonuses += workDay.hoursWorked * bonus.value;
          break;
        case BonusCalculationType.daily:
          personalBonuses += bonus.value;
          break;
        case BonusCalculationType.monthly:
          // Розподіляємо місячну надбавку на кількість робочих днів у місяці (22 дні в середньому)
          personalBonuses += bonus.value / 22.0;
          break;
      }
    }

    // Загальна сума за день
    return baseWage +
        dayTypeBonus +
        shiftTypeBonus +
        experienceBonus +
        qualificationBonus +
        personalBonuses;
  }

  /// Розрахунок заробітної плати за табель
  Future<Map<String, dynamic>> calculateTimesheetWage(
    Timesheet timesheet,
  ) async {
    // Отримання працівника
    final Employee? employee = await _databaseService.getEmployee(
      timesheet.employeeId,
    );
    if (employee == null) {
      throw Exception('Працівника не знайдено');
    }

    // Отримання налаштувань надбавок
    final BonusSettings bonusSettings =
        await _databaseService.getBonusSettings();

    // Отримання всіх персональних надбавок працівника
    final List<EmployeeBonus> allBonuses = await _databaseService
        .getEmployeeBonusesByEmployee(employee.id);

    // Розрахунок заробітної плати за кожен день
    double totalWage = 0.0;
    double regularHoursWage = 0.0;
    double saturdayHoursWage = 0.0;
    double sundayHoursWage = 0.0;
    double holidayHoursWage = 0.0;
    double nightShiftHoursWage = 0.0;
    double hazardousHoursWage = 0.0;
    double overtimeHoursWage = 0.0;
    double experienceBonusWage = 0.0;
    double qualificationBonusWage = 0.0;
    double personalBonusesWage = 0.0;

    for (var workDay in timesheet.workDays) {
      double dayWage = await calculateDayWage(workDay, employee);
      totalWage += dayWage;

      // Розподіл за типами днів
      double baseWage = workDay.hoursWorked * employee.hourlyRate;

      switch (workDay.dayType) {
        case DayType.regular:
          regularHoursWage += baseWage;
          break;
        case DayType.saturday:
          saturdayHoursWage += baseWage;
          break;
        case DayType.sunday:
          sundayHoursWage += baseWage;
          break;
        case DayType.holiday:
          holidayHoursWage += baseWage;
          break;
      }

      // Розподіл за типами змін
      if (workDay.shiftType == ShiftType.night) {
        nightShiftHoursWage += baseWage;
      }

      // Надбавка за стаж роботи
      if (employee.experienceYears != null && employee.experienceYears! > 0) {
        experienceBonusWage +=
            baseWage *
            (employee.experienceYears! * bonusSettings.experienceBonus);
      }

      // Надбавка за кваліфікацію
      if (employee.qualificationLevel != null &&
          employee.qualificationLevel! > 0) {
        qualificationBonusWage +=
            baseWage *
            (employee.qualificationLevel! * bonusSettings.qualificationBonus);
      }

      // Персональні надбавки
      List<EmployeeBonus> activeBonuses =
          allBonuses.where((bonus) => bonus.isActiveOn(workDay.date)).toList();
      for (var bonus in activeBonuses) {
        switch (bonus.calculationType) {
          case BonusCalculationType.percentage:
            personalBonusesWage += baseWage * bonus.value;
            break;
          case BonusCalculationType.hourly:
            personalBonusesWage += workDay.hoursWorked * bonus.value;
            break;
          case BonusCalculationType.daily:
            personalBonusesWage += bonus.value;
            break;
          case BonusCalculationType.monthly:
            // Розподіляємо місячну надбавку на кількість робочих днів у місяці (22 дні в середньому)
            personalBonusesWage += bonus.value / 22.0;
            break;
        }
      }
    }

    // Результат розрахунків
    return {
      'totalWage': totalWage,
      'regularHoursWage': regularHoursWage,
      'saturdayHoursWage': saturdayHoursWage,
      'sundayHoursWage': sundayHoursWage,
      'holidayHoursWage': holidayHoursWage,
      'nightShiftHoursWage': nightShiftHoursWage,
      'hazardousHoursWage': hazardousHoursWage,
      'overtimeHoursWage': overtimeHoursWage,
      'experienceBonusWage': experienceBonusWage,
      'qualificationBonusWage': qualificationBonusWage,
      'personalBonusesWage': personalBonusesWage,
      'totalHours': timesheet.totalHours,
      'regularHours': timesheet.getHoursByDayType(DayType.regular),
      'saturdayHours': timesheet.getHoursByDayType(DayType.saturday),
      'sundayHours': timesheet.getHoursByDayType(DayType.sunday),
      'holidayHours': timesheet.getHoursByDayType(DayType.holiday),
      'dayShiftHours': timesheet.getHoursByShiftType(ShiftType.day),
      'nightShiftHours': timesheet.getHoursByShiftType(ShiftType.night),
      // Додаємо інформацію про надбавки
      'saturdayBonus': bonusSettings.saturdayBonus,
      'sundayBonus': bonusSettings.sundayBonus,
      'holidayBonus': bonusSettings.holidayBonus,
      'nightShiftBonus': bonusSettings.nightShiftBonus,
      'hazardousBonus': bonusSettings.hazardousBonus,
      'overtimeBonus': bonusSettings.overtimeBonus,
      'experienceBonus': bonusSettings.experienceBonus,
      'qualificationBonus': bonusSettings.qualificationBonus,
      'employeeExperienceYears': employee.experienceYears ?? 0,
      'employeeQualificationLevel': employee.qualificationLevel ?? 0,
      'employeePersonalBonuses': allBonuses.length,
    };
  }
}
