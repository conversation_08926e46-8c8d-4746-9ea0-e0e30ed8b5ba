import 'package:intl/intl.dart';
import '../models/models.dart';
import 'database_service.dart';

/// Сервіс для роботи з календарем
class CalendarService {
  final DatabaseService _databaseService = DatabaseService();

  /// Визначення типу дня за датою
  Future<DayType> getDayType(DateTime date, String country) async {
    // Перевірка, чи є дата святковим днем
    bool isHolidayDay = await _databaseService.isHoliday(date, country);
    if (isHolidayDay) {
      return DayType.holiday;
    }

    // Визначення дня тижня
    int weekday = date.weekday; // 1 - понеділок, 7 - неділя

    if (weekday == 6) {
      return DayType.saturday;
    } else if (weekday == 7) {
      return DayType.sunday;
    } else {
      return DayType.regular;
    }
  }

  /// Отримання назви дня тижня
  String getWeekdayName(DateTime date, String locale) {
    return DateFormat.EEEE(locale).format(date);
  }

  /// Отримання короткої назви дня тижня
  String getShortWeekdayName(DateTime date, String locale) {
    return DateFormat.E(locale).format(date);
  }

  /// Отримання назви місяця
  String getMonthName(DateTime date, String locale) {
    return DateFormat.MMMM(locale).format(date);
  }

  /// Отримання короткої назви місяця
  String getShortMonthName(DateTime date, String locale) {
    return DateFormat.MMM(locale).format(date);
  }

  /// Форматування дати
  String formatDate(
    DateTime date,
    String locale, {
    String format = 'dd.MM.yyyy',
  }) {
    return DateFormat(format, locale).format(date);
  }

  /// Отримання першого дня місяця
  DateTime getFirstDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Отримання останнього дня місяця
  DateTime getLastDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0);
  }

  /// Отримання першого дня тижня
  DateTime getFirstDayOfWeek(DateTime date, {int startOfWeek = 1}) {
    // startOfWeek: 1 - понеділок, 7 - неділя
    int weekday = date.weekday;
    return date.subtract(Duration(days: (weekday - startOfWeek) % 7));
  }

  /// Отримання останнього дня тижня
  DateTime getLastDayOfWeek(DateTime date, {int startOfWeek = 1}) {
    // startOfWeek: 1 - понеділок, 7 - неділя
    DateTime firstDay = getFirstDayOfWeek(date, startOfWeek: startOfWeek);
    return firstDay.add(const Duration(days: 6));
  }

  /// Імпорт святкових днів для країни
  Future<void> importHolidays(String country) async {
    // Тут можна реалізувати імпорт святкових днів з API або з файлу
    // Для прикладу, додамо кілька святкових днів для України
    if (country == 'UA') {
      await _addUkrainianHolidays();
    } else if (country == 'CZ') {
      await _addCzechHolidays();
    } else if (country == 'SK') {
      await _addSlovakHolidays();
    } else if (country == 'PL') {
      await _addPolishHolidays();
    }
  }

  /// Додавання українських святкових днів
  Future<void> _addUkrainianHolidays() async {
    final int currentYear = DateTime.now().year;

    // Список українських свят
    final List<Holiday> holidays = [
      // Додаткові свята для тестування
      Holiday(
        name: 'Сьогоднішнє свято',
        date: DateTime.now(),
        country: 'UA',
        isRecurring: false,
      ),
      Holiday(
        name: 'Свято в понеділок',
        date: DateTime.now().add(Duration(days: 3 - DateTime.now().weekday)),
        country: 'UA',
        isRecurring: false,
      ),
      // Фіксовані щорічні свята
      Holiday(
        name: 'Новий рік',
        date: DateTime(currentYear, 1, 1),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'Різдво Христове',
        date: DateTime(currentYear, 1, 7),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'Міжнародний жіночий день',
        date: DateTime(currentYear, 3, 8),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'День праці',
        date: DateTime(currentYear, 5, 1),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'День перемоги над нацизмом у Другій світовій війні',
        date: DateTime(currentYear, 5, 9),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'День Конституції України',
        date: DateTime(currentYear, 6, 28),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'День Незалежності України',
        date: DateTime(currentYear, 8, 24),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'День захисників і захисниць України',
        date: DateTime(currentYear, 10, 14),
        country: 'UA',
        isRecurring: true,
      ),
      Holiday(
        name: 'Різдво Христове (за григоріанським календарем)',
        date: DateTime(currentYear, 12, 25),
        country: 'UA',
        isRecurring: true,
      ),
    ];

    // Додавання свят до бази даних
    for (var holiday in holidays) {
      await _databaseService.insertHoliday(holiday);
    }
  }

  /// Додавання чеських святкових днів
  Future<void> _addCzechHolidays() async {
    final int currentYear = DateTime.now().year;

    // Список чеських свят
    final List<Holiday> holidays = [
      // Фіксовані щорічні свята
      Holiday(
        name: 'Nový rok',
        date: DateTime(currentYear, 1, 1),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Den obnovy samostatného českého státu',
        date: DateTime(currentYear, 1, 1),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Velký pátek',
        date: DateTime(currentYear, 4, 7), // Приблизна дата для 2023 року
        country: 'CZ',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Velikonoční pondělí',
        date: DateTime(currentYear, 4, 10), // Приблизна дата для 2023 року
        country: 'CZ',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Svátek práce',
        date: DateTime(currentYear, 5, 1),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Den vítězství',
        date: DateTime(currentYear, 5, 8),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Den slovanských věrozvěstů Cyrila a Metoděje',
        date: DateTime(currentYear, 7, 5),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Den upálení mistra Jana Husa',
        date: DateTime(currentYear, 7, 6),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Den české státnosti',
        date: DateTime(currentYear, 9, 28),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Den vzniku samostatného československého státu',
        date: DateTime(currentYear, 10, 28),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Den boje za svobodu a demokracii a Mezinárodní den studentstva',
        date: DateTime(currentYear, 11, 17),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: 'Štědrý den',
        date: DateTime(currentYear, 12, 24),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: '1. svátek vánoční',
        date: DateTime(currentYear, 12, 25),
        country: 'CZ',
        isRecurring: true,
      ),
      Holiday(
        name: '2. svátek vánoční',
        date: DateTime(currentYear, 12, 26),
        country: 'CZ',
        isRecurring: true,
      ),
    ];

    // Додавання свят до бази даних
    for (var holiday in holidays) {
      await _databaseService.insertHoliday(holiday);
    }
  }

  /// Додавання словацьких святкових днів
  Future<void> _addSlovakHolidays() async {
    final int currentYear = DateTime.now().year;

    // Список словацьких свят
    final List<Holiday> holidays = [
      // Фіксовані щорічні свята
      Holiday(
        name: 'Deň vzniku Slovenskej republiky',
        date: DateTime(currentYear, 1, 1),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Zjavenie Pána (Traja králi)',
        date: DateTime(currentYear, 1, 6),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Veľký piatok',
        date: DateTime(currentYear, 4, 7), // Приблизна дата для 2023 року
        country: 'SK',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Veľkonočný pondelok',
        date: DateTime(currentYear, 4, 10), // Приблизна дата для 2023 року
        country: 'SK',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Sviatok práce',
        date: DateTime(currentYear, 5, 1),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Deň víťazstva nad fašizmom',
        date: DateTime(currentYear, 5, 8),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Sviatok svätého Cyrila a Metoda',
        date: DateTime(currentYear, 7, 5),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Výročie Slovenského národného povstania',
        date: DateTime(currentYear, 8, 29),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Deň Ústavy Slovenskej republiky',
        date: DateTime(currentYear, 9, 1),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Sviatok Panny Márie Sedembolestnej, patrónky Slovenska',
        date: DateTime(currentYear, 9, 15),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Sviatok všetkých svätých',
        date: DateTime(currentYear, 11, 1),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Deň boja za slobodu a demokraciu',
        date: DateTime(currentYear, 11, 17),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Štedrý deň',
        date: DateTime(currentYear, 12, 24),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Prvý sviatok vianočný',
        date: DateTime(currentYear, 12, 25),
        country: 'SK',
        isRecurring: true,
      ),
      Holiday(
        name: 'Druhý sviatok vianočný',
        date: DateTime(currentYear, 12, 26),
        country: 'SK',
        isRecurring: true,
      ),
    ];

    // Додавання свят до бази даних
    for (var holiday in holidays) {
      await _databaseService.insertHoliday(holiday);
    }
  }

  /// Додавання польських святкових днів
  Future<void> _addPolishHolidays() async {
    final int currentYear = DateTime.now().year;

    // Список польських свят
    final List<Holiday> holidays = [
      // Фіксовані щорічні свята
      Holiday(
        name: 'Nowy Rok',
        date: DateTime(currentYear, 1, 1),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Święto Trzech Króli',
        date: DateTime(currentYear, 1, 6),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Wielki Piątek',
        date: DateTime(currentYear, 4, 7), // Приблизна дата для 2023 року
        country: 'PL',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Poniedziałek Wielkanocny',
        date: DateTime(currentYear, 4, 10), // Приблизна дата для 2023 року
        country: 'PL',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Święto Pracy',
        date: DateTime(currentYear, 5, 1),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Święto Konstytucji 3 Maja',
        date: DateTime(currentYear, 5, 3),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Zielone Świątki',
        date: DateTime(currentYear, 5, 28), // Приблизна дата для 2023 року
        country: 'PL',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Boże Ciało',
        date: DateTime(currentYear, 6, 8), // Приблизна дата для 2023 року
        country: 'PL',
        isRecurring: false, // Змінюється щороку
      ),
      Holiday(
        name: 'Wniebowzięcie Najświętszej Maryi Panny',
        date: DateTime(currentYear, 8, 15),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Wszystkich Świętych',
        date: DateTime(currentYear, 11, 1),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Narodowe Święto Niepodległości',
        date: DateTime(currentYear, 11, 11),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Wigilia Bożego Narodzenia',
        date: DateTime(currentYear, 12, 24),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Boże Narodzenie (pierwszy dzień)',
        date: DateTime(currentYear, 12, 25),
        country: 'PL',
        isRecurring: true,
      ),
      Holiday(
        name: 'Boże Narodzenie (drugi dzień)',
        date: DateTime(currentYear, 12, 26),
        country: 'PL',
        isRecurring: true,
      ),
    ];

    // Додавання свят до бази даних
    for (var holiday in holidays) {
      await _databaseService.insertHoliday(holiday);
    }
  }
}
