^D:\TABEL\TABEL_APP\BUILD\WINDOWS\X64\PDFIUM-DOWNLOAD\CMAKEFILES\AC93FC81CC35F045658F8CFE78E06165\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Tabel/tabel_app/build/windows/x64/pdfium-download -BD:/Tabel/tabel_app/build/windows/x64/pdfium-download --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/Tabel/tabel_app/build/windows/x64/pdfium-download/pdfium-download.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
