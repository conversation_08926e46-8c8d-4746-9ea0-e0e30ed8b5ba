^D:\TABEL\TABEL_APP\BUILD\WINDOWS\X64\PDFIUM-DOWNLOAD\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Tabel/tabel_app/build/windows/x64/pdfium-download -BD:/Tabel/tabel_app/build/windows/x64/pdfium-download --check-stamp-file D:/Tabel/tabel_app/build/windows/x64/pdfium-download/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
