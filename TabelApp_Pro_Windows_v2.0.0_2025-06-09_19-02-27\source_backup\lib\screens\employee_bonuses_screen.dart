import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../services/database_service.dart';
import '../utils/utils.dart';

/// Екран управління персональними надбавками працівника
class EmployeeBonusesScreen extends StatefulWidget {
  /// Працівник
  final Employee employee;

  /// Конструктор
  const EmployeeBonusesScreen({Key? key, required this.employee})
    : super(key: key);

  @override
  State<EmployeeBonusesScreen> createState() => _EmployeeBonusesScreenState();
}

class _EmployeeBonusesScreenState extends State<EmployeeBonusesScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  List<EmployeeBonus> _bonuses = [];
  bool _isLoading = true;
  String? _errorMessage;

  // Контролери для форми
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  BonusType _selectedType = BonusType.personal;
  BonusCalculationType _selectedCalculationType =
      BonusCalculationType.percentage;
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _loadBonuses();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _valueController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// Завантаження надбавок працівника
  Future<void> _loadBonuses() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final bonuses = await _databaseService.getEmployeeBonusesByEmployee(
        widget.employee.id,
      );

      setState(() {
        _bonuses = bonuses;
      });
    } catch (e) {
      setState(() {
        _errorMessage =
            '${AppLocalizations.of(context).translate('error')}: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Відображення діалогу додавання/редагування надбавки
  Future<void> _showBonusDialog([EmployeeBonus? bonus]) async {
    // Скидання форми
    _formKey.currentState?.reset();

    // Заповнення полів, якщо редагуємо існуючу надбавку
    if (bonus != null) {
      _nameController.text = bonus.name;

      // Залежно від типу нарахування відображаємо значення
      if (bonus.calculationType == BonusCalculationType.percentage) {
        _valueController.text = (bonus.value * 100).toString();
      } else {
        _valueController.text = bonus.value.toString();
      }

      _descriptionController.text = bonus.description ?? '';
      _selectedType = bonus.type;
      _selectedCalculationType = bonus.calculationType;
      _startDate = bonus.startDate;
      _endDate = bonus.endDate;
    } else {
      _nameController.clear();
      _valueController.clear();
      _descriptionController.clear();
      _selectedType = BonusType.personal;
      _selectedCalculationType = BonusCalculationType.percentage;
      _startDate = DateTime.now();
      _endDate = null;
    }

    await showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              bonus == null
                  ? AppLocalizations.of(context).translate('add_bonus')
                  : AppLocalizations.of(context).translate('edit_bonus'),
            ),
            content: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Назва надбавки
                    TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(
                          context,
                        ).translate('bonus_name'),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return AppLocalizations.of(
                            context,
                          ).translate('required_field');
                        }
                        return null;
                      },
                    ),

                    // Тип нарахування надбавки
                    DropdownButtonFormField<BonusCalculationType>(
                      value: _selectedCalculationType,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(
                          context,
                        ).translate('calculation_type'),
                      ),
                      items:
                          BonusCalculationType.values.map((type) {
                            return DropdownMenuItem<BonusCalculationType>(
                              value: type,
                              child: Text(_getBonusCalculationTypeName(type)),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCalculationType = value!;
                        });
                      },
                    ),

                    // Значення надбавки
                    TextFormField(
                      controller: _valueController,
                      decoration: InputDecoration(
                        labelText: _getValueFieldLabel(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return AppLocalizations.of(
                            context,
                          ).translate('required_field');
                        }
                        final double? numValue = double.tryParse(value);
                        if (numValue == null || numValue <= 0) {
                          return AppLocalizations.of(
                            context,
                          ).translate('invalid_hourly_rate');
                        }
                        return null;
                      },
                    ),

                    // Опис надбавки
                    TextFormField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        labelText:
                            '${AppLocalizations.of(context).translate('bonus_description')} (${AppLocalizations.of(context).translate('optional')})',
                      ),
                      maxLines: 2,
                    ),

                    // Тип надбавки
                    DropdownButtonFormField<BonusType>(
                      value: _selectedType,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(
                          context,
                        ).translate('bonus_type'),
                      ),
                      items:
                          BonusType.values.map((type) {
                            return DropdownMenuItem<BonusType>(
                              value: type,
                              child: Text(_getBonusTypeName(type)),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedType = value!;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Дата початку
                    ListTile(
                      title: Text(
                        AppLocalizations.of(
                          context,
                        ).translate('bonus_start_date'),
                      ),
                      subtitle: Text(
                        DateFormat('dd.MM.yyyy').format(_startDate),
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: _startDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null && picked != _startDate) {
                          setState(() {
                            _startDate = picked;
                          });
                        }
                      },
                    ),

                    // Дата закінчення
                    ListTile(
                      title: Text(
                        '${AppLocalizations.of(context).translate('bonus_end_date')} (${AppLocalizations.of(context).translate('optional')})',
                      ),
                      subtitle:
                          _endDate != null
                              ? Text(DateFormat('dd.MM.yyyy').format(_endDate!))
                              : Text(
                                AppLocalizations.of(
                                  context,
                                ).translate('indefinite'),
                              ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_endDate != null)
                            IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _endDate = null;
                                });
                              },
                            ),
                          const Icon(Icons.calendar_today),
                        ],
                      ),
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate:
                              _endDate ??
                              DateTime.now().add(const Duration(days: 365)),
                          firstDate: _startDate,
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            _endDate = picked;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(AppLocalizations.of(context).translate('cancel')),
              ),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    // Отримуємо значення залежно від типу нарахування
                    final double value =
                        _selectedCalculationType ==
                                BonusCalculationType.percentage
                            ? double.parse(_valueController.text) / 100
                            : double.parse(_valueController.text);

                    // Закриваємо діалог перед асинхронною операцією
                    Navigator.of(context).pop();

                    // Виконуємо асинхронні операції після закриття діалогу
                    Future.microtask(() async {
                      if (bonus == null) {
                        // Створення нової надбавки
                        final newBonus = EmployeeBonus(
                          employeeId: widget.employee.id,
                          type: _selectedType,
                          value: value,
                          name: _nameController.text,
                          description:
                              _descriptionController.text.isEmpty
                                  ? null
                                  : _descriptionController.text,
                          startDate: _startDate,
                          endDate: _endDate,
                          calculationType: _selectedCalculationType,
                        );

                        await _databaseService.insertEmployeeBonus(newBonus);
                      } else {
                        // Оновлення існуючої надбавки
                        final updatedBonus = EmployeeBonus(
                          id: bonus.id,
                          employeeId: widget.employee.id,
                          type: _selectedType,
                          value: value,
                          name: _nameController.text,
                          description:
                              _descriptionController.text.isEmpty
                                  ? null
                                  : _descriptionController.text,
                          startDate: _startDate,
                          endDate: _endDate,
                          calculationType: _selectedCalculationType,
                        );

                        await _databaseService.updateEmployeeBonus(
                          updatedBonus,
                        );
                      }

                      // Оновлення списку надбавок
                      await _loadBonuses();
                    });
                  }
                },
                child: Text(
                  bonus == null
                      ? AppLocalizations.of(context).translate('add')
                      : AppLocalizations.of(context).translate('save'),
                ),
              ),
            ],
          ),
    );
  }

  /// Видалення надбавки
  Future<void> _deleteBonus(EmployeeBonus bonus) async {
    final bool confirm =
        await showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: Text(AppLocalizations.of(context).translate('delete')),
                content: Text(
                  '${AppLocalizations.of(context).translate('delete_confirm')} "${bonus.name}"?',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: Text(
                      AppLocalizations.of(context).translate('cancel'),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                    ),
                    child: Text(
                      AppLocalizations.of(context).translate('delete'),
                    ),
                  ),
                ],
              ),
        ) ??
        false;

    if (confirm) {
      await _databaseService.deleteEmployeeBonus(bonus.id);
      await _loadBonuses();
    }
  }

  /// Отримання назви типу надбавки
  String _getBonusTypeName(BonusType type) {
    final localizations = AppLocalizations.of(context);
    switch (type) {
      case BonusType.hazardous:
        return localizations.translate('bonus_type_hazardous');
      case BonusType.experience:
        return localizations.translate('bonus_type_experience');
      case BonusType.qualification:
        return localizations.translate('bonus_type_qualification');
      case BonusType.overtime:
        return localizations.translate('bonus_type_overtime');
      case BonusType.personal:
        return localizations.translate('bonus_type_personal');
      case BonusType.achievement:
        return localizations.translate('bonus_type_achievement');
    }
  }

  /// Отримання назви типу нарахування надбавки
  String _getBonusCalculationTypeName(BonusCalculationType type) {
    final localizations = AppLocalizations.of(context);
    switch (type) {
      case BonusCalculationType.percentage:
        return localizations.translate('calculation_type_percentage');
      case BonusCalculationType.hourly:
        return localizations.translate('calculation_type_hourly');
      case BonusCalculationType.daily:
        return localizations.translate('calculation_type_daily');
      case BonusCalculationType.monthly:
        return localizations.translate('calculation_type_monthly');
    }
  }

  /// Отримання назви поля для значення надбавки
  String _getValueFieldLabel() {
    final localizations = AppLocalizations.of(context);
    final String bonusValue = localizations.translate('bonus_value');

    switch (_selectedCalculationType) {
      case BonusCalculationType.percentage:
        return '$bonusValue (%)';
      case BonusCalculationType.hourly:
        return '$bonusValue ${localizations.translate('value_hourly')}';
      case BonusCalculationType.daily:
        return '$bonusValue ${localizations.translate('value_daily')}';
      case BonusCalculationType.monthly:
        return '$bonusValue ${localizations.translate('value_monthly')}';
    }
  }

  /// Отримання кольору для типу надбавки
  Color _getBonusTypeColor(BonusType type) {
    switch (type) {
      case BonusType.hazardous:
        return Colors.red.shade200;
      case BonusType.experience:
        return Colors.blue.shade200;
      case BonusType.qualification:
        return Colors.green.shade200;
      case BonusType.overtime:
        return Colors.orange.shade200;
      case BonusType.personal:
        return Colors.purple.shade200;
      case BonusType.achievement:
        return Colors.amber.shade200;
    }
  }

  /// Форматування значення надбавки для відображення
  String _formatBonusValue(EmployeeBonus bonus) {
    final localizations = AppLocalizations.of(context);

    switch (bonus.calculationType) {
      case BonusCalculationType.percentage:
        return '${(bonus.value * 100).toStringAsFixed(0)}${localizations.translate('value_percentage')}';
      case BonusCalculationType.hourly:
        return '${bonus.value.toStringAsFixed(2)}${localizations.translate('value_hourly')}';
      case BonusCalculationType.daily:
        return '${bonus.value.toStringAsFixed(2)}${localizations.translate('value_daily')}';
      case BonusCalculationType.monthly:
        return '${bonus.value.toStringAsFixed(2)}${localizations.translate('value_monthly')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${AppLocalizations.of(context).translate('employee_bonuses')}: ${widget.employee.fullName}',
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _bonuses.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(AppLocalizations.of(context).translate('no_bonuses')),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _showBonusDialog(),
                      child: Text(
                        AppLocalizations.of(context).translate('add_bonus'),
                      ),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                itemCount: _bonuses.length,
                itemBuilder: (context, index) {
                  final bonus = _bonuses[index];
                  final bool isActive = bonus.isActiveOn(DateTime.now());

                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: _getBonusTypeColor(bonus.type),
                        child: Text(
                          _formatBonusValue(bonus),
                          style: const TextStyle(
                            color: Colors.black87,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        bonus.name,
                        style: TextStyle(
                          fontWeight:
                              isActive ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(_getBonusTypeName(bonus.type)),
                          Text(
                            '${AppLocalizations.of(context).translate('bonus_period')}: ${DateFormat('dd.MM.yyyy').format(bonus.startDate)} - ${bonus.endDate != null ? DateFormat('dd.MM.yyyy').format(bonus.endDate!) : AppLocalizations.of(context).translate('indefinite')}',
                          ),
                          if (bonus.description != null)
                            Text(
                              bonus.description!,
                              style: const TextStyle(
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _showBonusDialog(bonus),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _deleteBonus(bonus),
                          ),
                        ],
                      ),
                      isThreeLine: bonus.description != null,
                    ),
                  );
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showBonusDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }
}
