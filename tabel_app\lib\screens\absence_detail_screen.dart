import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';

/// Екран для перегляду та редагування деталей відсутності
class AbsenceDetailScreen extends StatefulWidget {
  final Absence? absence;

  const AbsenceDetailScreen({super.key, this.absence});

  @override
  State<AbsenceDetailScreen> createState() => _AbsenceDetailScreenState();
}

class _AbsenceDetailScreenState extends State<AbsenceDetailScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  final AbsenceService _absenceService = AbsenceService();
  final CalendarService _calendarService = CalendarService();
  bool _isLoading = false;
  String? _errorMessage;
  bool _isEditing = false;
  bool _isAdmin = false;
  Employee? _currentEmployee;
  String _countryCode = 'UA';

  @override
  void initState() {
    super.initState();
    _isEditing = widget.absence == null;
    _loadData();
  }

  /// Завантаження даних
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Отримання поточного працівника
      final appState = Provider.of<AppState>(context, listen: false);
      _currentEmployee = appState.currentEmployee;
      _isAdmin = _currentEmployee?.isAdmin ?? false;
      _countryCode = appState.country.code;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка завантаження даних: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final languageCode = appState.language.code;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.absence == null
              ? AppLocalizations.of(context).translate('new_absence')
              : AppLocalizations.of(context).translate('absence_details'),
        ),
        actions: [
          if (widget.absence != null && !_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _canEdit() ? _toggleEdit : null,
              tooltip: AppLocalizations.of(context).translate('edit'),
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _buildForm(languageCode),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// Побудова форми
  Widget _buildForm(String languageCode) {
    final dateFormat = DateFormat('dd.MM.yyyy');

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: FormBuilder(
        key: _formKey,
        enabled: _isEditing,
        initialValue: _getInitialValues(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Тип відсутності
            FormBuilderDropdown<AbsenceType>(
              name: 'type',
              decoration: InputDecoration(
                labelText: AppLocalizations.of(
                  context,
                ).translate('absence_type'),
                border: const OutlineInputBorder(),
              ),
              items:
                  AbsenceType.values.map((type) {
                    return DropdownMenuItem<AbsenceType>(
                      value: type,
                      child: Text(
                        AppLocalizations.of(
                          context,
                        ).translate(type.getLocalizationKey()),
                      ),
                    );
                  }).toList(),
              validator: FormBuilderValidators.required(),
            ),
            const SizedBox(height: 16),
            // Дата початку
            FormBuilderDateTimePicker(
              name: 'startDate',
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).translate('start_date'),
                border: const OutlineInputBorder(),
                suffixIcon: const Icon(Icons.calendar_today),
              ),
              inputType: InputType.date,
              format: dateFormat,
              validator: FormBuilderValidators.required(),
              onChanged: (value) {
                // Якщо дата початку пізніше дати закінчення, оновлюємо дату закінчення
                final endDate = _formKey.currentState?.fields['endDate']?.value;
                if (value != null &&
                    endDate != null &&
                    value.isAfter(endDate)) {
                  _formKey.currentState?.fields['endDate']?.didChange(value);
                }
              },
            ),
            const SizedBox(height: 16),
            // Дата закінчення
            FormBuilderDateTimePicker(
              name: 'endDate',
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).translate('end_date'),
                border: const OutlineInputBorder(),
                suffixIcon: const Icon(Icons.calendar_today),
              ),
              inputType: InputType.date,
              format: dateFormat,
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
                (value) {
                  if (value == null) return null;
                  final startDate =
                      _formKey.currentState?.fields['startDate']?.value;
                  if (startDate != null && value.isBefore(startDate)) {
                    return AppLocalizations.of(
                      context,
                    ).translate('end_date_before_start_date');
                  }
                  return null;
                },
              ]),
            ),
            const SizedBox(height: 16),
            // Коментар
            FormBuilderTextField(
              name: 'comment',
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).translate('comment'),
                border: const OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            // Документ-підстава
            FormBuilderTextField(
              name: 'document',
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).translate('document'),
                border: const OutlineInputBorder(),
                hintText: AppLocalizations.of(
                  context,
                ).translate('document_hint'),
              ),
            ),
            if (widget.absence != null) ...[
              const SizedBox(height: 24),
              // Інформація про статус
              _buildStatusInfo(languageCode),
            ],
            if (_isAdmin &&
                widget.absence != null &&
                widget.absence!.status == AbsenceStatus.planned) ...[
              const SizedBox(height: 24),
              // Поле для коментаря адміністратора
              FormBuilderTextField(
                name: 'approvalComment',
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(
                    context,
                  ).translate('approval_comment'),
                  border: const OutlineInputBorder(),
                  alignLabelWithHint: true,
                ),
                maxLines: 2,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Побудова інформації про статус
  Widget _buildStatusInfo(String languageCode) {
    if (widget.absence == null) return const SizedBox.shrink();

    final absence = widget.absence!;
    final dateFormat = DateFormat('dd.MM.yyyy HH:mm');

    // Визначення кольору статусу
    Color statusColor;
    switch (absence.status) {
      case AbsenceStatus.planned:
        statusColor = Colors.orange;
        break;
      case AbsenceStatus.approved:
        statusColor = Colors.green;
        break;
      case AbsenceStatus.rejected:
        statusColor = Colors.red;
        break;
      case AbsenceStatus.cancelled:
        statusColor = Colors.grey;
        break;
    }

    return Card(
      margin: EdgeInsets.zero,
      color: statusColor.withAlpha(26), // 0.1 * 255 = 26
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: statusColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: statusColor),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).translate('status'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(
                    context,
                  ).translate(absence.status.getLocalizationKey()),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
              ],
            ),
            if (absence.approvedById != null && absence.approvedAt != null) ...[
              const SizedBox(height: 8),
              Text(
                '${AppLocalizations.of(context).translate('processed_by')}: ${absence.approvedById}',
              ),
              Text(
                '${AppLocalizations.of(context).translate('processed_at')}: ${dateFormat.format(absence.approvedAt!)}',
              ),
            ],
            if (absence.approvalComment != null &&
                absence.approvalComment!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '${AppLocalizations.of(context).translate('comment')}: ${absence.approvalComment}',
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Побудова нижньої панелі з кнопками
  Widget _buildBottomBar() {
    if (_isLoading) return const SizedBox.shrink();

    return BottomAppBar(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (widget.absence != null && !_isEditing) ...[
              // Кнопки для адміністратора
              if (_isAdmin && widget.absence!.status == AbsenceStatus.planned)
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: _approveAbsence,
                      icon: const Icon(Icons.check, color: Colors.green),
                      label: Text(
                        AppLocalizations.of(context).translate('approve'),
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _rejectAbsence,
                      icon: const Icon(Icons.close, color: Colors.red),
                      label: Text(
                        AppLocalizations.of(context).translate('reject'),
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                    ),
                  ],
                )
              else if (widget.absence!.status == AbsenceStatus.planned)
                // Кнопка скасування для працівника
                ElevatedButton.icon(
                  onPressed: _cancelAbsence,
                  icon: const Icon(Icons.cancel, color: Colors.grey),
                  label: Text(
                    AppLocalizations.of(context).translate('cancel_request'),
                  ),
                  style: ElevatedButton.styleFrom(foregroundColor: Colors.grey),
                )
              else
                const SizedBox.shrink(),
              const Spacer(),
            ],
            if (_isEditing) ...[
              // Кнопки для редагування
              TextButton(
                onPressed: () {
                  if (widget.absence == null) {
                    Navigator.of(context).pop();
                  } else {
                    setState(() {
                      _isEditing = false;
                    });
                  }
                },
                child: Text(AppLocalizations.of(context).translate('cancel')),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _saveAbsence,
                child: Text(AppLocalizations.of(context).translate('save')),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Отримання початкових значень для форми
  Map<String, dynamic> _getInitialValues() {
    if (widget.absence == null) {
      return {
        'type': AbsenceType.vacation,
        'startDate': DateTime.now(),
        'endDate': DateTime.now(),
      };
    }

    return {
      'type': widget.absence!.type,
      'startDate': widget.absence!.startDate,
      'endDate': widget.absence!.endDate,
      'comment': widget.absence!.comment,
      'document': widget.absence!.document,
      'approvalComment': widget.absence!.approvalComment,
    };
  }

  /// Перевірка можливості редагування
  bool _canEdit() {
    if (widget.absence == null) return true;
    if (_isAdmin) return true;

    // Працівник може редагувати тільки заплановані відсутності
    return widget.absence!.status == AbsenceStatus.planned;
  }

  /// Перемикання режиму редагування
  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  /// Збереження відсутності
  Future<void> _saveAbsence() async {
    if (_formKey.currentState?.saveAndValidate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final formValues = _formKey.currentState!.value;

      if (_currentEmployee == null) {
        throw Exception('Працівника не знайдено');
      }

      if (widget.absence == null) {
        // Створення нової відсутності
        final newAbsence = Absence(
          employeeId: _currentEmployee!.id,
          type: formValues['type'] as AbsenceType,
          startDate: formValues['startDate'] as DateTime,
          endDate: formValues['endDate'] as DateTime,
          status: AbsenceStatus.planned,
          comment: formValues['comment'] as String?,
          document: formValues['document'] as String?,
        );

        await _absenceService.createAbsence(newAbsence);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).translate('absence_created'),
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      } else {
        // Оновлення існуючої відсутності
        final updatedAbsence = widget.absence!.copyWith(
          type: formValues['type'] as AbsenceType,
          startDate: formValues['startDate'] as DateTime,
          endDate: formValues['endDate'] as DateTime,
          comment: formValues['comment'] as String?,
          document: formValues['document'] as String?,
          updatedAt: DateTime.now(),
        );

        await _absenceService.updateAbsence(updatedAbsence);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).translate('absence_updated'),
            ),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {
          _isEditing = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка збереження даних: ${e.toString()}';
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_errorMessage!), backgroundColor: Colors.red),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Затвердження відсутності
  Future<void> _approveAbsence() async {
    if (!_isAdmin || widget.absence == null || _currentEmployee == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final approvalComment =
          _formKey.currentState?.fields['approvalComment']?.value as String?;

      await _absenceService.approveAbsence(
        widget.absence!.id,
        _currentEmployee!.id,
        approvalComment,
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context).translate('absence_approved'),
          ),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка затвердження відсутності: ${e.toString()}';
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_errorMessage!), backgroundColor: Colors.red),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Відхилення відсутності
  Future<void> _rejectAbsence() async {
    if (!_isAdmin || widget.absence == null || _currentEmployee == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final approvalComment =
          _formKey.currentState?.fields['approvalComment']?.value as String?;

      await _absenceService.rejectAbsence(
        widget.absence!.id,
        _currentEmployee!.id,
        approvalComment,
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context).translate('absence_rejected'),
          ),
          backgroundColor: Colors.orange,
        ),
      );
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка відхилення відсутності: ${e.toString()}';
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_errorMessage!), backgroundColor: Colors.red),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Скасування відсутності
  Future<void> _cancelAbsence() async {
    if (widget.absence == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _absenceService.cancelAbsence(widget.absence!.id);

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context).translate('absence_cancelled'),
          ),
          backgroundColor: Colors.grey,
        ),
      );
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _errorMessage = 'Помилка скасування відсутності: ${e.toString()}';
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_errorMessage!), backgroundColor: Colors.red),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
