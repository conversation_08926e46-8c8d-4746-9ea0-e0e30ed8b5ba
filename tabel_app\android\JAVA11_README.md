# Налаштування Java 11 для Flutter проекту

Цей документ містить інструкції з налаштування Java 11 для збірки Flutter проекту на Android.

## Передумови

Для успішної збірки Flutter проекту на Android з використанням Java 11 вам знадобиться:

1. Java Development Kit (JDK) 11
2. Flutter SDK
3. Android SDK

## Встановлення Java 11

### Варіант 1: Завантаження з офіційного сайту

1. Завантажте Java 11 JDK з одного з наступних джерел:
   - [Oracle JDK](https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html) (потребує реєстрації)
   - [AdoptOpenJDK](https://adoptopenjdk.net/?variant=openjdk11&jvmVariant=hotspot)
   - [Amazon Corretto](https://docs.aws.amazon.com/corretto/latest/corretto-11-ug/downloads-list.html)
   - [Azul Zulu](https://www.azul.com/downloads/?version=java-11-lts&package=jdk)

2. Встановіть завантажений JDK, слідуючи інструкціям інсталятора

### Варіант 2: Встановлення через Chocolatey (для Windows)

Якщо у вас встановлено Chocolatey, ви можете встановити Java 11 за допомогою наступної команди:

```
choco install adoptopenjdk11
```

## Налаштування проекту для використання Java 11

### Автоматичне налаштування

1. Запустіть скрипт `setup_java11.bat`:
   ```
   setup_java11.bat
   ```

2. Скрипт автоматично знайде встановлену Java 11, налаштує файл `gradle.properties` і очистить кеш Gradle

3. Після успішного налаштування, ви можете зібрати додаток за допомогою скрипту `build_with_java11.bat`:
   ```
   build_with_java11.bat
   ```

### Ручне налаштування

Якщо автоматичне налаштування не працює, ви можете налаштувати проект вручну:

1. Знайдіть шлях до встановленої Java 11 (наприклад, `C:\Program Files\Java\jdk-11.0.12`)

2. Відкрийте файл `gradle.properties` в директорії `android` і додайте наступний рядок:
   ```
   org.gradle.java.home=C:\\Program Files\\Java\\jdk-11.0.12
   ```
   (замініть шлях на правильний для вашої системи)

3. Очистіть проект і кеш Gradle:
   ```
   flutter clean
   rmdir /s /q android\.gradle
   rmdir /s /q %USERPROFILE%\.gradle\caches
   ```

4. Зберіть додаток:
   ```
   flutter build apk --release
   ```

## Вирішення проблем

Якщо у вас виникають проблеми при збірці додатку, спробуйте наступні кроки:

1. Запустіть скрипт `fix_gradle.bat` для виправлення проблем з Gradle:
   ```
   fix_gradle.bat
   ```

2. Перевірте, що шлях до Java 11 правильний і не містить пробілів або спеціальних символів

3. Спробуйте зібрати debug-версію додатку:
   ```
   flutter build apk --debug
   ```

4. Перевірте, що у вас встановлена сумісна версія Flutter:
   ```
   flutter --version
   ```
   Рекомендується використовувати Flutter 2.5.0 або новіше

5. Перевірте, що у вас встановлені всі необхідні компоненти Android SDK:
   ```
   flutter doctor -v
   ```

## Додаткові ресурси

- [Flutter документація](https://flutter.dev/docs)
- [Gradle документація](https://docs.gradle.org/current/userguide/userguide.html)
- [Java 11 документація](https://docs.oracle.com/en/java/javase/11/)
