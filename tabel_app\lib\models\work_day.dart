import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Типи робочих днів
enum DayType {
  /// Звичайний робочий день
  regular,

  /// Вихідний день (субота)
  saturday,

  /// Вихідний день (неділя)
  sunday,

  /// Святковий день
  holiday,
}

/// Типи змін
enum ShiftType {
  /// Денна зміна
  day,

  /// Нічна зміна
  night,
}

/// Модель робочого дня
class WorkDay extends Equatable {
  /// Унікальний ідентифікатор
  final String id;

  /// Ідентифікатор працівника
  final String employeeId;

  /// Дата робочого дня
  final DateTime date;

  /// Кількість відпрацьованих годин
  final double hoursWorked;

  /// Тип дня (звичайний, вихідний, святковий)
  final DayType dayType;

  /// Тип зміни (денна, нічна)
  final ShiftType shiftType;

  /// Коментар до робочого дня
  final String? comment;

  /// Ідентифікатор зміни
  final String? shiftId;

  /// Конструктор
  WorkDay({
    String? id,
    required this.employeeId,
    required this.date,
    required this.hoursWorked,
    required this.dayType,
    required this.shiftType,
    this.comment,
    this.shiftId,
  }) : id = id ?? const Uuid().v4();

  /// Створення копії об'єкта з можливістю зміни окремих полів
  WorkDay copyWith({
    String? employeeId,
    DateTime? date,
    double? hoursWorked,
    DayType? dayType,
    ShiftType? shiftType,
    String? comment,
    String? shiftId,
  }) {
    return WorkDay(
      id: id,
      employeeId: employeeId ?? this.employeeId,
      date: date ?? this.date,
      hoursWorked: hoursWorked ?? this.hoursWorked,
      dayType: dayType ?? this.dayType,
      shiftType: shiftType ?? this.shiftType,
      comment: comment ?? this.comment,
      shiftId: shiftId ?? this.shiftId,
    );
  }

  /// Перетворення об'єкта в Map для збереження в базі даних
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'date': date.millisecondsSinceEpoch,
      'hoursWorked': hoursWorked,
      'dayType': dayType.index,
      'shiftType': shiftType.index,
      'comment': comment,
      'shiftId': shiftId,
    };
  }

  /// Створення об'єкта з Map (з бази даних)
  factory WorkDay.fromMap(Map<String, dynamic> map) {
    return WorkDay(
      id: map['id'],
      employeeId: map['employeeId'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      hoursWorked: map['hoursWorked'],
      dayType: DayType.values[map['dayType']],
      shiftType: ShiftType.values[map['shiftType']],
      comment: map['comment'],
      shiftId: map['shiftId'],
    );
  }

  @override
  List<Object?> get props => [
    id,
    employeeId,
    date,
    hoursWorked,
    dayType,
    shiftType,
    comment,
    shiftId,
  ];
}
