import 'dart:io';

void main() {
  final file = File('lib/utils/localization.dart');
  final content = file.readAsStringSync();
  
  // Знаходимо початок і кінець _localizedValues
  final startIndex = content.indexOf('_localizedValues = {');
  final endIndex = content.indexOf('};', startIndex);
  
  if (startIndex == -1 || endIndex == -1) {
    print('Не вдалося знайти _localizedValues у файлі');
    return;
  }
  
  // Отримуємо вміст _localizedValues
  final localizedValues = content.substring(startIndex, endIndex + 2);
  
  // Знаходимо всі мови
  final languages = ['uk', 'en', 'cs', 'sk', 'pl'];
  
  // Знаходимо всі ключі для української мови (вважаємо її базовою)
  final ukKeys = extractKeys(localizedValues, 'uk');
  
  // Перевіряємо наявність всіх ключів у всіх мовах
  for (final lang in languages) {
    if (lang == 'uk') continue; // Пропускаємо українську мову, оскільки вона базова
    
    final langKeys = extractKeys(localizedValues, lang);
    
    // Знаходимо ключі, які є в українській мові, але відсутні в поточній мові
    final missingKeys = ukKeys.where((key) => !langKeys.contains(key)).toList();
    
    if (missingKeys.isNotEmpty) {
      print('Відсутні ключі для мови $lang:');
      for (final key in missingKeys) {
        print('  $key');
      }
    } else {
      print('Всі ключі присутні для мови $lang');
    }
  }
}

List<String> extractKeys(String content, String lang) {
  // Знаходимо початок і кінець секції для мови
  final langStart = content.indexOf("'$lang': {");
  if (langStart == -1) return [];
  
  // Знаходимо кінець секції для мови (наступна мова або кінець _localizedValues)
  int langEnd = content.indexOf("',", langStart);
  for (int i = 0; i < 1000; i++) { // Обмеження на кількість ітерацій
    final nextQuote = content.indexOf("'", langEnd + 2);
    if (nextQuote == -1) break;
    
    final nextChar = content.substring(nextQuote + 1, nextQuote + 2);
    if (nextChar == ':') {
      langEnd = content.lastIndexOf('}', nextQuote);
      break;
    }
    
    langEnd = nextQuote;
  }
  
  if (langEnd == -1) return [];
  
  final langContent = content.substring(langStart, langEnd);
  
  // Знаходимо всі ключі
  final keyRegex = RegExp(r"'([^']+)':");
  final matches = keyRegex.allMatches(langContent);
  
  return matches.map((match) => match.group(1)!).toList();
}
