import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';
import '../main.dart';
import 'sync_settings_screen.dart';
import 'database_repair_screen.dart';

/// Вкладка налаштувань
class SettingsTab extends StatefulWidget {
  const SettingsTab({super.key});

  @override
  State<SettingsTab> createState() => _SettingsTabState();
}

class _SettingsTabState extends State<SettingsTab> {
  // Сервіси
  late DatabaseService _databaseService;
  late SyncService _syncService;

  // Налаштування надбавок
  BonusSettings? _bonusSettings;

  // Контролери для полів надбавок
  final TextEditingController _nightShiftBonusController =
      TextEditingController();
  final TextEditingController _saturdayBonusController =
      TextEditingController();
  final TextEditingController _sundayBonusController = TextEditingController();
  final TextEditingController _holidayBonusController = TextEditingController();

  // Стан
  bool _isLoading = true;
  bool _isSyncing = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Ініціалізація сервісів
    _databaseService = Provider.of<DatabaseService>(context, listen: false);
    _syncService = Provider.of<SyncService>(context, listen: false);

    // Завантаження налаштувань
    _loadSettings();
  }

  @override
  void dispose() {
    _nightShiftBonusController.dispose();
    _saturdayBonusController.dispose();
    _sundayBonusController.dispose();
    _holidayBonusController.dispose();
    super.dispose();
  }

  /// Завантаження налаштувань
  Future<void> _loadSettings() async {
    final localizations = AppLocalizations.of(context);
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Отримання налаштувань надбавок
      final bonusSettings = await _databaseService.getBonusSettings();

      // Заповнення полів
      _nightShiftBonusController.text =
          (bonusSettings.nightShiftBonus * 100).toString();
      _saturdayBonusController.text =
          (bonusSettings.saturdayBonus * 100).toString();
      _sundayBonusController.text =
          (bonusSettings.sundayBonus * 100).toString();
      _holidayBonusController.text =
          (bonusSettings.holidayBonus * 100).toString();

      setState(() {
        _bonusSettings = bonusSettings;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '${localizations.translate('error')}: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Збереження налаштувань надбавок
  Future<void> _saveBonusSettings() async {
    final localizations = AppLocalizations.of(context);
    // Валідація полів
    if (_nightShiftBonusController.text.isEmpty ||
        _saturdayBonusController.text.isEmpty ||
        _sundayBonusController.text.isEmpty ||
        _holidayBonusController.text.isEmpty) {
      setState(() {
        _errorMessage = localizations.translate('required_field');
      });
      return;
    }

    // Парсинг значень
    final nightShiftBonus = double.tryParse(_nightShiftBonusController.text);
    final saturdayBonus = double.tryParse(_saturdayBonusController.text);
    final sundayBonus = double.tryParse(_sundayBonusController.text);
    final holidayBonus = double.tryParse(_holidayBonusController.text);

    if (nightShiftBonus == null ||
        saturdayBonus == null ||
        sundayBonus == null ||
        holidayBonus == null) {
      setState(() {
        _errorMessage = localizations.translate('invalid_hourly_rate');
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Створення нових налаштувань
      final newSettings = BonusSettings(
        nightShiftBonus: nightShiftBonus / 100,
        saturdayBonus: saturdayBonus / 100,
        sundayBonus: sundayBonus / 100,
        holidayBonus: holidayBonus / 100,
      );

      // Збереження налаштувань
      await _databaseService.updateBonusSettings(newSettings);

      setState(() {
        _bonusSettings = newSettings;
        _successMessage = localizations.translate('success');
      });
    } catch (e) {
      setState(() {
        _errorMessage = '${localizations.translate('error')}: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Синхронізація даних
  Future<void> _syncData() async {
    final localizations = AppLocalizations.of(context);
    setState(() {
      _isSyncing = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Перевірка підключення до інтернету
      final isConnected = await _syncService.isConnected();
      if (!isConnected) {
        setState(() {
          _errorMessage = localizations.translate('no_internet');
        });
        return;
      }

      // Синхронізація даних
      await _syncService.syncData();

      setState(() {
        _successMessage = localizations.translate('sync_success');
      });
    } catch (e) {
      setState(() {
        _errorMessage =
            '${localizations.translate('sync_error')}: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Заголовок
                    Text(
                      localizations.translate('settings'),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Налаштування мови
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('language'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Список мов
                            Wrap(
                              spacing: 8,
                              children:
                                  AppLanguage.values.map((language) {
                                    return ChoiceChip(
                                      label: Text(language.name),
                                      selected: appState.language == language,
                                      onSelected: (selected) {
                                        if (selected) {
                                          appState.setLanguage(language);
                                        }
                                      },
                                    );
                                  }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Налаштування теми
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('theme'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Перемикач теми
                            SwitchListTile(
                              title: Text(localizations.translate('dark')),
                              value: appState.isDarkMode,
                              onChanged: (value) {
                                appState.setThemeMode(value);
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Налаштування країни
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('country'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Список країн
                            Wrap(
                              spacing: 8,
                              children:
                                  AppCountry.values.map((country) {
                                    return ChoiceChip(
                                      label: Text(
                                        country.getName(appState.language.code),
                                      ),
                                      selected: appState.country == country,
                                      onSelected: (selected) {
                                        if (selected) {
                                          appState.setCountry(country);
                                        }
                                      },
                                    );
                                  }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Налаштування надбавок
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('bonus_settings'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Надбавка за нічну зміну
                            TextField(
                              controller: _nightShiftBonusController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                labelText: localizations.translate(
                                  'night_shift_bonus',
                                ),
                                suffixText: '%',
                                border: const OutlineInputBorder(),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Надбавка за роботу в суботу
                            TextField(
                              controller: _saturdayBonusController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                labelText: localizations.translate(
                                  'saturday_bonus',
                                ),
                                suffixText: '%',
                                border: const OutlineInputBorder(),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Надбавка за роботу в неділю
                            TextField(
                              controller: _sundayBonusController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                labelText: localizations.translate(
                                  'sunday_bonus',
                                ),
                                suffixText: '%',
                                border: const OutlineInputBorder(),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Надбавка за роботу в святковий день
                            TextField(
                              controller: _holidayBonusController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                labelText: localizations.translate(
                                  'holiday_bonus',
                                ),
                                suffixText: '%',
                                border: const OutlineInputBorder(),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Кнопка збереження
                            Center(
                              child: ElevatedButton.icon(
                                onPressed: _saveBonusSettings,
                                icon: const Icon(Icons.save),
                                label: Text(localizations.translate('save')),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Синхронізація
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('sync'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Кнопка синхронізації
                            Center(
                              child: ElevatedButton.icon(
                                onPressed: _isSyncing ? null : _syncData,
                                icon:
                                    _isSyncing
                                        ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ),
                                        )
                                        : const Icon(Icons.sync),
                                label: Text(localizations.translate('sync')),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Кнопка налаштувань синхронізації
                            ListTile(
                              leading: const Icon(Icons.settings),
                              title: Text(
                                localizations.translate('sync_settings'),
                              ),
                              trailing: const Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                              ),
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const SyncSettingsScreen(),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Нагадування
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.translate('reminders'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Кнопка налаштувань нагадувань
                            ListTile(
                              leading: const Icon(Icons.notifications_active),
                              title: Text(
                                localizations.translate('reminder_settings'),
                              ),
                              trailing: const Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                              ),
                              onTap: () {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Coming soon...'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Діагностика бази даних
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Діагностика бази даних',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Кнопка діагностики
                            ListTile(
                              leading: const Icon(Icons.healing, color: Colors.orange),
                              title: const Text('Перевірити базу даних'),
                              subtitle: const Text('Діагностика та відновлення даних'),
                              trailing: const Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                              ),
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const DatabaseRepairScreen(),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Повідомлення про помилку
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),

                    // Повідомлення про успіх
                    if (_successMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Text(
                          _successMessage!,
                          style: const TextStyle(color: Colors.green),
                        ),
                      ),
                  ],
                ),
              ),
    );
  }
}
