{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-fe326f788af1f0c7e639.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-a47b3682ef6855b5f9d0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0b560d7a8be497144153.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-a47b3682ef6855b5f9d0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-0b560d7a8be497144153.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-fe326f788af1f0c7e639.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}