group "net.nfet.flutter.printing"
version "1.0"

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "com.android.tools.build:gradle:7.3.0"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: "com.android.library"

android {
    if (project.android.hasProperty("namespace")) {
        namespace = "net.nfet.flutter.printing"
    }

    compileSdk = 34

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdk = 21
    }

    lintOptions {
        disable "InvalidPackage"
    }
}
