# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

default_platform(:ios)

platform :ios do
  desc "Build and sign the app for development distribution"
  lane :build_development do
    # Оновлення сертифікатів та профілів
    match(type: "development", readonly: true)
    
    # Збірка Flutter додатку
    sh("cd .. && cd .. && flutter build ios --release --no-codesign")
    
    # Збірка та підписання iOS додатку
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      configuration: "Release",
      export_method: "development",
      output_directory: "./build/ios/ipa",
      include_bitcode: false,
      include_symbols: true,
      export_options: {
        provisioningProfiles: {
          "com.yourcompany.tabelapp" => "Tabel App Development Profile"
        }
      }
    )
  end
  
  desc "Build and sign the app for App Store distribution"
  lane :build_appstore do
    # Оновлення сертифікатів та профілів
    match(type: "appstore", readonly: true)
    
    # Збірка Flutter додатку
    sh("cd .. && cd .. && flutter build ios --release --no-codesign")
    
    # Збірка та підписання iOS додатку
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      configuration: "Release",
      export_method: "app-store",
      output_directory: "./build/ios/ipa",
      include_bitcode: false,
      include_symbols: true
    )
    
    # Завантаження в TestFlight
    upload_to_testflight(
      skip_waiting_for_build_processing: true
    )
  end
  
  desc "Build and sign the app for Ad Hoc distribution"
  lane :build_adhoc do
    # Оновлення сертифікатів та профілів
    match(type: "adhoc", readonly: true)
    
    # Збірка Flutter додатку
    sh("cd .. && cd .. && flutter build ios --release --no-codesign")
    
    # Збірка та підписання iOS додатку
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      configuration: "Release",
      export_method: "ad-hoc",
      output_directory: "./build/ios/ipa",
      include_bitcode: false,
      include_symbols: true
    )
  end
end
