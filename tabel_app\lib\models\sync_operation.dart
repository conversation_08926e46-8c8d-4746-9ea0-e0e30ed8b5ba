import 'dart:convert';

/// Типи операцій синхронізації
enum SyncOperationType {
  create,
  update,
  delete,
}

/// Клас для представлення операції синхронізації
class SyncOperation {
  /// Тип операції
  final SyncOperationType type;
  
  /// Тип моделі
  final String modelType;
  
  /// Ідентифікатор моделі
  final String modelId;
  
  /// Дані моделі
  final Map<String, dynamic> data;
  
  /// Часова мітка операції
  final DateTime timestamp;
  
  /// Прапорець, що вказує, чи була операція оброблена
  final bool processed;

  SyncOperation({
    required this.type,
    required this.modelType,
    required this.modelId,
    required this.data,
    DateTime? timestamp,
    this.processed = false,
  }) : timestamp = timestamp ?? DateTime.now();
  
  /// Перетворення в JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'modelType': modelType,
      'modelId': modelId,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'processed': processed,
    };
  }
  
  /// Створення з JSON
  factory SyncOperation.fromJson(Map<String, dynamic> json) {
    return SyncOperation(
      type: SyncOperationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      modelType: json['modelType'],
      modelId: json['modelId'],
      data: json['data'],
      timestamp: DateTime.parse(json['timestamp']),
      processed: json['processed'] ?? false,
    );
  }
  
  /// Серіалізація в рядок
  String toJsonString() => jsonEncode(toJson());
  
  /// Десеріалізація з рядка
  static SyncOperation fromJsonString(String jsonString) {
    return SyncOperation.fromJson(jsonDecode(jsonString));
  }
  
  /// Створення копії з оновленими полями
  SyncOperation copyWith({
    SyncOperationType? type,
    String? modelType,
    String? modelId,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    bool? processed,
  }) {
    return SyncOperation(
      type: type ?? this.type,
      modelType: modelType ?? this.modelType,
      modelId: modelId ?? this.modelId,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      processed: processed ?? this.processed,
    );
  }
}
