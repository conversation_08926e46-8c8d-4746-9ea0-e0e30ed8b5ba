import 'package:equatable/equatable.dart';

/// Модель налаштувань додатку
class AppSettings extends Equatable {
  /// Код мови
  final String languageCode;

  /// Темна тема
  final bool isDarkMode;

  /// Код країни
  final String countryCode;

  /// Конструктор
  const AppSettings({
    this.languageCode = 'uk',
    this.isDarkMode = false,
    this.countryCode = 'UA',
  });

  /// Створення копії об'єкта з можливістю зміни окремих полів
  AppSettings copyWith({
    String? languageCode,
    bool? isDarkMode,
    String? countryCode,
  }) {
    return AppSettings(
      languageCode: languageCode ?? this.languageCode,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      countryCode: countryCode ?? this.countryCode,
    );
  }

  /// Перетворення об'єкта в Map для збереження в SharedPreferences
  Map<String, dynamic> toMap() {
    return {
      'languageCode': languageCode,
      'isDarkMode': isDarkMode,
      'countryCode': countryCode,
    };
  }

  /// Створення об'єкта з Map (з SharedPreferences)
  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      languageCode: map['languageCode'] ?? 'uk',
      isDarkMode: map['isDarkMode'] ?? false,
      countryCode: map['countryCode'] ?? 'UA',
    );
  }

  @override
  List<Object?> get props => [languageCode, isDarkMode, countryCode];
}

/// Перелік доступних країн
enum AppCountry {
  ukraine('UA', 'Україна', 'Ukraine', 'грн', 'UAH'),
  czechia('CZ', 'Чехія', 'Czechia', 'Kč', 'CZK'),
  slovakia('SK', 'Словаччина', 'Slovakia', '€', 'EUR'),
  poland('PL', 'Польща', 'Poland', 'zł', 'PLN');

  /// Код країни
  final String code;

  /// Назва країни українською
  final String nameUk;

  /// Назва країни англійською
  final String nameEn;

  /// Символ валюти
  final String currencySymbol;

  /// Код валюти
  final String currencyCode;

  /// Конструктор
  const AppCountry(
    this.code,
    this.nameUk,
    this.nameEn,
    this.currencySymbol,
    this.currencyCode,
  );

  /// Отримання назви країни залежно від мови
  String getName(String languageCode) {
    switch (languageCode) {
      case 'uk':
        return nameUk;
      case 'en':
      default:
        return nameEn;
    }
  }

  /// Отримання країни за кодом
  static AppCountry fromCode(String code) {
    return AppCountry.values.firstWhere(
      (country) => country.code == code,
      orElse: () => AppCountry.ukraine,
    );
  }

  /// Отримання символу валюти
  String getCurrencySymbol() {
    return currencySymbol;
  }
}
